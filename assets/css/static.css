/* Button Block Base Style
=================================================*/
.sqs-block-button-container {
  text-align: center;
}
.sqs-block-button.sqs-stretched .sqs-block-content,
.sqs-block-button.sqs-stretched .sqs-block-button-element {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  height: 100%;
  display: flex;
}
.sqs-block-button.sqs-stretched .sqs-block-button-container {
  flex: 1;
}
.sqs-block-button.sqs-stretched .sqs-block-button-element {
  align-items: center;
  box-sizing: border-box;
  justify-content: center;
}
.sqs-block-button:not(.sqs-stretched) .sqs-block-button-container {
  display: flex;
}
.sqs-block-button:not(.sqs-stretched) .sqs-block-button-container--left {
  justify-content: flex-start;
}
.sqs-block-button:not(.sqs-stretched) .sqs-block-button-container--center {
  justify-content: center;
}
.sqs-block-button:not(.sqs-stretched) .sqs-block-button-container--right {
  justify-content: flex-end;
}
.sqs-block-button-element,
.image-button a,
.list-item-content__button {
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  line-height: normal;
  padding: var(--primaryButtonPadding) calc(var(--primaryButtonPadding) * 1.67);
}
@media (hover: hover) {
  .sqs-block-button-element:hover,
  .image-button a:hover,
  .list-item-content__button:hover {
    opacity: 1;
  }
}
.sqs-button-element--primary[disabled],
.sqs-button-element--secondary[disabled],
.sqs-button-element--tertiary[disabled] {
  pointer-events: none !important;
  opacity: 0.8 !important;
}
/* Universal Button Variant Styles
=================================================*/
#siteWrapper.site-wrapper .sqs-button-element--primary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--primary {
  padding: var(--primary-button-padding-y) var(--primary-button-padding-x);
}
#siteWrapper.site-wrapper .sqs-button-element--primary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--primary,
#siteWrapper.site-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary,
body .sqs-block-form-lightbox .form-button-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary {
  border-width: var(--primary-button-stroke);
}
#siteWrapper.site-wrapper .sqs-button-element--secondary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--secondary {
  padding: var(--secondary-button-padding-y) var(--secondary-button-padding-x);
}
#siteWrapper.site-wrapper .sqs-button-element--secondary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--secondary,
#siteWrapper.site-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary,
body .sqs-block-form-lightbox .form-button-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-width: var(--secondary-button-stroke);
}
#siteWrapper.site-wrapper .sqs-button-element--tertiary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--tertiary {
  padding: var(--tertiary-button-padding-y) var(--tertiary-button-padding-x);
}
#siteWrapper.site-wrapper .sqs-button-element--tertiary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--tertiary,
#siteWrapper.site-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary,
body .sqs-block-form-lightbox .form-button-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-width: var(--tertiary-button-stroke);
}
#siteWrapper.site-wrapper .sqs-button-element--primary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--primary,
#siteWrapper.site-wrapper .sqs-button-element--secondary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--secondary,
#siteWrapper.site-wrapper .sqs-button-element--tertiary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--tertiary,
#siteWrapper.site-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary,
body .sqs-block-form-lightbox .form-button-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary,
#siteWrapper.site-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary,
body .sqs-block-form-lightbox .form-button-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary,
#siteWrapper.site-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary,
body .sqs-block-form-lightbox .form-button-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  line-height: normal;
  border-style: solid;
}
/* Specific Button Style Overrides
=================================================*/
#siteWrapper.site-wrapper div#Tock_widget_container > div.TockWidgetWrapper .TockButton {
  height: auto;
  line-height: 0;
}
#siteWrapper.site-wrapper div#Tock_widget_container > div.TockWidgetWrapper .TockButtonWidgetContainer,
#siteWrapper.site-wrapper div#Tock_widget_container > div.TockWidgetWrapper .TockButton-buttonContainer,
#siteWrapper.site-wrapper div#Tock_widget_container > div.TockWidgetWrapper .TockInlineButton-container {
  width: auto;
}
#siteWrapper.site-wrapper div#Tock_widget_container > div.TockWidgetWrapper .TockSearchBar-container {
  align-items: center;
}
#siteWrapper.site-wrapper div#Tock_widget_container > div.TockWidgetWrapper .TockInlineButton-container {
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 49px;
  min-width: 180px;
  padding: 0px;
  border-radius: 0 3px 3px 0;
}
#siteWrapper.site-wrapper div#Tock_widget_container.Tock_widget_container-columnLayout > div.TockWidgetWrapper .TockInlineButton-container {
  border-radius: 3px;
}
.sqs-modal-lightbox .sqs-modal-lightbox-content .sqs-button-element--primary {
  border-width: var(--primary-button-stroke);
  border-style: solid;
}
.sqs-modal-lightbox .sqs-modal-lightbox-content .sqs-button-element--secondary {
  border-width: var(--secondary-button-stroke);
  border-style: solid;
}
.sqs-modal-lightbox .sqs-modal-lightbox-content .sqs-button-element--tertiary {
  border-width: var(--tertiary-button-stroke);
  border-style: solid;
}
/* Button Block Style Tweak: Solid
=================================================*/
body.primary-button-style-solid .sqs-button-element--primary,
body.primary-button-style-solid .sqs-editable-button.sqs-button-element--primary {
  transition: 0.1s opacity linear;
  -webkit-backface-visibility: hidden;
}
@media (hover: hover) {
  body.primary-button-style-solid .sqs-button-element--primary:hover,
  body.primary-button-style-solid .sqs-editable-button.sqs-button-element--primary:hover {
    opacity: 0.8;
  }
}
/* Button Block Style Tweak: Outline
=================================================*/
.primary-button-style-outline .sqs-button-element--primary,
.primary-button-style-outline .list-item .sqs-block-button-element.list-item-content__button.sqs-button-element--primary,
.primary-button-style-outline .list-item[data-is-card-enabled="true"] .sqs-block-button-element.list-item-content__button.sqs-button-element--primary,
.primary-button-style-outline .sqs-block-form .sqs-editable-button.sqs-button-element--primary,
.primary-button-style-outline .header-menu-cta .btn.sqs-button-element--primary,
.primary-button-style-outline .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary,
.primary-button-style-outline .sqs-modal-lightbox-content .sqs-button-element--primary {
  transition: 0.1s background-color linear, 0.1s color linear;
}
.primary-button-style-outline .sqs-button-element--primary:not(:hover),
.primary-button-style-outline .list-item .sqs-block-button-element.list-item-content__button.sqs-button-element--primary:not(:hover),
.primary-button-style-outline .list-item[data-is-card-enabled="true"] .sqs-block-button-element.list-item-content__button.sqs-button-element--primary:not(:hover),
.primary-button-style-outline .sqs-block-form .sqs-editable-button.sqs-button-element--primary:not(:hover),
.primary-button-style-outline .header-menu-cta .btn.sqs-button-element--primary:not(:hover),
.primary-button-style-outline .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary:not(:hover),
.primary-button-style-outline .sqs-modal-lightbox-content .sqs-button-element--primary:not(:hover) {
  background: transparent;
}
.primary-button-style-outline .newsletter-form-button.sqs-system-button.sqs-button-element--primary:not(:hover) {
  background: transparent !important;
}
/* Primary Button Shape Tweak: Square
=================================================*/
.primary-button-shape-square .sqs-button-element--primary,
.primary-button-shape-square #Tock_widget_container .sqs-button-element--primary.TockButton-blue {
  border-radius: 0;
}
/* Primary Button Shape Tweak: Rounded
=================================================*/
.primary-button-shape-rounded .sqs-button-element--primary,
.primary-button-shape-rounded #Tock_widget_container .sqs-button-element--primary.TockButton-blue {
  border-radius: 0.4rem;
}
/* Primary Button Shape Tweak: Pill
=================================================*/
.primary-button-shape-pill .sqs-button-element--primary,
.primary-button-shape-pill #Tock_widget_container .sqs-button-element--primary.TockButton-blue {
  border-radius: 300px;
}
/* Primary Button Shape Tweak: Oval
=================================================*/
.primary-button-shape-oval .sqs-button-element--primary,
.primary-button-shape-oval #Tock_widget_container .sqs-button-element--primary.TockButton-blue {
  border-radius: 100%;
}
/* Primary Button Shape Tweak: Petal
=================================================*/
.primary-button-shape-petal .sqs-button-element--primary,
.primary-button-shape-petal #Tock_widget_container .sqs-button-element--primary.TockButton-blue {
  border-radius: 16px 0px;
}
/* Button Block Shape Tweak: Underline
=================================================*/
body.primary-button-shape-underline #siteWrapper .sqs-button-element--primary:not(.ma-pricing-option-button),
body.primary-button-shape-underline .sqs-block-form-lightbox .sqs-button-element--primary:not(.ma-pricing-option-button),
body.primary-button-shape-underline #siteWrapper .comment-btn-wrapper .comment-btn.sqs-button-element--primary,
body.primary-button-shape-underline .sqs-block-form-lightbox .comment-btn-wrapper .comment-btn.sqs-button-element--primary,
body.primary-button-shape-underline #siteWrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary,
body.primary-button-shape-underline .sqs-block-form-lightbox .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary {
  border-top: none;
  border-right: none;
  border-left: none;
  border-bottom-style: solid;
  border-radius: 0;
  border-bottom-width: var(--primary-button-stroke);
}
/* Primary Button Shape Tweak: Custom
=================================================*/
.primary-button-shape-custom .sqs-button-element--primary,
.primary-button-shape-custom .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary {
  border-top-left-radius: var(--primary-button-rounded-border-top-left-radius);
  border-top-right-radius: var(--primary-button-rounded-border-top-right-radius);
  border-bottom-left-radius: var(--primary-button-rounded-border-bottom-left-radius);
  border-bottom-right-radius: var(--primary-button-rounded-border-bottom-right-radius);
}
.secondary-button-style-solid .sqs-button-element--secondary,
.secondary-button-style-solid .sqs-editable-button.sqs-button-element--secondary,
.secondary-button-style-solid .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  transition: 0.1s opacity linear;
  -webkit-backface-visibility: hidden;
}
.secondary-button-style-solid .sqs-button-element--secondary:hover,
.secondary-button-style-solid .sqs-editable-button.sqs-button-element--secondary:hover,
.secondary-button-style-solid .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary:hover {
  opacity: 0.8;
}
.secondary-button-style-outline .sqs-button-element--secondary,
.secondary-button-style-outline .list-item .sqs-block-button-element.list-item-content__button.sqs-button-element--secondary,
.secondary-button-style-outline .list-item[data-is-card-enabled="true"] .sqs-block-button-element.list-item-content__button.sqs-button-element--secondary,
.secondary-button-style-outline .sqs-block-form .sqs-editable-button.sqs-button-element--secondary,
.secondary-button-style-outline .header-menu-cta .btn.sqs-button-element--secondary,
.secondary-button-style-outline .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary,
.secondary-button-style-outline .sqs-modal-lightbox-content .sqs-button-element--secondary {
  transition: 0.1s background-color linear, 0.1s color linear;
}
.secondary-button-style-outline .sqs-button-element--secondary:not(:hover),
.secondary-button-style-outline .list-item .sqs-block-button-element.list-item-content__button.sqs-button-element--secondary:not(:hover),
.secondary-button-style-outline .list-item[data-is-card-enabled="true"] .sqs-block-button-element.list-item-content__button.sqs-button-element--secondary:not(:hover),
.secondary-button-style-outline .sqs-block-form .sqs-editable-button.sqs-button-element--secondary:not(:hover),
.secondary-button-style-outline .header-menu-cta .btn.sqs-button-element--secondary:not(:hover),
.secondary-button-style-outline .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary:not(:hover),
.secondary-button-style-outline .sqs-modal-lightbox-content .sqs-button-element--secondary:not(:hover) {
  background: transparent;
}
.secondary-button-style-outline .newsletter-form-button.sqs-system-button.sqs-button-element--secondary {
  background: transparent !important;
}
/* Secondary Button Shape Tweak: Square
=================================================*/
.secondary-button-shape-square .sqs-button-element--secondary,
.secondary-button-shape-square .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-radius: 0;
}
/* Secondary Button Shape Tweak: Rounded
=================================================*/
.secondary-button-shape-rounded .sqs-button-element--secondary,
.secondary-button-shape-rounded .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-radius: 0.4rem;
}
/* Secondary Button Shape Tweak: Pill
=================================================*/
.secondary-button-shape-pill .sqs-button-element--secondary,
.secondary-button-shape-pill .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-radius: 300px;
}
/* Secondary Button Shape Tweak: Oval
=================================================*/
.secondary-button-shape-oval .sqs-button-element--secondary,
.secondary-button-shape-oval .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-radius: 100%;
}
/* Secondary Button Shape Tweak: Underline
=================================================*/
body.secondary-button-shape-underline #siteWrapper .sqs-button-element--secondary,
body.secondary-button-shape-underline .sqs-block-form-lightbox .sqs-button-element--secondary,
body.secondary-button-shape-underline #siteWrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary,
body.secondary-button-shape-underline .sqs-block-form-lightbox .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-top: none;
  border-right: none;
  border-left: none;
  border-bottom-style: solid;
  border-radius: 0;
  border-bottom-width: var(--secondary-button-stroke);
}
/* Secondary Button Shape Tweak: Petal
=================================================*/
.secondary-button-shape-petal .sqs-button-element--secondary,
.secondary-button-shape-petal .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-radius: 16px 0px;
}
/* Secondary Button Shape Tweak: Custom
=================================================*/
.secondary-button-shape-custom .sqs-button-element--secondary,
.secondary-button-shape-custom .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-top-left-radius: var(--secondary-button-rounded-border-top-left-radius);
  border-top-right-radius: var(--secondary-button-rounded-border-top-right-radius);
  border-bottom-left-radius: var(--secondary-button-rounded-border-bottom-left-radius);
  border-bottom-right-radius: var(--secondary-button-rounded-border-bottom-right-radius);
}
.tertiary-button-style-solid .sqs-button-element--tertiary,
.tertiary-button-style-solid .sqs-editable-button.sqs-button-element--tertiary,
.tertiary-button-style-solid .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  transition: 0.1s opacity linear;
  -webkit-backface-visibility: hidden;
}
.tertiary-button-style-solid .sqs-button-element--tertiary:hover,
.tertiary-button-style-solid .sqs-editable-button.sqs-button-element--tertiary:hover,
.tertiary-button-style-solid .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary:hover {
  opacity: 0.8;
}
.tertiary-button-style-outline .sqs-button-element--tertiary,
.tertiary-button-style-outline .list-item .sqs-block-button-element.list-item-content__button.sqs-button-element--tertiary,
.tertiary-button-style-outline .list-item[data-is-card-enabled="true"] .sqs-block-button-element.list-item-content__button.sqs-button-element--tertiary,
.tertiary-button-style-outline .sqs-block-form .sqs-editable-button.sqs-button-element--tertiary,
.tertiary-button-style-outline .header-menu-cta .btn.sqs-button-element--tertiary,
.tertiary-button-style-outline .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary,
.tertiary-button-style-outline .sqs-modal-lightbox-content .sqs-button-element--tertiary {
  transition: 0.1s background-color linear, 0.1s color linear;
}
.tertiary-button-style-outline .sqs-button-element--tertiary:not(:hover),
.tertiary-button-style-outline .list-item .sqs-block-button-element.list-item-content__button.sqs-button-element--tertiary:not(:hover),
.tertiary-button-style-outline .list-item[data-is-card-enabled="true"] .sqs-block-button-element.list-item-content__button.sqs-button-element--tertiary:not(:hover),
.tertiary-button-style-outline .sqs-block-form .sqs-editable-button.sqs-button-element--tertiary:not(:hover),
.tertiary-button-style-outline .header-menu-cta .btn.sqs-button-element--tertiary:not(:hover),
.tertiary-button-style-outline .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary:not(:hover),
.tertiary-button-style-outline .sqs-modal-lightbox-content .sqs-button-element--tertiary:not(:hover) {
  background: transparent;
}
.tertiary-button-style-outline .newsletter-form-button.sqs-system-button.sqs-button-element--tertiary {
  background: transparent !important;
}
/* Tertiary Button Shape Tweak: Square
=================================================*/
.tertiary-button-shape-square .sqs-button-element--tertiary,
.tertiary-button-shape-square .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-radius: 0;
}
/* Tertiary Button Shape Tweak: Rounded
=================================================*/
.tertiary-button-shape-rounded .sqs-button-element--tertiary,
.tertiary-button-shape-rounded .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-radius: 0.4rem;
}
/* Tertiary Button Shape Tweak: Pill
=================================================*/
.tertiary-button-shape-pill .sqs-button-element--tertiary,
.tertiary-button-shape-pill .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-radius: 300px;
}
/* Tertiary Button Shape Tweak: Oval
=================================================*/
.tertiary-button-shape-oval .sqs-button-element--tertiary,
.tertiary-button-shape-oval .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-radius: 100%;
}
/* Tertiry Button Shape Tweak: Underline
=================x================================*/
body.tertiary-button-shape-underline #siteWrapper .sqs-button-element--tertiary,
body.tertiary-button-shape-underline .sqs-block-form-lightbox .sqs-button-element--tertiary,
body.tertiary-button-shape-underline #siteWrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary,
body.tertiary-button-shape-underline .sqs-block-form-lightbox .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-top: none;
  border-right: none;
  border-left: none;
  border-bottom-style: solid;
  border-radius: 0;
  border-bottom-width: var(--tertiary-button-stroke);
}
/* Tertiary Button Shape Tweak: Petal
=================================================*/
.tertiary-button-shape-petal .sqs-button-element--tertiary,
.tertiary-button-shape-petal .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-radius: 16px 0px;
}
/* Tertiary Button Shape Tweak: Custom
=================================================*/
.tertiary-button-shape-custom .sqs-button-element--tertiary,
.tertiary-button-shape-custom .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-top-left-radius: var(--tertiary-button-rounded-border-top-left-radius);
  border-top-right-radius: var(--tertiary-button-rounded-border-top-right-radius);
  border-bottom-left-radius: var(--tertiary-button-rounded-border-bottom-left-radius);
  border-bottom-right-radius: var(--tertiary-button-rounded-border-bottom-right-radius);
}
@media (hover: hover) {
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .image-button a:hover,
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .sqs-add-to-cart-button:hover,
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .sqs-editable-button:not(input):hover,
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .sqs-block-button-element.sqs-block-button-element--primary:hover,
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .ma-pricing-toggle-wrapper .ma-pricing-option-button:hover,
  .tweak-global-animations-animation-type-flex.primary-button-style-outline [data-animation-role="header-element"] .btn:hover {
    background-color: transparent !important;
  }
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .user-items-list-simple {
    background: transparent;
  }
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .user-items-list-simple:hover {
    background-color: transparent;
  }
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .user-items-list-carousel {
    background: transparent;
  }
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .user-items-list-carousel:hover {
    background-color: transparent;
  }
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .user-items-list-banner-slideshow {
    background: transparent;
  }
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .user-items-list-banner-slideshow:hover {
    background-color: transparent;
  }
}
@media (hover: hover) {
  .tweak-global-animations-animation-type-flex.secondary-button-style-outline .sqs-button-element--secondary:hover,
  .tweak-global-animations-animation-type-flex.tertiary-button-style-outline .sqs-button-element--tertiary:hover {
    background: transparent;
  }
}
.sqs-announcement-bar .sqs-announcement-bar-close {
  background: transparent;
}
div#Tock_widget_container > div.TockWidgetWrapper .InlineWidgetDropDown-NoRightBorder {
  border-right: 1px solid #e7e7e7;
}
/* Extended styling for Form block with support for tweaks
=================================================*/
body .sqs-block-form {
  --fallback-white-hsl: 0,0%,98%;
  --fallback-black-hsl: 0,0%,0%;
  --solid-alpha-modifier: 1;
  --solid-hover-alpha-modifier: 0.6;
  --outline-hover-alpha-modifier: 0.4;
  --inverted-solid-hover-alpha-modifier: var(--solid-hover-alpha-modifier);
  --inverted-outline-hover-alpha-modifier: var(--outline-hover-alpha-modifier);
  --solid-focus-alpha-modifier: 1;
  --outline-focus-alpha-modifier: 0.4;
  --fill-hover-transition: 0.15s background-color cubic-bezier(0.33, 1, 0.68, 1);
}
body.form-use-theme-colors .sqs-block-form {
  --dynamic-fill-color: var(--tweak-form-block-field-fill-color);
  --dynamic-fill-color-hsl: var(--tweak-form-block-field-fill-color-hsl);
  --dynamic-fill-color-a: var(--tweak-form-block-field-fill-color-a);
  --dynamic-border-color: var(--tweak-form-block-field-border-color);
  --dynamic-border-color-hsl: var(--tweak-form-block-field-border-color-hsl);
  --dynamic-border-color-a: var(--tweak-form-block-field-border-color-a);
  --dynamic-input-text-color: var(--tweak-form-block-field-input-color);
  --dynamic-input-text-color-hsl: var(--tweak-form-block-field-input-color-hsl);
  --dynamic-input-text-color-a: var(--tweak-form-block-field-input-color-a);
  --dynamic-accessory-color: var(--tweak-form-block-field-accessory-color);
}
body.form-use-theme-colors .sqs-block-form.sqs-background-enabled {
  --dynamic-fill-color: var(--tweak-form-block-field-fill-color-on-background);
  --dynamic-fill-color-hsl: var(--tweak-form-block-field-fill-color-on-background-hsl);
  --dynamic-fill-color-a: var(--tweak-form-block-field-fill-color-on-background-a);
  --dynamic-border-color: var(--tweak-form-block-field-border-color-on-background);
  --dynamic-border-color-hsl: var(--tweak-form-block-field-border-color-on-background-hsl);
  --dynamic-border-color-a: var(--tweak-form-block-field-border-color-on-background-a);
  --dynamic-input-text-color: var(--tweak-form-block-field-input-color-on-background);
  --dynamic-input-text-color-hsl: var(--tweak-form-block-field-input-color-on-background-hsl);
  --dynamic-input-text-color-a: var(--tweak-form-block-field-input-color-a);
  --dynamic-accessory-color: var(--tweak-form-block-field-accessory-color-on-background);
}
/* Form Field Padding */
.sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]),
.sqs-block-form .form-item textarea,
.sqs-block-form .form-item .file-display,
.sqs-block-form .form-item select {
  padding: var(--form-field-padding-vertical) var(--form-field-padding-horizontal);
}
.sqs-block-form .form-item select {
  padding-right: calc(var(--form-field-padding-horizontal) + var(--form-field-dropdown-icon-size) + 4px);
}
/* Form Field Fill Tweak: Solid
=================================================*/
.form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]),
.form-field-style-solid .sqs-block-form .form-item textarea,
.form-field-style-solid .sqs-block-form .form-item .file-display,
.form-field-style-solid .sqs-block-form .form-item select {
  background-color: hsla(var(--dynamic-fill-color-hsl, var(--fallback-white-hsl)), calc(var(--dynamic-fill-color-a, 1) * var(--solid-alpha-modifier)));
  transition: var(--fill-hover-transition);
}
@media (hover: hover) {
  .form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):hover,
  .form-field-style-solid .sqs-block-form .form-item textarea:hover,
  .form-field-style-solid .sqs-block-form .form-item .file-display:hover,
  .form-field-style-solid .sqs-block-form .form-item select:hover {
    background-color: hsla(var(--dynamic-fill-color-hsl, var(--fallback-white-hsl)), calc(var(--dynamic-fill-color-a, 1) * var(--solid-hover-alpha-modifier)));
  }
}
.form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):focus,
.form-field-style-solid .sqs-block-form .form-item textarea:focus,
.form-field-style-solid .sqs-block-form .form-item .file-display:focus,
.form-field-style-solid .sqs-block-form .form-item select:focus {
  background-color: hsla(var(--dynamic-fill-color-hsl, var(--fallback-white-hsl)), calc(var(--dynamic-fill-color-a, 1) * var(--solid-focus-alpha-modifier)));
}
/* Form Field Fill Tweak: Outline
=================================================*/
.form-field-style-outline .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]),
.form-field-style-outline .sqs-block-form .form-item textarea,
.form-field-style-outline .sqs-block-form .form-item .file-display,
.form-field-style-outline .sqs-block-form .form-item select {
  border-color: var(--dynamic-border-color, #A9A9A9);
  background-color: hsla(var(--dynamic-fill-color-hsl, var(--fallback-white-hsl)), 0);
  transition: var(--fill-hover-transition);
}
@media (hover: hover) {
  .form-field-style-outline .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):hover,
  .form-field-style-outline .sqs-block-form .form-item textarea:hover,
  .form-field-style-outline .sqs-block-form .form-item .file-display:hover,
  .form-field-style-outline .sqs-block-form .form-item select:hover {
    background-color: hsla(var(--dynamic-fill-color-hsl, var(--fallback-white-hsl)), calc(var(--dynamic-fill-color-a, 1) * var(--outline-hover-alpha-modifier)));
  }
}
.form-field-style-outline .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):focus,
.form-field-style-outline .sqs-block-form .form-item textarea:focus,
.form-field-style-outline .sqs-block-form .form-item .file-display:focus,
.form-field-style-outline .sqs-block-form .form-item select:focus {
  background-color: hsla(var(--dynamic-fill-color-hsl, var(--fallback-white-hsl)), calc(var(--dynamic-fill-color-a, 1) * var(--outline-focus-alpha-modifier)));
}
/* Form Field Shape Tweak: Square
=================================================*/
.form-field-shape-square {
  /* Form Field Border Tweak: None
  =================================================*/
  /* Form Field Border Tweak: All
  =================================================*/
  /* Form Field Border Tweak: Bottom
  =================================================*/
}
.form-field-shape-square.form-field-border-none .sqs-block-form .form-item input,
.form-field-shape-square.form-field-border-none .sqs-block-form .form-item textarea,
.form-field-shape-square.form-field-border-none .sqs-block-form .form-item .file-display,
.form-field-shape-square.form-field-border-none .sqs-block-form .form-item select {
  border: 1px solid transparent;
  box-shadow: 0 0 0 1px transparent;
}
.form-field-shape-square.form-field-border-all .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]),
.form-field-shape-square.form-field-border-all .sqs-block-form .form-item textarea,
.form-field-shape-square.form-field-border-all .sqs-block-form .form-item .file-display,
.form-field-shape-square.form-field-border-all .sqs-block-form .form-item select {
  border: solid var(--form-field-border-thickness) var(--dynamic-border-color, #A9A9A9);
  border-radius: 0;
  box-shadow: 0 0 0 1px transparent;
}
.form-field-shape-square.form-field-border-bottom .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]),
.form-field-shape-square.form-field-border-bottom .sqs-block-form .form-item textarea,
.form-field-shape-square.form-field-border-bottom .sqs-block-form .form-item .file-display,
.form-field-shape-square.form-field-border-bottom .sqs-block-form .form-item select {
  border-bottom: var(--form-field-border-thickness) solid var(--dynamic-border-color, #A9A9A9);
  border-top: 0;
  border-right: 0;
  border-left: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
/* Form Field Shape Tweak: Rounded
=================================================*/
.form-field-shape-rounded {
  /* Form Field Border Tweak: None
  =================================================*/
  /* Form Field Border Tweak: All
  =================================================*/
  /* Form Field Border Tweak: Bottom
  =================================================*/
}
.form-field-shape-rounded .sqs-block-form input:not([type="checkbox"]):not([type="radio"]),
.form-field-shape-rounded .sqs-block-form textarea,
.form-field-shape-rounded .sqs-block-form .file-upload,
.form-field-shape-rounded .sqs-block-form .file-display,
.form-field-shape-rounded .sqs-block-form select,
.form-field-shape-rounded .sqs-block-form .form-field-error {
  border-radius: 0.4rem;
}
.form-field-shape-rounded .sqs-block-form .form-input-effects {
  --fx-input-border-radius: 0.4rem;
}
.form-field-shape-rounded.form-field-border-none .sqs-block-form .form-item input,
.form-field-shape-rounded.form-field-border-none .sqs-block-form .form-item textarea,
.form-field-shape-rounded.form-field-border-none .sqs-block-form .form-item .file-display,
.form-field-shape-rounded.form-field-border-none .sqs-block-form .form-item select {
  border: 1px solid transparent;
  box-shadow: 0 0 0 1px transparent;
}
.form-field-shape-rounded.form-field-border-all .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]),
.form-field-shape-rounded.form-field-border-all .sqs-block-form .form-item textarea,
.form-field-shape-rounded.form-field-border-all .sqs-block-form .form-item .file-display,
.form-field-shape-rounded.form-field-border-all .sqs-block-form .form-item select {
  border: solid var(--form-field-border-thickness) var(--dynamic-border-color, #A9A9A9);
  box-shadow: 0 0 0 1px transparent;
}
.form-field-shape-rounded.form-field-border-bottom .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]),
.form-field-shape-rounded.form-field-border-bottom .sqs-block-form .form-item textarea,
.form-field-shape-rounded.form-field-border-bottom .sqs-block-form .form-item .file-display,
.form-field-shape-rounded.form-field-border-bottom .sqs-block-form .form-item select {
  border-bottom: var(--form-field-border-thickness) solid var(--dynamic-border-color, #A9A9A9);
  border-top: 0;
  border-right: 0;
  border-left: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
/* Form Field Shape Tweak: Pill
=================================================*/
.form-field-shape-pill {
  --pill-border-radius: calc(var(--form-field-padding-vertical) + var(--form-block-input-text-font-line-height) * 0.66);
  /* Form Field Border Tweak: None
  =================================================*/
  /* Form Field Border Tweak: All
  =================================================*/
  /* Form Field Border Tweak: Bottom
  =================================================*/
}
.form-field-shape-pill .sqs-block-form input:not([type="checkbox"]):not([type="radio"]),
.form-field-shape-pill .sqs-block-form textarea,
.form-field-shape-pill .sqs-block-form .file-upload,
.form-field-shape-pill .sqs-block-form .file-display,
.form-field-shape-pill .sqs-block-form select,
.form-field-shape-pill .sqs-block-form .form-field-error {
  border-radius: var(--pill-border-radius);
}
.form-field-shape-pill .sqs-block-form .form-input-effects {
  --fx-input-border-radius: var(--pill-border-radius);
}
.form-field-shape-pill.form-field-border-none .sqs-block-form .form-item input,
.form-field-shape-pill.form-field-border-none .sqs-block-form .form-item textarea,
.form-field-shape-pill.form-field-border-none .sqs-block-form .form-item .file-display,
.form-field-shape-pill.form-field-border-none .sqs-block-form .form-item select {
  border: 1px solid transparent;
  box-shadow: 0 0 0 1px transparent;
}
.form-field-shape-pill.form-field-border-all .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]),
.form-field-shape-pill.form-field-border-all .sqs-block-form .form-item textarea,
.form-field-shape-pill.form-field-border-all .sqs-block-form .form-item .file-display,
.form-field-shape-pill.form-field-border-all .sqs-block-form .form-item select {
  border: solid var(--form-field-border-thickness) var(--dynamic-border-color, #A9A9A9);
  box-shadow: 0 0 0 1px transparent;
}
.form-field-shape-pill.form-field-border-bottom .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]),
.form-field-shape-pill.form-field-border-bottom .sqs-block-form .form-item textarea,
.form-field-shape-pill.form-field-border-bottom .sqs-block-form .form-item .file-display,
.form-field-shape-pill.form-field-border-bottom .sqs-block-form .form-item select {
  border-bottom: var(--form-field-border-thickness) solid var(--dynamic-border-color, #A9A9A9);
  border-top: 0;
  border-right: 0;
  border-left: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
/* Form Field Shape Tweak: Custom
=================================================*/
.form-field-shape-custom {
  /* Form Field Border Tweak: None
  =================================================*/
  /* Form Field Border Tweak: All
  =================================================*/
  /* Form Field Border Tweak: Bottom
  =================================================*/
}
.form-field-shape-custom .sqs-block-form input:not([type="checkbox"]):not([type="radio"]),
.form-field-shape-custom .sqs-block-form textarea,
.form-field-shape-custom .sqs-block-form .file-upload,
.form-field-shape-custom .sqs-block-form .file-display,
.form-field-shape-custom .sqs-block-form select,
.form-field-shape-custom .sqs-block-form .form-field-error {
  border-top-left-radius: var(--form-field-shape-border-top-left-radius);
  border-top-right-radius: var(--form-field-shape-border-top-right-radius);
  border-bottom-right-radius: var(--form-field-shape-border-bottom-right-radius);
  border-bottom-left-radius: var(--form-field-shape-border-bottom-left-radius);
}
.form-field-shape-custom .sqs-block-form .form-input-effects {
  --fx-input-border-radius: var(--form-field-shape-border-top-left-radius) var(--form-field-shape-border-top-right-radius) var(--form-field-shape-border-bottom-right-radius) var(--form-field-shape-border-bottom-left-radius);
}
.form-field-shape-custom.form-field-border-none .sqs-block-form .form-item input,
.form-field-shape-custom.form-field-border-none .sqs-block-form .form-item textarea,
.form-field-shape-custom.form-field-border-none .sqs-block-form .form-item .file-display,
.form-field-shape-custom.form-field-border-none .sqs-block-form .form-item select {
  border: 1px solid transparent;
  box-shadow: 0 0 0 1px transparent;
}
.form-field-shape-custom.form-field-border-all .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]),
.form-field-shape-custom.form-field-border-all .sqs-block-form .form-item textarea,
.form-field-shape-custom.form-field-border-all .sqs-block-form .form-item .file-display,
.form-field-shape-custom.form-field-border-all .sqs-block-form .form-item select {
  border: solid var(--form-field-border-thickness) var(--dynamic-border-color, #A9A9A9);
  box-shadow: 0 0 0 1px transparent;
}
.form-field-shape-custom.form-field-border-bottom .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]),
.form-field-shape-custom.form-field-border-bottom .sqs-block-form .form-item textarea,
.form-field-shape-custom.form-field-border-bottom .sqs-block-form .form-item .file-display,
.form-field-shape-custom.form-field-border-bottom .sqs-block-form .form-item select {
  border-bottom: var(--form-field-border-thickness) solid var(--dynamic-border-color, #A9A9A9);
  border-top: 0;
  border-right: 0;
  border-left: 0;
}
/* Correct padding and border width for prefix element
=================================================*/
.form-field-border-all .sqs-block-form .form-item .form-input-prefix {
  padding: var(--form-field-padding-vertical) 10px var(--form-field-padding-vertical) var(--form-field-padding-horizontal);
  border-width: var(--form-field-border-thickness) 0;
  left: var(--form-field-border-thickness);
}
.form-field-border-bottom .sqs-block-form .form-item .form-input-prefix {
  padding: var(--form-field-padding-vertical) 10px var(--form-field-padding-vertical) var(--form-field-padding-horizontal);
  border-width: 0 0 var(--form-field-border-thickness) 0;
  left: 0;
}
.form-field-border-none .sqs-block-form .form-item .form-input-prefix {
  padding: var(--form-field-padding-vertical) 10px var(--form-field-padding-vertical) var(--form-field-padding-horizontal);
  border-width: 1px 0;
  left: 1px;
}
/* Form accessory
=================================================*/
.sqs-block-form .form-item.select svg path,
.sqs-block-form .form-item.time svg path,
.sqs-block-form .form-item.date svg path {
  fill: var(--dynamic-accessory-color, #000);
}
.sqs-block-form .form-item.select select ~ div {
  right: var(--form-field-padding-horizontal);
}
.sqs-block-form .form-item.date input ~ div,
.sqs-block-form .form-item.time input ~ div {
  right: var(--form-field-padding-horizontal);
}
.form-field-border-all .sqs-block-form .form-item select ~ div {
  right: calc(var(--form-field-border-thickness) + var(--form-field-padding-horizontal));
}
/* Checkbox field */
.form-field-checkbox-shape-square .sqs-block-form .form-item.checkbox input[type="checkbox"],
.form-field-checkbox-shape-square .sqs-block-form .form-item.email input[type="checkbox"],
.form-field-checkbox-shape-square .sqs-block-form .form-item.cover-the-fees input[type="checkbox"] {
  border-radius: 0;
}
.form-field-checkbox-shape-pill .sqs-block-form .form-item.checkbox input[type="checkbox"],
.form-field-checkbox-shape-pill .sqs-block-form .form-item.email input[type="checkbox"],
.form-field-checkbox-shape-pill .sqs-block-form .form-item.cover-the-fees input[type="checkbox"] {
  border-radius: 300px;
}
.form-field-checkbox-shape-rounded .sqs-block-form .form-item.checkbox input[type="checkbox"],
.form-field-checkbox-shape-rounded .sqs-block-form .form-item.email input[type="checkbox"],
.form-field-checkbox-shape-rounded .sqs-block-form .form-item.cover-the-fees input[type="checkbox"] {
  border-radius: 25%;
}
.form-field-checkbox-shape-custom .sqs-block-form .form-item.checkbox input[type="checkbox"],
.form-field-checkbox-shape-custom .sqs-block-form .form-item.email input[type="checkbox"],
.form-field-checkbox-shape-custom .sqs-block-form .form-item.cover-the-fees input[type="checkbox"],
.form-field-checkbox-shape-custom .sqs-block-form .form-item.checkbox label span,
.form-field-checkbox-shape-custom .sqs-block-form .form-item.email label span,
.form-field-checkbox-shape-custom .sqs-block-form .form-item.cover-the-fees label span {
  border-top-left-radius: var(--form-field-checkbox-shape-border-top-left-radius);
  border-top-right-radius: var(--form-field-checkbox-shape-border-top-right-radius);
  border-bottom-right-radius: var(--form-field-checkbox-shape-border-bottom-right-radius);
  border-bottom-left-radius: var(--form-field-checkbox-shape-border-bottom-left-radius);
}
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .sqs-block-form .form-item.checkbox,
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .form-item.email,
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .form-item.cover-the-fees {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: var(--dynamic-fill-color-a, 1);
  --checked-background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --checked-background-color-alpha: var(--dynamic-fill-color-a, 1);
  --checked-background-color-alpha-default: var(--dynamic-fill-color-a, 1);
  --checked-foreground-color: var(--dynamic-border-color, #000);
  --hover-alpha-modifier: var(--solid-hover-alpha-modifier);
}
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .sqs-block-form .form-item.checkbox label,
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .form-item.email label,
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .form-item.cover-the-fees label {
  column-gap: var(--form-field-checkbox-space-between-icon-and-text);
  cursor: pointer;
}
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .sqs-block-form .form-item.checkbox label input[type="checkbox"],
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .form-item.email label input[type="checkbox"],
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .form-item.cover-the-fees label input[type="checkbox"] {
  cursor: pointer;
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  border: var(--form-field-checkbox-border-thickness) solid var(--dynamic-border-color, #A9A9A9);
  width: var(--form-field-checkbox-size);
  height: var(--form-field-checkbox-size);
  flex-shrink: 0;
  margin-right: 0px;
}
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .sqs-block-form .form-item.checkbox label input[type="checkbox"]::before,
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .form-item.email label input[type="checkbox"]::before,
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .form-item.cover-the-fees label input[type="checkbox"]::before {
  width: 75%;
  height: 75%;
  background-color: var(--checked-foreground-color) !important;
}
@media (hover: hover) {
  .form-field-checkbox-fill-solid.form-field-checkbox-color-normal .sqs-block-form .form-item.checkbox label input[type="checkbox"]:hover,
  .form-field-checkbox-fill-solid.form-field-checkbox-color-normal .form-item.email label input[type="checkbox"]:hover,
  .form-field-checkbox-fill-solid.form-field-checkbox-color-normal .form-item.cover-the-fees label input[type="checkbox"]:hover {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
}
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .sqs-block-form .form-item.checkbox label input[type="checkbox"]:checked,
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .form-item.email label input[type="checkbox"]:checked,
.form-field-checkbox-fill-solid.form-field-checkbox-color-normal .form-item.cover-the-fees label input[type="checkbox"]:checked {
  background-color: hsla(var(--checked-background-color-hsl), var(--checked-background-color-alpha-default)) !important;
}
@media (hover: hover) {
  .form-field-checkbox-fill-solid.form-field-checkbox-color-normal .sqs-block-form .form-item.checkbox label input[type="checkbox"]:checked:hover,
  .form-field-checkbox-fill-solid.form-field-checkbox-color-normal .form-item.email label input[type="checkbox"]:checked:hover,
  .form-field-checkbox-fill-solid.form-field-checkbox-color-normal .form-item.cover-the-fees label input[type="checkbox"]:checked:hover {
    background-color: hsla(var(--checked-background-color-hsl), calc(var(--checked-background-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier)))) !important;
  }
}
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .sqs-block-form .form-item.checkbox,
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .form-item.email,
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .form-item.cover-the-fees {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: var(--dynamic-fill-color-a, 1);
  --checked-background-color-hsl: var(--dynamic-border-color-hsl, var(--fallback-black-hsl));
  --checked-background-color-alpha: var(--dynamic-border-color-a, 1);
  --checked-background-color-alpha-default: var(--dynamic-border-color-a, 1);
  --checked-foreground-color: var(--dynamic-fill-color, #FAFAFA);
  --hover-alpha-modifier: var(--solid-hover-alpha-modifier);
  --checked-hover-alpha-modifier: var(--inverted-solid-hover-alpha-modifier);
}
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .sqs-block-form .form-item.checkbox label,
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .form-item.email label,
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .form-item.cover-the-fees label {
  column-gap: var(--form-field-checkbox-space-between-icon-and-text);
  cursor: pointer;
}
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .sqs-block-form .form-item.checkbox label input[type="checkbox"],
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .form-item.email label input[type="checkbox"],
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .form-item.cover-the-fees label input[type="checkbox"] {
  cursor: pointer;
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  border: var(--form-field-checkbox-border-thickness) solid var(--dynamic-border-color, #A9A9A9);
  width: var(--form-field-checkbox-size);
  height: var(--form-field-checkbox-size);
  flex-shrink: 0;
  margin-right: 0px;
}
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .sqs-block-form .form-item.checkbox label input[type="checkbox"]::before,
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .form-item.email label input[type="checkbox"]::before,
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .form-item.cover-the-fees label input[type="checkbox"]::before {
  width: 75%;
  height: 75%;
  background-color: var(--checked-foreground-color) !important;
}
@media (hover: hover) {
  .form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .sqs-block-form .form-item.checkbox label input[type="checkbox"]:hover,
  .form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .form-item.email label input[type="checkbox"]:hover,
  .form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .form-item.cover-the-fees label input[type="checkbox"]:hover {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
}
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .sqs-block-form .form-item.checkbox label input[type="checkbox"]:checked,
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .form-item.email label input[type="checkbox"]:checked,
.form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .form-item.cover-the-fees label input[type="checkbox"]:checked {
  background-color: hsla(var(--checked-background-color-hsl), var(--checked-background-color-alpha-default)) !important;
}
@media (hover: hover) {
  .form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .sqs-block-form .form-item.checkbox label input[type="checkbox"]:checked:hover,
  .form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .form-item.email label input[type="checkbox"]:checked:hover,
  .form-field-checkbox-fill-solid.form-field-checkbox-color-inverted .form-item.cover-the-fees label input[type="checkbox"]:checked:hover {
    background-color: hsla(var(--checked-background-color-hsl), calc(var(--checked-background-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier)))) !important;
  }
}
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .sqs-block-form .form-item.checkbox,
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .form-item.email,
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .form-item.cover-the-fees {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: 0;
  --checked-background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --checked-background-color-alpha: var(--dynamic-fill-color-a, 1);
  --checked-background-color-alpha-default: 0;
  --checked-foreground-color: var(--dynamic-border-color, #000);
  --hover-alpha-modifier: var(--outline-hover-alpha-modifier);
}
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .sqs-block-form .form-item.checkbox label,
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .form-item.email label,
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .form-item.cover-the-fees label {
  column-gap: var(--form-field-checkbox-space-between-icon-and-text);
  cursor: pointer;
}
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .sqs-block-form .form-item.checkbox label input[type="checkbox"],
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .form-item.email label input[type="checkbox"],
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .form-item.cover-the-fees label input[type="checkbox"] {
  cursor: pointer;
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  border: var(--form-field-checkbox-border-thickness) solid var(--dynamic-border-color, #A9A9A9);
  width: var(--form-field-checkbox-size);
  height: var(--form-field-checkbox-size);
  flex-shrink: 0;
  margin-right: 0px;
}
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .sqs-block-form .form-item.checkbox label input[type="checkbox"]::before,
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .form-item.email label input[type="checkbox"]::before,
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .form-item.cover-the-fees label input[type="checkbox"]::before {
  width: 75%;
  height: 75%;
  background-color: var(--checked-foreground-color) !important;
}
@media (hover: hover) {
  .form-field-checkbox-fill-outline.form-field-checkbox-color-normal .sqs-block-form .form-item.checkbox label input[type="checkbox"]:hover,
  .form-field-checkbox-fill-outline.form-field-checkbox-color-normal .form-item.email label input[type="checkbox"]:hover,
  .form-field-checkbox-fill-outline.form-field-checkbox-color-normal .form-item.cover-the-fees label input[type="checkbox"]:hover {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
}
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .sqs-block-form .form-item.checkbox label input[type="checkbox"]:checked,
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .form-item.email label input[type="checkbox"]:checked,
.form-field-checkbox-fill-outline.form-field-checkbox-color-normal .form-item.cover-the-fees label input[type="checkbox"]:checked {
  background-color: hsla(var(--checked-background-color-hsl), var(--checked-background-color-alpha-default)) !important;
}
@media (hover: hover) {
  .form-field-checkbox-fill-outline.form-field-checkbox-color-normal .sqs-block-form .form-item.checkbox label input[type="checkbox"]:checked:hover,
  .form-field-checkbox-fill-outline.form-field-checkbox-color-normal .form-item.email label input[type="checkbox"]:checked:hover,
  .form-field-checkbox-fill-outline.form-field-checkbox-color-normal .form-item.cover-the-fees label input[type="checkbox"]:checked:hover {
    background-color: hsla(var(--checked-background-color-hsl), calc(var(--checked-background-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier)))) !important;
  }
}
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .sqs-block-form .form-item.checkbox,
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .form-item.email,
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .form-item.cover-the-fees {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: 0;
  --checked-background-color-hsl: var(--dynamic-border-color-hsl, var(--fallback-black-hsl));
  --checked-background-color-alpha: var(--dynamic-border-color-a, 1);
  --checked-background-color-alpha-default: var(--dynamic-border-color-a, 1);
  --checked-foreground-color: var(--dynamic-fill-color, #FAFAFA);
  --hover-alpha-modifier: var(--outline-hover-alpha-modifier);
  --checked-hover-alpha-modifier: var(--inverted-outline-hover-alpha-modifier);
}
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .sqs-block-form .form-item.checkbox label,
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .form-item.email label,
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .form-item.cover-the-fees label {
  column-gap: var(--form-field-checkbox-space-between-icon-and-text);
  cursor: pointer;
}
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .sqs-block-form .form-item.checkbox label input[type="checkbox"],
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .form-item.email label input[type="checkbox"],
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .form-item.cover-the-fees label input[type="checkbox"] {
  cursor: pointer;
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  border: var(--form-field-checkbox-border-thickness) solid var(--dynamic-border-color, #A9A9A9);
  width: var(--form-field-checkbox-size);
  height: var(--form-field-checkbox-size);
  flex-shrink: 0;
  margin-right: 0px;
}
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .sqs-block-form .form-item.checkbox label input[type="checkbox"]::before,
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .form-item.email label input[type="checkbox"]::before,
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .form-item.cover-the-fees label input[type="checkbox"]::before {
  width: 75%;
  height: 75%;
  background-color: var(--checked-foreground-color) !important;
}
@media (hover: hover) {
  .form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .sqs-block-form .form-item.checkbox label input[type="checkbox"]:hover,
  .form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .form-item.email label input[type="checkbox"]:hover,
  .form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .form-item.cover-the-fees label input[type="checkbox"]:hover {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
}
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .sqs-block-form .form-item.checkbox label input[type="checkbox"]:checked,
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .form-item.email label input[type="checkbox"]:checked,
.form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .form-item.cover-the-fees label input[type="checkbox"]:checked {
  background-color: hsla(var(--checked-background-color-hsl), var(--checked-background-color-alpha-default)) !important;
}
@media (hover: hover) {
  .form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .sqs-block-form .form-item.checkbox label input[type="checkbox"]:checked:hover,
  .form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .form-item.email label input[type="checkbox"]:checked:hover,
  .form-field-checkbox-fill-outline.form-field-checkbox-color-inverted .form-item.cover-the-fees label input[type="checkbox"]:checked:hover {
    background-color: hsla(var(--checked-background-color-hsl), calc(var(--checked-background-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier)))) !important;
  }
}
.form-field-checkbox-type-button.form-field-checkbox-fill-solid .sqs-block-form .form-item.checkbox {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: var(--dynamic-fill-color-a, 1);
  --foreground-color-hsl: var(--dynamic-border-color-hsl, var(--fallback-black-hsl));
  --foreground-color-alpha: var(--dynamic-border-color-a, 1);
  --checked-foreground-color: var(--dynamic-fill-color, #FAFAFA);
  --hover-alpha-modifier: var(--solid-hover-alpha-modifier);
  --checked-hover-alpha-modifier: var(--inverted-solid-hover-alpha-modifier);
}
.form-field-checkbox-type-button.form-field-checkbox-fill-solid .sqs-block-form .form-item.checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}
.form-field-checkbox-type-button.form-field-checkbox-fill-solid .sqs-block-form .form-item.checkbox label {
  cursor: pointer;
  user-select: none;
  display: inline-flex;
}
.form-field-checkbox-type-button.form-field-checkbox-fill-solid .sqs-block-form .form-item.checkbox label span {
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  color: hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
  padding: var(--form-field-checkbox-padding-vertical) var(--form-field-checkbox-padding-horizontal);
  border: var(--form-field-checkbox-border-thickness) solid hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
}
@media (hover: hover) {
  .form-field-checkbox-type-button.form-field-checkbox-fill-solid .sqs-block-form .form-item.checkbox label span:hover {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
}
.form-field-checkbox-type-button.form-field-checkbox-fill-solid .sqs-block-form .form-item.checkbox label input[type="checkbox"]:checked ~ span {
  background-color: hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
  color: var(--checked-foreground-color);
  border-color: hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
}
@media (hover: hover) {
  .form-field-checkbox-type-button.form-field-checkbox-fill-solid .sqs-block-form .form-item.checkbox label input[type="checkbox"]:checked ~ span:hover {
    background-color: hsla(var(--foreground-color-hsl), calc(var(--foreground-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier))));
  }
}
.form-field-checkbox-type-button.form-field-checkbox-fill-outline .sqs-block-form .form-item.checkbox {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: 0;
  --foreground-color-hsl: var(--dynamic-border-color-hsl, var(--fallback-black-hsl));
  --foreground-color-alpha: var(--dynamic-border-color-a, 1);
  --checked-foreground-color: var(--dynamic-fill-color, #FAFAFA);
  --hover-alpha-modifier: var(--outline-hover-alpha-modifier);
  --checked-hover-alpha-modifier: var(--inverted-outline-hover-alpha-modifier);
}
.form-field-checkbox-type-button.form-field-checkbox-fill-outline .sqs-block-form .form-item.checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}
.form-field-checkbox-type-button.form-field-checkbox-fill-outline .sqs-block-form .form-item.checkbox label {
  cursor: pointer;
  user-select: none;
  display: inline-flex;
}
.form-field-checkbox-type-button.form-field-checkbox-fill-outline .sqs-block-form .form-item.checkbox label span {
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  color: hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
  padding: var(--form-field-checkbox-padding-vertical) var(--form-field-checkbox-padding-horizontal);
  border: var(--form-field-checkbox-border-thickness) solid hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
}
@media (hover: hover) {
  .form-field-checkbox-type-button.form-field-checkbox-fill-outline .sqs-block-form .form-item.checkbox label span:hover {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
}
.form-field-checkbox-type-button.form-field-checkbox-fill-outline .sqs-block-form .form-item.checkbox label input[type="checkbox"]:checked ~ span {
  background-color: hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
  color: var(--checked-foreground-color);
  border-color: hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
}
@media (hover: hover) {
  .form-field-checkbox-type-button.form-field-checkbox-fill-outline .sqs-block-form .form-item.checkbox label input[type="checkbox"]:checked ~ span:hover {
    background-color: hsla(var(--foreground-color-hsl), calc(var(--foreground-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier))));
  }
}
.form-field-checkbox-type-button.form-field-checkbox-shape-square .sqs-block-form .form-item.checkbox label span {
  border-radius: 0;
}
.form-field-checkbox-type-button.form-field-checkbox-shape-pill {
  --checkbox-pill-border-radius: calc(var(--form-field-checkbox-padding-vertical) + var(--form-block-option-text-font-line-height) * 0.66);
}
.form-field-checkbox-type-button.form-field-checkbox-shape-pill .sqs-block-form .form-item.checkbox label span {
  border-radius: var(--checkbox-pill-border-radius);
}
.form-field-checkbox-type-button.form-field-checkbox-shape-rounded .sqs-block-form .form-item.checkbox label span {
  border-radius: 0.4rem;
}
.form-field-checkbox-layout-stack .sqs-block-form .form-item.checkbox {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: var(--form-field-checkbox-row-gap) var(--form-field-checkbox-column-gap);
}
.form-field-checkbox-layout-fit .sqs-block-form .form-item.checkbox {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: var(--form-field-checkbox-row-gap) var(--form-field-checkbox-column-gap);
}
/* Radio field */
.form-field-radio-shape-square .sqs-block-form .form-item.radio .radio-mark,
.form-field-radio-shape-square .sqs-block-form .form-item.radio .radio-mark::after,
.form-field-radio-shape-square .sqs-block-form .form-item.radio .radio-label,
.form-field-radio-shape-square .sqs-block-form .form-item.radio .radio-outline {
  border-radius: 0;
}
.form-field-radio-shape-pill .sqs-block-form .form-item.radio .radio-mark,
.form-field-radio-shape-pill .sqs-block-form .form-item.radio .radio-mark::after,
.form-field-radio-shape-pill .sqs-block-form .form-item.radio .radio-label,
.form-field-radio-shape-pill .sqs-block-form .form-item.radio .radio-outline {
  border-radius: 300px;
}
.form-field-radio-shape-custom .sqs-block-form .form-item.radio .radio-mark,
.form-field-radio-shape-custom .sqs-block-form .form-item.radio .radio-mark::after,
.form-field-radio-shape-custom .sqs-block-form .form-item.radio .radio-label,
.form-field-radio-shape-custom .sqs-block-form .form-item.radio .radio-outline {
  border-top-left-radius: var(--form-field-radio-shape-border-top-left-radius);
  border-top-right-radius: var(--form-field-radio-shape-border-top-right-radius);
  border-bottom-right-radius: var(--form-field-radio-shape-border-bottom-right-radius);
  border-bottom-left-radius: var(--form-field-radio-shape-border-bottom-left-radius);
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-normal .sqs-block-form .form-item.radio {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: var(--dynamic-fill-color-a, 1);
  --checked-background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --checked-background-color-alpha: var(--dynamic-fill-color-a, 1);
  --checked-background-color-alpha-default: var(--dynamic-fill-color-a, 1);
  --checked-foreground-color: var(--dynamic-border-color, #000);
  --hover-alpha-modifier: var(--solid-hover-alpha-modifier);
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-normal .sqs-block-form .form-item.radio label {
  column-gap: var(--form-field-radio-space-between-icon-and-text);
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-normal .sqs-block-form .form-item.radio label input[type="radio"] {
  width: var(--form-field-radio-size);
  height: var(--form-field-radio-size);
  margin-right: 0px;
  cursor: pointer;
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-normal .sqs-block-form .form-item.radio label .radio-mark {
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  border: var(--form-field-radio-border-thickness) solid var(--dynamic-border-color, #000);
  width: var(--form-field-radio-size);
  height: var(--form-field-radio-size);
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-normal .sqs-block-form .form-item.radio label .radio-mark::after {
  background-color: var(--checked-foreground-color) !important;
  position: absolute;
  width: 50%;
  height: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-normal .sqs-block-form .form-item.radio label .radio-outline {
  width: var(--form-field-radio-size);
  height: var(--form-field-radio-size);
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-normal .sqs-block-form .form-item.radio label input[type="radio"]:checked ~ .radio-mark {
  background-color: hsla(var(--checked-background-color-hsl), var(--checked-background-color-alpha-default)) !important;
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-normal .sqs-block-form .form-item.radio label input[type="radio"]:checked ~ .radio-mark::after {
  transform: translate(-50%, -50%) scale(1);
}
@media (hover: hover) {
  .form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-normal .sqs-block-form .form-item.radio label:hover input[type="radio"] ~ .radio-mark {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
  .form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-normal .sqs-block-form .form-item.radio label:hover input[type="radio"]:checked ~ .radio-mark {
    background-color: hsla(var(--checked-background-color-hsl), calc(var(--checked-background-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier)))) !important;
  }
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-normal .sqs-block-form .form-item.radio label input[type="radio"] {
  flex-shrink: 0;
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-inverted .sqs-block-form .form-item.radio {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: var(--dynamic-fill-color-a, 1);
  --checked-background-color-hsl: var(--dynamic-border-color-hsl, var(--fallback-black-hsl));
  --checked-background-color-alpha: var(--dynamic-border-color-a, 1);
  --checked-background-color-alpha-default: var(--dynamic-border-color-a, 1);
  --checked-foreground-color: var(--dynamic-fill-color, #FAFAFA);
  --hover-alpha-modifier: var(--solid-hover-alpha-modifier);
  --checked-hover-alpha-modifier: var(--inverted-solid-hover-alpha-modifier);
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-inverted .sqs-block-form .form-item.radio label {
  column-gap: var(--form-field-radio-space-between-icon-and-text);
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-inverted .sqs-block-form .form-item.radio label input[type="radio"] {
  width: var(--form-field-radio-size);
  height: var(--form-field-radio-size);
  margin-right: 0px;
  cursor: pointer;
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-inverted .sqs-block-form .form-item.radio label .radio-mark {
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  border: var(--form-field-radio-border-thickness) solid var(--dynamic-border-color, #000);
  width: var(--form-field-radio-size);
  height: var(--form-field-radio-size);
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-inverted .sqs-block-form .form-item.radio label .radio-mark::after {
  background-color: var(--checked-foreground-color) !important;
  position: absolute;
  width: 50%;
  height: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-inverted .sqs-block-form .form-item.radio label .radio-outline {
  width: var(--form-field-radio-size);
  height: var(--form-field-radio-size);
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-inverted .sqs-block-form .form-item.radio label input[type="radio"]:checked ~ .radio-mark {
  background-color: hsla(var(--checked-background-color-hsl), var(--checked-background-color-alpha-default)) !important;
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-inverted .sqs-block-form .form-item.radio label input[type="radio"]:checked ~ .radio-mark::after {
  transform: translate(-50%, -50%) scale(1);
}
@media (hover: hover) {
  .form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-inverted .sqs-block-form .form-item.radio label:hover input[type="radio"] ~ .radio-mark {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
  .form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-inverted .sqs-block-form .form-item.radio label:hover input[type="radio"]:checked ~ .radio-mark {
    background-color: hsla(var(--checked-background-color-hsl), calc(var(--checked-background-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier)))) !important;
  }
}
.form-field-radio-type-icon.form-field-radio-fill-solid.form-field-radio-color-inverted .sqs-block-form .form-item.radio label input[type="radio"] {
  flex-shrink: 0;
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-normal .sqs-block-form .form-item.radio {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: 0;
  --checked-background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --checked-background-color-alpha: var(--dynamic-fill-color-a, 1);
  --checked-background-color-alpha-default: 0;
  --checked-foreground-color: var(--dynamic-border-color, #000);
  --hover-alpha-modifier: var(--outline-hover-alpha-modifier);
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-normal .sqs-block-form .form-item.radio label {
  column-gap: var(--form-field-radio-space-between-icon-and-text);
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-normal .sqs-block-form .form-item.radio label input[type="radio"] {
  width: var(--form-field-radio-size);
  height: var(--form-field-radio-size);
  margin-right: 0px;
  cursor: pointer;
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-normal .sqs-block-form .form-item.radio label .radio-mark {
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  border: var(--form-field-radio-border-thickness) solid var(--dynamic-border-color, #000);
  width: var(--form-field-radio-size);
  height: var(--form-field-radio-size);
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-normal .sqs-block-form .form-item.radio label .radio-mark::after {
  background-color: var(--checked-foreground-color) !important;
  position: absolute;
  width: 50%;
  height: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-normal .sqs-block-form .form-item.radio label .radio-outline {
  width: var(--form-field-radio-size);
  height: var(--form-field-radio-size);
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-normal .sqs-block-form .form-item.radio label input[type="radio"]:checked ~ .radio-mark {
  background-color: hsla(var(--checked-background-color-hsl), var(--checked-background-color-alpha-default)) !important;
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-normal .sqs-block-form .form-item.radio label input[type="radio"]:checked ~ .radio-mark::after {
  transform: translate(-50%, -50%) scale(1);
}
@media (hover: hover) {
  .form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-normal .sqs-block-form .form-item.radio label:hover input[type="radio"] ~ .radio-mark {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
  .form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-normal .sqs-block-form .form-item.radio label:hover input[type="radio"]:checked ~ .radio-mark {
    background-color: hsla(var(--checked-background-color-hsl), calc(var(--checked-background-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier)))) !important;
  }
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-normal .sqs-block-form .form-item.radio label input[type="radio"] {
  flex-shrink: 0;
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-inverted .sqs-block-form .form-item.radio {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: 0;
  --checked-background-color-hsl: var(--dynamic-border-color-hsl, var(--fallback-black-hsl));
  --checked-background-color-alpha: var(--dynamic-border-color-a, 1);
  --checked-background-color-alpha-default: var(--dynamic-border-color-a, 1);
  --checked-foreground-color: var(--dynamic-fill-color, #FAFAFA);
  --hover-alpha-modifier: var(--outline-hover-alpha-modifier);
  --checked-hover-alpha-modifier: var(--inverted-outline-hover-alpha-modifier);
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-inverted .sqs-block-form .form-item.radio label {
  column-gap: var(--form-field-radio-space-between-icon-and-text);
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-inverted .sqs-block-form .form-item.radio label input[type="radio"] {
  width: var(--form-field-radio-size);
  height: var(--form-field-radio-size);
  margin-right: 0px;
  cursor: pointer;
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-inverted .sqs-block-form .form-item.radio label .radio-mark {
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  border: var(--form-field-radio-border-thickness) solid var(--dynamic-border-color, #000);
  width: var(--form-field-radio-size);
  height: var(--form-field-radio-size);
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-inverted .sqs-block-form .form-item.radio label .radio-mark::after {
  background-color: var(--checked-foreground-color) !important;
  position: absolute;
  width: 50%;
  height: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-inverted .sqs-block-form .form-item.radio label .radio-outline {
  width: var(--form-field-radio-size);
  height: var(--form-field-radio-size);
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-inverted .sqs-block-form .form-item.radio label input[type="radio"]:checked ~ .radio-mark {
  background-color: hsla(var(--checked-background-color-hsl), var(--checked-background-color-alpha-default)) !important;
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-inverted .sqs-block-form .form-item.radio label input[type="radio"]:checked ~ .radio-mark::after {
  transform: translate(-50%, -50%) scale(1);
}
@media (hover: hover) {
  .form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-inverted .sqs-block-form .form-item.radio label:hover input[type="radio"] ~ .radio-mark {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
  .form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-inverted .sqs-block-form .form-item.radio label:hover input[type="radio"]:checked ~ .radio-mark {
    background-color: hsla(var(--checked-background-color-hsl), calc(var(--checked-background-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier)))) !important;
  }
}
.form-field-radio-type-icon.form-field-radio-fill-outline.form-field-radio-color-inverted .sqs-block-form .form-item.radio label input[type="radio"] {
  flex-shrink: 0;
}
.form-field-radio-type-icon.form-field-radio-shape-rounded .sqs-block-form .form-item.radio .radio-mark,
.form-field-radio-type-icon.form-field-radio-shape-rounded .sqs-block-form .form-item.radio .radio-mark::after,
.form-field-radio-type-icon.form-field-radio-shape-rounded .sqs-block-form .form-item.radio .radio-label,
.form-field-radio-type-icon.form-field-radio-shape-rounded .sqs-block-form .form-item.radio .radio-outline {
  border-radius: 25%;
}
.form-field-radio-type-button.form-field-radio-fill-solid .sqs-block-form .form-item.radio {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: var(--dynamic-fill-color-a, 1);
  --foreground-color-hsl: var(--dynamic-border-color-hsl, var(--fallback-black-hsl));
  --foreground-color-alpha: var(--dynamic-border-color-a, 1);
  --checked-foreground-color: var(--dynamic-fill-color, #FAFAFA);
  --hover-alpha-modifier: var(--solid-hover-alpha-modifier);
  --checked-hover-alpha-modifier: var(--inverted-solid-hover-alpha-modifier);
}
.form-field-radio-type-button.form-field-radio-fill-solid .sqs-block-form .form-item.radio input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  /* hover states on the input rather than siblings as that's what obtains hover/focus from label */
}
@media (hover: hover) {
  .form-field-radio-type-button.form-field-radio-fill-solid .sqs-block-form .form-item.radio input[type="radio"]:hover ~ span {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
  .form-field-radio-type-button.form-field-radio-fill-solid .sqs-block-form .form-item.radio input[type="radio"]:checked:hover ~ span {
    background-color: hsla(var(--foreground-color-hsl), calc(var(--foreground-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier))));
  }
}
.form-field-radio-type-button.form-field-radio-fill-solid .sqs-block-form .form-item.radio label span:empty {
  display: none;
}
.form-field-radio-type-button.form-field-radio-fill-solid .sqs-block-form .form-item.radio label {
  cursor: pointer;
  user-select: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.form-field-radio-type-button.form-field-radio-fill-solid .sqs-block-form .form-item.radio label span {
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  color: hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
  padding: var(--form-field-radio-padding-vertical) var(--form-field-radio-padding-horizontal);
  border: var(--form-field-radio-border-thickness) solid hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
}
.form-field-radio-type-button.form-field-radio-fill-solid .sqs-block-form .form-item.radio label input[type="radio"]:checked ~ span {
  background-color: hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
  color: var(--checked-foreground-color);
  border-color: hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
}
.form-field-radio-type-button.form-field-radio-fill-outline .sqs-block-form .form-item.radio {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: 0;
  --foreground-color-hsl: var(--dynamic-border-color-hsl, var(--fallback-black-hsl));
  --foreground-color-alpha: var(--dynamic-border-color-a, 1);
  --checked-foreground-color: var(--dynamic-fill-color, #FAFAFA);
  --hover-alpha-modifier: var(--outline-hover-alpha-modifier);
  --checked-hover-alpha-modifier: var(--inverted-outline-hover-alpha-modifier);
}
.form-field-radio-type-button.form-field-radio-fill-outline .sqs-block-form .form-item.radio input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  /* hover states on the input rather than siblings as that's what obtains hover/focus from label */
}
@media (hover: hover) {
  .form-field-radio-type-button.form-field-radio-fill-outline .sqs-block-form .form-item.radio input[type="radio"]:hover ~ span {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
  .form-field-radio-type-button.form-field-radio-fill-outline .sqs-block-form .form-item.radio input[type="radio"]:checked:hover ~ span {
    background-color: hsla(var(--foreground-color-hsl), calc(var(--foreground-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier))));
  }
}
.form-field-radio-type-button.form-field-radio-fill-outline .sqs-block-form .form-item.radio label span:empty {
  display: none;
}
.form-field-radio-type-button.form-field-radio-fill-outline .sqs-block-form .form-item.radio label {
  cursor: pointer;
  user-select: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.form-field-radio-type-button.form-field-radio-fill-outline .sqs-block-form .form-item.radio label span {
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  color: hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
  padding: var(--form-field-radio-padding-vertical) var(--form-field-radio-padding-horizontal);
  border: var(--form-field-radio-border-thickness) solid hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
}
.form-field-radio-type-button.form-field-radio-fill-outline .sqs-block-form .form-item.radio label input[type="radio"]:checked ~ span {
  background-color: hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
  color: var(--checked-foreground-color);
  border-color: hsla(var(--foreground-color-hsl), var(--foreground-color-alpha));
}
.form-field-radio-type-button.form-field-radio-shape-rounded .sqs-block-form .form-item.radio .radio-mark,
.form-field-radio-type-button.form-field-radio-shape-rounded .sqs-block-form .form-item.radio .radio-mark::after,
.form-field-radio-type-button.form-field-radio-shape-rounded .sqs-block-form .form-item.radio .radio-label,
.form-field-radio-type-button.form-field-radio-shape-rounded .sqs-block-form .form-item.radio .radio-outline {
  border-radius: 0.4rem;
}
.form-field-radio-type-button.form-field-radio-shape-pill .sqs-block-form .form-item.radio {
  --radio-pill-border-radius: calc(var(--form-field-radio-padding-vertical) + var(--form-block-option-text-font-line-height) * 0.66);
}
.form-field-radio-type-button.form-field-radio-shape-pill .sqs-block-form .form-item.radio .radio-mark,
.form-field-radio-type-button.form-field-radio-shape-pill .sqs-block-form .form-item.radio .radio-mark::after,
.form-field-radio-type-button.form-field-radio-shape-pill .sqs-block-form .form-item.radio .radio-label,
.form-field-radio-type-button.form-field-radio-shape-pill .sqs-block-form .form-item.radio .radio-outline {
  border-radius: var(--radio-pill-border-radius);
}
.form-field-radio-layout-stack .sqs-block-form .form-item.radio {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: var(--form-field-radio-row-gap) var(--form-field-radio-column-gap);
}
.form-field-radio-layout-stack .sqs-block-form .form-item.radio .radio-mark {
  left: unset;
}
.form-field-radio-layout-fit .sqs-block-form .form-item.radio {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: var(--form-field-radio-row-gap) var(--form-field-radio-column-gap);
}
.form-field-radio-layout-fit .sqs-block-form .form-item.radio .radio-mark {
  left: unset;
}
/* Survey field */
.form-field-survey-shape-square .sqs-block-form .form-item.likert .radio-mark,
.form-field-survey-shape-square .sqs-block-form .form-item.likert .radio-mark::after,
.form-field-survey-shape-square .sqs-block-form .form-item.likert .radio-label,
.form-field-survey-shape-square .sqs-block-form .form-item.likert .radio-outline {
  border-radius: 0;
}
.form-field-survey-shape-rounded .sqs-block-form .form-item.likert .radio-mark,
.form-field-survey-shape-rounded .sqs-block-form .form-item.likert .radio-mark::after,
.form-field-survey-shape-rounded .sqs-block-form .form-item.likert .radio-label,
.form-field-survey-shape-rounded .sqs-block-form .form-item.likert .radio-outline {
  border-radius: 25%;
}
.form-field-survey-shape-pill .sqs-block-form .form-item.likert .radio-mark,
.form-field-survey-shape-pill .sqs-block-form .form-item.likert .radio-mark::after,
.form-field-survey-shape-pill .sqs-block-form .form-item.likert .radio-label,
.form-field-survey-shape-pill .sqs-block-form .form-item.likert .radio-outline {
  border-radius: 300px;
}
.form-field-survey-shape-custom .sqs-block-form .form-item.likert .radio-mark,
.form-field-survey-shape-custom .sqs-block-form .form-item.likert .radio-mark::after,
.form-field-survey-shape-custom .sqs-block-form .form-item.likert .radio-label,
.form-field-survey-shape-custom .sqs-block-form .form-item.likert .radio-outline {
  border-top-left-radius: var(--form-field-survey-shape-border-top-left-radius);
  border-top-right-radius: var(--form-field-survey-shape-border-top-right-radius);
  border-bottom-right-radius: var(--form-field-survey-shape-border-bottom-right-radius);
  border-bottom-left-radius: var(--form-field-survey-shape-border-bottom-left-radius);
}
.form-field-survey-fill-solid.form-field-survey-color-normal .sqs-block-form .form-item.likert {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: var(--dynamic-fill-color-a, 1);
  --checked-background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --checked-background-color-alpha: var(--dynamic-fill-color-a, 1);
  --checked-background-color-alpha-default: var(--dynamic-fill-color-a, 1);
  --checked-foreground-color: var(--dynamic-border-color, #000);
  --hover-alpha-modifier: var(--solid-hover-alpha-modifier);
}
.form-field-survey-fill-solid.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title) input[type="radio"] {
  width: var(--form-field-survey-size);
  height: var(--form-field-survey-size);
  margin-right: 0px;
  cursor: pointer;
}
.form-field-survey-fill-solid.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title) .radio-mark {
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  border: var(--form-field-survey-border-thickness) solid var(--dynamic-border-color, #000);
  width: var(--form-field-survey-size);
  height: var(--form-field-survey-size);
}
.form-field-survey-fill-solid.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title) .radio-mark::after {
  background-color: var(--checked-foreground-color) !important;
  position: absolute;
  width: 50%;
  height: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
}
.form-field-survey-fill-solid.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title) .radio-outline {
  width: var(--form-field-survey-size);
  height: var(--form-field-survey-size);
}
.form-field-survey-fill-solid.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title) input[type="radio"]:checked ~ .radio-mark {
  background-color: hsla(var(--checked-background-color-hsl), var(--checked-background-color-alpha-default)) !important;
}
.form-field-survey-fill-solid.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title) input[type="radio"]:checked ~ .radio-mark::after {
  transform: translate(-50%, -50%) scale(1);
}
@media (hover: hover) {
  .form-field-survey-fill-solid.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title):hover input[type="radio"] ~ .radio-mark {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
  .form-field-survey-fill-solid.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title):hover input[type="radio"]:checked ~ .radio-mark {
    background-color: hsla(var(--checked-background-color-hsl), calc(var(--checked-background-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier)))) !important;
  }
}
.form-field-survey-fill-solid.form-field-survey-color-inverted .sqs-block-form .form-item.likert {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: var(--dynamic-fill-color-a, 1);
  --checked-background-color-hsl: var(--dynamic-border-color-hsl, var(--fallback-black-hsl));
  --checked-background-color-alpha: var(--dynamic-border-color-a, 1);
  --checked-background-color-alpha-default: var(--dynamic-border-color-a, 1);
  --checked-foreground-color: var(--dynamic-fill-color, #FAFAFA);
  --hover-alpha-modifier: var(--solid-hover-alpha-modifier);
  --checked-hover-alpha-modifier: var(--inverted-solid-hover-alpha-modifier);
}
.form-field-survey-fill-solid.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title) input[type="radio"] {
  width: var(--form-field-survey-size);
  height: var(--form-field-survey-size);
  margin-right: 0px;
  cursor: pointer;
}
.form-field-survey-fill-solid.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title) .radio-mark {
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  border: var(--form-field-survey-border-thickness) solid var(--dynamic-border-color, #000);
  width: var(--form-field-survey-size);
  height: var(--form-field-survey-size);
}
.form-field-survey-fill-solid.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title) .radio-mark::after {
  background-color: var(--checked-foreground-color) !important;
  position: absolute;
  width: 50%;
  height: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
}
.form-field-survey-fill-solid.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title) .radio-outline {
  width: var(--form-field-survey-size);
  height: var(--form-field-survey-size);
}
.form-field-survey-fill-solid.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title) input[type="radio"]:checked ~ .radio-mark {
  background-color: hsla(var(--checked-background-color-hsl), var(--checked-background-color-alpha-default)) !important;
}
.form-field-survey-fill-solid.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title) input[type="radio"]:checked ~ .radio-mark::after {
  transform: translate(-50%, -50%) scale(1);
}
@media (hover: hover) {
  .form-field-survey-fill-solid.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title):hover input[type="radio"] ~ .radio-mark {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
  .form-field-survey-fill-solid.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title):hover input[type="radio"]:checked ~ .radio-mark {
    background-color: hsla(var(--checked-background-color-hsl), calc(var(--checked-background-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier)))) !important;
  }
}
.form-field-survey-fill-outline.form-field-survey-color-normal .sqs-block-form .form-item.likert {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: 0;
  --checked-background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --checked-background-color-alpha: var(--dynamic-fill-color-a, 1);
  --checked-background-color-alpha-default: 0;
  --checked-foreground-color: var(--dynamic-border-color, #000);
  --hover-alpha-modifier: var(--outline-hover-alpha-modifier);
}
.form-field-survey-fill-outline.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title) input[type="radio"] {
  width: var(--form-field-survey-size);
  height: var(--form-field-survey-size);
  margin-right: 0px;
  cursor: pointer;
}
.form-field-survey-fill-outline.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title) .radio-mark {
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  border: var(--form-field-survey-border-thickness) solid var(--dynamic-border-color, #000);
  width: var(--form-field-survey-size);
  height: var(--form-field-survey-size);
}
.form-field-survey-fill-outline.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title) .radio-mark::after {
  background-color: var(--checked-foreground-color) !important;
  position: absolute;
  width: 50%;
  height: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
}
.form-field-survey-fill-outline.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title) .radio-outline {
  width: var(--form-field-survey-size);
  height: var(--form-field-survey-size);
}
.form-field-survey-fill-outline.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title) input[type="radio"]:checked ~ .radio-mark {
  background-color: hsla(var(--checked-background-color-hsl), var(--checked-background-color-alpha-default)) !important;
}
.form-field-survey-fill-outline.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title) input[type="radio"]:checked ~ .radio-mark::after {
  transform: translate(-50%, -50%) scale(1);
}
@media (hover: hover) {
  .form-field-survey-fill-outline.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title):hover input[type="radio"] ~ .radio-mark {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
  .form-field-survey-fill-outline.form-field-survey-color-normal .sqs-block-form .form-item.likert label:not(.title):hover input[type="radio"]:checked ~ .radio-mark {
    background-color: hsla(var(--checked-background-color-hsl), calc(var(--checked-background-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier)))) !important;
  }
}
.form-field-survey-fill-outline.form-field-survey-color-inverted .sqs-block-form .form-item.likert {
  --background-color-hsl: var(--dynamic-fill-color-hsl, var(--fallback-white-hsl));
  --background-color-alpha: var(--dynamic-fill-color-a, 1);
  --background-color-alpha-default: 0;
  --checked-background-color-hsl: var(--dynamic-border-color-hsl, var(--fallback-black-hsl));
  --checked-background-color-alpha: var(--dynamic-border-color-a, 1);
  --checked-background-color-alpha-default: var(--dynamic-border-color-a, 1);
  --checked-foreground-color: var(--dynamic-fill-color, #FAFAFA);
  --hover-alpha-modifier: var(--outline-hover-alpha-modifier);
  --checked-hover-alpha-modifier: var(--inverted-outline-hover-alpha-modifier);
}
.form-field-survey-fill-outline.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title) input[type="radio"] {
  width: var(--form-field-survey-size);
  height: var(--form-field-survey-size);
  margin-right: 0px;
  cursor: pointer;
}
.form-field-survey-fill-outline.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title) .radio-mark {
  transition: var(--fill-hover-transition);
  background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha-default) * var(--solid-alpha-modifier)));
  border: var(--form-field-survey-border-thickness) solid var(--dynamic-border-color, #000);
  width: var(--form-field-survey-size);
  height: var(--form-field-survey-size);
}
.form-field-survey-fill-outline.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title) .radio-mark::after {
  background-color: var(--checked-foreground-color) !important;
  position: absolute;
  width: 50%;
  height: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
}
.form-field-survey-fill-outline.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title) .radio-outline {
  width: var(--form-field-survey-size);
  height: var(--form-field-survey-size);
}
.form-field-survey-fill-outline.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title) input[type="radio"]:checked ~ .radio-mark {
  background-color: hsla(var(--checked-background-color-hsl), var(--checked-background-color-alpha-default)) !important;
}
.form-field-survey-fill-outline.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title) input[type="radio"]:checked ~ .radio-mark::after {
  transform: translate(-50%, -50%) scale(1);
}
@media (hover: hover) {
  .form-field-survey-fill-outline.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title):hover input[type="radio"] ~ .radio-mark {
    background-color: hsla(var(--background-color-hsl), calc(var(--background-color-alpha) * var(--hover-alpha-modifier)));
  }
  .form-field-survey-fill-outline.form-field-survey-color-inverted .sqs-block-form .form-item.likert label:not(.title):hover input[type="radio"]:checked ~ .radio-mark {
    background-color: hsla(var(--checked-background-color-hsl), calc(var(--checked-background-color-alpha) * var(--checked-hover-alpha-modifier, var(--hover-alpha-modifier)))) !important;
  }
}
/* Form Spacing
=================================================*/
.form-wrapper .react-form-contents .field-list .title {
  padding-bottom: var(--form-label-spacing-bottom);
}
.form-wrapper .react-form-contents .field-list p.description {
  margin-bottom: var(--form-description-spacing-bottom) !important;
}
.form-wrapper .react-form-contents .field-list .description.required {
  padding: 0px;
}
.form-wrapper .react-form-contents .field-list .caption-text:not(:empty) {
  padding-bottom: var(--form-caption-spacing-bottom);
  margin-top: 4px;
}
.form-wrapper .react-form-contents .field-list .address .field .caption-text {
  padding-top: 0px;
}
.form-wrapper .react-form-contents .field-list .address .field:first-of-type .caption-text {
  padding-top: 4px;
}
.form-wrapper .react-form-contents .field-list .field {
  margin: 0 0 var(--form-field-spacing-bottom) !important;
}
.form-wrapper .react-form-contents .field-list .field.likert {
  margin-bottom: 0px !important;
  padding-bottom: 12px;
}
.form-wrapper .react-form-contents .field-list .field.likert .item {
  margin: var(--form-field-spacing-bottom) 0;
}
.form-wrapper .react-form-contents .field-list .field.likert .item:first-of-type {
  margin: calc(var(--form-field-spacing-bottom) / 2) 0;
}
.form-wrapper .react-form-contents .field-list .field.likert .question {
  margin-bottom: 4px;
}
.form-wrapper .react-form-contents .field-list .field .option {
  margin-top: 4px;
}
.form-wrapper .react-form-contents .field-list .field .option label {
  padding: 0px 4px 0px 0px;
}
.form-wrapper .react-form-contents .field-list .field.radio .option,
.form-wrapper .react-form-contents .field-list .field.checkbox .option {
  margin-bottom: 0px;
}
.form-wrapper .react-form-contents .field-list .line-field {
  margin: calc(var(--form-field-spacing-bottom) + 12px) 0 !important;
}
.form-wrapper .react-form-contents .field-list .section {
  padding-bottom: 0px;
  margin: 0px;
}
.form-wrapper .react-form-contents .field-list .fields {
  column-gap: var(--form-field-column-gap);
}
.form-wrapper .react-form-contents .field-list fieldset {
  row-gap: 0px;
}
.form-wrapper .react-form-contents .field-list legend {
  padding: 0 !important;
}
/* Form focus outlines
=================================================*/
input:focus,
select:focus,
textarea:focus,
input[type="radio"]:focus-visible ~ .radio-outline,
.form-field-checkbox-type-button .field.checkbox input[type="checkbox"]:focus-visible + span,
.form-field-radio-type-button input[type="radio"]:focus-visible + .radio-label {
  outline-style: solid;
  outline-color: var(--dynamic-border-color, var(--navigationLinkColor));
  outline-width: 2px;
  outline-offset: 2px !important;
}
/* Form input color
=================================================*/
.form-wrapper .react-form-contents .field-list input:not([type="checkbox"]):not([type="radio"]),
.form-wrapper .react-form-contents .field-list textarea,
.form-wrapper .react-form-contents .field-list select {
  color: var(--dynamic-input-text-color, #000);
}
.form-wrapper .react-form-contents .field-list input::placeholder,
.form-wrapper .react-form-contents .field-list textarea::placeholder,
.form-wrapper .react-form-contents .field-list select.show-placeholder {
  color: hsla(var(--dynamic-input-text-color-hsl, var(--fallback-black-hsl)), calc(var(--dynamic-input-text-color-a, 1) * 0.55));
}
.form-wrapper .react-form-contents .field-list .form-input-prefix {
  color: var(--dynamic-input-text-color, #000);
  opacity: 0.55;
  pointer-events: none;
}
/* Line color (Survey field and Line field)
=================================================*/
.sqs-block-form .form-item.likert .option,
.sqs-block-form .form-item.line-field {
  border-color: var(--dynamic-border-color) !important;
}
/* Form lightbox background color
=================================================*/
.form-use-theme-colors .sqs-modal-lightbox-content .lightbox-inner .lightbox-content.lightbox-form-content-background {
  background-color: var(--tweak-form-block-background-color);
}
.form-use-theme-colors .sqs-modal-lightbox-content .lightbox-inner .lightbox-content.lightbox-form-content-background .sqs-block-form {
  background-color: transparent;
}
.form-use-theme-colors .sqs-modal-lightbox-content .lightbox-inner .lightbox-content.lightbox-form-content-background .form-title,
.form-use-theme-colors .sqs-modal-lightbox-content .lightbox-inner .lightbox-content.lightbox-form-content-background .lightbox-close {
  color: var(--tweak-form-block-title-color-on-background);
}
.form-use-theme-colors .sqs-modal-lightbox-content .lightbox-inner .lightbox-content.lightbox-form-content-background .lightbox-close {
  top: 18px;
  right: 18px;
}
/* Override theme colors for the lightbox form if use-theme-color is toggled off
=================================================*/
body:not(.form-use-theme-colors) .sqs-modal-lightbox-content .lightbox-inner .sqs-block-form {
  --tweak-form-block-title-color-on-background: #000;
  --tweak-form-block-description-color-on-background: #000;
  --tweak-form-block-caption-color-on-background: #000;
  --tweak-form-block-option-color-on-background: #000;
  --tweak-form-block-survey-title-color-on-background: #000;
  --tweak-form-block-button-text-color-on-background: #fff;
  --tweak-form-block-button-background-color-on-background: #000;
  background-color: #fff;
}
.sqs-block-form * {
  -webkit-tap-highlight-color: transparent;
}
/* Override text area height
=================================================*/
.sqs-block-form textarea {
  height: 4em;
}
/* Effects configuration
================================================= */
/* Grid Normalization
================================================= */
.sqs-block-form .field-list {
  /* disable pointer events on non-input radio elements */
}
.sqs-block-form .field-list .field:not(.checkbox, .radio, .likert) {
  display: grid;
  align-content: flex-start;
  align-items: flex-start;
  grid-auto-flow: row;
  grid-template-columns: min-content auto;
  grid-template-rows: auto;
}
.sqs-block-form .field-list .field:not(.checkbox, .radio, .likert) > * {
  grid-column: 1 / span 2;
}
.sqs-block-form .field-list .field:not(.checkbox, .radio, .likert) .form-input-prefix,
.sqs-block-form .field-list .field:not(.checkbox, .radio, .likert) .form-input-effects {
  grid-row: 2 / span 1;
}
.sqs-block-form .field-list .field:not(.checkbox, .radio, .likert) .description ~ .form-input-prefix,
.sqs-block-form .field-list .field:not(.checkbox, .radio, .likert) .description ~ .form-input-effects {
  grid-row: 3;
}
.sqs-block-form .field-list .caption,
.sqs-block-form .field-list .caption > * {
  display: block;
  height: auto;
  word-break: break-word;
}
.sqs-block-form .field-list .caption .caption-text:empty {
  display: none;
}
.sqs-block-form .field-list .radio-label,
.sqs-block-form .field-list .radio-mark,
.sqs-block-form .field-list .radio-outline {
  pointer-events: none;
}
/* Effects base config
================================================= */
.sqs-block-form {
  /* easing variables */
  --fx-ease-out: cubic-bezier(0.33, 1, 0.68, 1);
  /* input fx variables */
  --fx-input-border-width: 0px;
  --fx-input-border-width-increase: 1px;
  --fx-input-border-width-active: 0px;
  --fx-input-border-radius: 0px;
  /* border color variables */
  --fx-border-color-hsl: var(--dynamic-border-color-hsl, var(--fallback-black-hsl));
  --fx-border-color-a: var(--dynamic-border-color-a, 1);
  /* accent color */
  --fx-border-color-accent-hsl: var(--tweak-form-block-field-accent-color-hsl);
  --fx-border-color-accent-a: var(--tweak-form-block-field-accent-color-a);
  /* radio variables */
  --fx-radio-border-width-default: var(--form-field-radio-border-thickness);
  --fx-radio-border-width: var(--fx-radio-border-width-default);
  --fx-radio-border-width-active: var(--fx-radio-border-width-default);
  /* checkbox variables */
  --fx-checkbox-border-width-default: var(--form-field-checkbox-border-thickness);
  --fx-checkbox-border-width: var(--fx-checkbox-border-width-default);
  --fx-checkbox-border-width-active: var(--fx-checkbox-border-width-default);
  /* animation variables */
  --fx-border-transition-duration-border-width: 0.075s;
  --fx-border-transition-transform: transform 0.15s var(--fx-ease-out);
  --fx-border-transition-border-width: border-width var(--fx-border-transition-duration-border-width) var(--fx-ease-out);
  --fx-border-transition-border-color: border-color 0.2s var(--fx-ease-out);
  --fx-border-transition-opacity: opacity 0.2s var(--fx-ease-out);
  --fx-radio-transition-transform: transform 0.1s 0.1s var(--fx-ease-out);
  --fx-checkbox-transition-mask-position: mask-position 0.2s 0.1s var(--fx-ease-out),
    -webkit-mask-position 0.2s 0.1s var(--fx-ease-out);
  /* highlight variables */
  --fx-highlight-input-border-width-increase: 1px;
  --fx-highlight-input-border-width: calc(var(--form-field-border-thickness) + var(--fx-highlight-input-border-width-increase));
  --fx-highlight-input-color: var(--dynamic-border-color, var(--navigationLinkColor));
  --fx-highlight-input-single-trace-animation-duration: 5s;
  --fx-highlight-input-double-trace-animation-duration: 6s;
  --fx-highlight-input-border-bottom-single-trace-animation-duration: 3s;
  --fx-highlight-input-border-bottom-double-trace-animation-duration: 4s;
  --fx-highlight-input-glow-animation-duration: 2.5s;
}
.sqs-block-form.sqs-background-enabled {
  --fx-border-color-accent-hsl: var(--tweak-form-block-field-accent-color-on-background-hsl);
  --fx-border-color-accent-a: var(--tweak-form-block-field-accent-color-on-background-a);
}
.form-item .form-input-effects {
  position: absolute;
  inset: 0 !important;
  pointer-events: none;
  content-visibility: auto;
  display: block;
}
/* Hover/Focus states global config
================================================= */
.form-field-border-all .sqs-block-form {
  --fx-input-border-width: var(--form-field-border-thickness);
  --fx-input-border-width-active: var(--form-field-border-thickness);
}
.form-field-border-bottom .sqs-block-form {
  --fx-input-border-width: 0 0 var(--form-field-border-thickness) 0;
  --fx-input-border-width-active: 0 0 var(--form-field-border-thickness) 0;
}
/* Hover/Focus states opacity config (3 available types - accent, opacity, scale-up)
================================================= */
.form-field-hover-focus-accent,
.form-field-hover-focus-opacity,
.form-field-hover-focus-scale-up {
  /* disable checkbox & radio hover backgrounds when outline */
  /* disable input hover backgrounds when outline */
}
.form-field-hover-focus-accent .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]),
.form-field-hover-focus-opacity .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]),
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]),
.form-field-hover-focus-accent .sqs-block-form .form-item textarea,
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea,
.form-field-hover-focus-accent .sqs-block-form .form-item select,
.form-field-hover-focus-opacity .sqs-block-form .form-item select,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select {
  /* disable input backgrounds */
  /* used to allow border to control background to avoid subpixel rendering issues */
  z-index: 1;
  background: transparent !important;
}
.form-field-hover-focus-accent .sqs-block-form .form-item input,
.form-field-hover-focus-opacity .sqs-block-form .form-item input,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input,
.form-field-hover-focus-accent .sqs-block-form .form-item textarea,
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea,
.form-field-hover-focus-accent .sqs-block-form .form-item select,
.form-field-hover-focus-opacity .sqs-block-form .form-item select,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select {
  border-color: transparent !important;
}
@media (hover: hover) {
  .form-field-hover-focus-accent .sqs-block-form .form-item input:hover ~ *:not(.option),
  .form-field-hover-focus-opacity .sqs-block-form .form-item input:hover ~ *:not(.option),
  .form-field-hover-focus-scale-up .sqs-block-form .form-item input:hover ~ *:not(.option),
  .form-field-hover-focus-accent .sqs-block-form .form-item textarea:hover ~ *:not(.option),
  .form-field-hover-focus-opacity .sqs-block-form .form-item textarea:hover ~ *:not(.option),
  .form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:hover ~ *:not(.option),
  .form-field-hover-focus-accent .sqs-block-form .form-item select:hover ~ *:not(.option),
  .form-field-hover-focus-opacity .sqs-block-form .form-item select:hover ~ *:not(.option),
  .form-field-hover-focus-scale-up .sqs-block-form .form-item select:hover ~ *:not(.option),
  .form-field-hover-focus-accent .sqs-block-form .form-item input:hover[type="checkbox"]::after,
  .form-field-hover-focus-opacity .sqs-block-form .form-item input:hover[type="checkbox"]::after,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item input:hover[type="checkbox"]::after,
  .form-field-hover-focus-accent .sqs-block-form .form-item textarea:hover[type="checkbox"]::after,
  .form-field-hover-focus-opacity .sqs-block-form .form-item textarea:hover[type="checkbox"]::after,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:hover[type="checkbox"]::after,
  .form-field-hover-focus-accent .sqs-block-form .form-item select:hover[type="checkbox"]::after,
  .form-field-hover-focus-opacity .sqs-block-form .form-item select:hover[type="checkbox"]::after,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item select:hover[type="checkbox"]::after,
  .form-field-hover-focus-accent .sqs-block-form .form-item input:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-opacity .sqs-block-form .form-item input:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item input:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-accent .sqs-block-form .form-item textarea:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-opacity .sqs-block-form .form-item textarea:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-accent .sqs-block-form .form-item select:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-opacity .sqs-block-form .form-item select:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item select:hover[type="checkbox"] ~ * {
    --fx-input-border-width: var(--fx-input-border-width-active);
    --fx-radio-border-width: var(--fx-radio-border-width-active);
    --fx-checkbox-border-width: var(--fx-checkbox-border-width-active);
  }
  .form-field-hover-focus-accent .sqs-block-form .form-item input:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-opacity .sqs-block-form .form-item input:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item input:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-accent .sqs-block-form .form-item textarea:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-opacity .sqs-block-form .form-item textarea:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-accent .sqs-block-form .form-item select:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-opacity .sqs-block-form .form-item select:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item select:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-accent .sqs-block-form .form-item input:hover[type="radio"] ~ *,
  .form-field-hover-focus-opacity .sqs-block-form .form-item input:hover[type="radio"] ~ *,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item input:hover[type="radio"] ~ *,
  .form-field-hover-focus-accent .sqs-block-form .form-item textarea:hover[type="radio"] ~ *,
  .form-field-hover-focus-opacity .sqs-block-form .form-item textarea:hover[type="radio"] ~ *,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:hover[type="radio"] ~ *,
  .form-field-hover-focus-accent .sqs-block-form .form-item select:hover[type="radio"] ~ *,
  .form-field-hover-focus-opacity .sqs-block-form .form-item select:hover[type="radio"] ~ *,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item select:hover[type="radio"] ~ *,
  .form-field-hover-focus-accent .sqs-block-form .form-item input:hover[type="checkbox"]::after,
  .form-field-hover-focus-opacity .sqs-block-form .form-item input:hover[type="checkbox"]::after,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item input:hover[type="checkbox"]::after,
  .form-field-hover-focus-accent .sqs-block-form .form-item textarea:hover[type="checkbox"]::after,
  .form-field-hover-focus-opacity .sqs-block-form .form-item textarea:hover[type="checkbox"]::after,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:hover[type="checkbox"]::after,
  .form-field-hover-focus-accent .sqs-block-form .form-item select:hover[type="checkbox"]::after,
  .form-field-hover-focus-opacity .sqs-block-form .form-item select:hover[type="checkbox"]::after,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item select:hover[type="checkbox"]::after,
  .form-field-hover-focus-accent .sqs-block-form .form-item input:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-opacity .sqs-block-form .form-item input:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item input:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-accent .sqs-block-form .form-item textarea:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-opacity .sqs-block-form .form-item textarea:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-accent .sqs-block-form .form-item select:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-opacity .sqs-block-form .form-item select:hover[type="checkbox"] ~ *,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item select:hover[type="checkbox"] ~ * {
    --fx-border-alpha: var(--fx-border-alpha-hover, 1);
  }
}
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus,
.form-field-hover-focus-opacity .sqs-block-form .form-item input:focus,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus,
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus,
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea:focus,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus,
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus,
.form-field-hover-focus-opacity .sqs-block-form .form-item select:focus,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus,
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus-visible,
.form-field-hover-focus-opacity .sqs-block-form .form-item input:focus-visible,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus-visible,
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus-visible,
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea:focus-visible,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus-visible,
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus-visible,
.form-field-hover-focus-opacity .sqs-block-form .form-item select:focus-visible,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus-visible,
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus[type="radio"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item input:focus[type="radio"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus[type="radio"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus[type="radio"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea:focus[type="radio"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus[type="radio"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus[type="radio"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item select:focus[type="radio"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus[type="radio"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus-visible[type="radio"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item input:focus-visible[type="radio"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus-visible[type="radio"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus-visible[type="radio"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea:focus-visible[type="radio"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus-visible[type="radio"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus-visible[type="radio"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item select:focus-visible[type="radio"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus-visible[type="radio"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus[type="checkbox"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item input:focus[type="checkbox"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus[type="checkbox"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus[type="checkbox"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea:focus[type="checkbox"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus[type="checkbox"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus[type="checkbox"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item select:focus[type="checkbox"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus[type="checkbox"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus-visible[type="checkbox"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item input:focus-visible[type="checkbox"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus-visible[type="checkbox"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus-visible[type="checkbox"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea:focus-visible[type="checkbox"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus-visible[type="checkbox"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus-visible[type="checkbox"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item select:focus-visible[type="checkbox"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus-visible[type="checkbox"] ~ * {
  outline-color: transparent !important;
}
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus ~ *:not(.option),
.form-field-hover-focus-opacity .sqs-block-form .form-item input:focus ~ *:not(.option),
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus ~ *:not(.option),
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus ~ *:not(.option),
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea:focus ~ *:not(.option),
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus ~ *:not(.option),
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus ~ *:not(.option),
.form-field-hover-focus-opacity .sqs-block-form .form-item select:focus ~ *:not(.option),
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus ~ *:not(.option),
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus[type="checkbox"]::after,
.form-field-hover-focus-opacity .sqs-block-form .form-item input:focus[type="checkbox"]::after,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus[type="checkbox"]::after,
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus[type="checkbox"]::after,
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea:focus[type="checkbox"]::after,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus[type="checkbox"]::after,
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus[type="checkbox"]::after,
.form-field-hover-focus-opacity .sqs-block-form .form-item select:focus[type="checkbox"]::after,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus[type="checkbox"]::after,
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus[type="checkbox"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item input:focus[type="checkbox"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus[type="checkbox"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus[type="checkbox"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea:focus[type="checkbox"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus[type="checkbox"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus[type="checkbox"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item select:focus[type="checkbox"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus[type="checkbox"] ~ * {
  --fx-input-border-width: var(--fx-input-border-width-active);
  --fx-radio-border-width: var(--fx-radio-border-width-active);
  --fx-checkbox-border-width: var(--fx-checkbox-border-width-active);
}
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity .sqs-block-form .form-item input:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity .sqs-block-form .form-item select:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus[type="radio"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item input:focus[type="radio"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus[type="radio"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus[type="radio"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea:focus[type="radio"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus[type="radio"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus[type="radio"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item select:focus[type="radio"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus[type="radio"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus[type="checkbox"]::after,
.form-field-hover-focus-opacity .sqs-block-form .form-item input:focus[type="checkbox"]::after,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus[type="checkbox"]::after,
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus[type="checkbox"]::after,
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea:focus[type="checkbox"]::after,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus[type="checkbox"]::after,
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus[type="checkbox"]::after,
.form-field-hover-focus-opacity .sqs-block-form .form-item select:focus[type="checkbox"]::after,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus[type="checkbox"]::after,
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus[type="checkbox"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item input:focus[type="checkbox"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus[type="checkbox"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus[type="checkbox"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item textarea:focus[type="checkbox"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus[type="checkbox"] ~ *,
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus[type="checkbox"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item select:focus[type="checkbox"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus[type="checkbox"] ~ * {
  --fx-border-alpha: var(--fx-border-alpha-focus, 1);
}
.form-field-hover-focus-accent .sqs-block-form .form-item .radio-mark,
.form-field-hover-focus-opacity .sqs-block-form .form-item .radio-mark,
.form-field-hover-focus-scale-up .sqs-block-form .form-item .radio-mark {
  border-width: var(--fx-radio-border-width-active) !important;
  border-color: transparent !important;
}
.form-field-hover-focus-accent .sqs-block-form .form-item .radio-mark::before,
.form-field-hover-focus-opacity .sqs-block-form .form-item .radio-mark::before,
.form-field-hover-focus-scale-up .sqs-block-form .form-item .radio-mark::before {
  position: absolute;
  border-style: solid;
  border-width: var(--fx-radio-border-width);
  border-color: hsla(var(--fx-border-color-hsl), calc(var(--fx-border-color-a) * var(--fx-border-alpha, 1)));
  border-radius: inherit;
  content: "";
  inset: calc(var(--fx-radio-border-width-active) * -1);
  transition: var(--fx-border-transition-border-width), var(--fx-border-transition-border-color), var(--fx-border-transition-transform);
}
.form-field-hover-focus-accent .sqs-block-form .form-item .radio-mark::after,
.form-field-hover-focus-opacity .sqs-block-form .form-item .radio-mark::after,
.form-field-hover-focus-scale-up .sqs-block-form .form-item .radio-mark::after {
  transition: var(--fx-radio-transition-transform);
}
.form-field-hover-focus-accent .sqs-block-form .form-item input[type="checkbox"],
.form-field-hover-focus-opacity .sqs-block-form .form-item input[type="checkbox"],
.form-field-hover-focus-scale-up .sqs-block-form .form-item input[type="checkbox"] {
  border-width: var(--fx-checkbox-border-width-active) !important;
  overflow: visible;
}
.form-field-hover-focus-accent .sqs-block-form .form-item input[type="checkbox"]::before,
.form-field-hover-focus-opacity .sqs-block-form .form-item input[type="checkbox"]::before,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input[type="checkbox"]::before {
  -webkit-mask-image: linear-gradient(90deg, transparent 0 50%, #fff 50%);
  -webkit-mask-position: 0 0;
  -webkit-mask-size: 200% 100%;
  mask-image: linear-gradient(90deg, transparent 0 50%, #fff 50%);
  mask-position: 0 0;
  mask-size: 200% 100%;
  transform: scale(1);
  transition: var(--fx-checkbox-transition-mask-position);
}
.form-field-hover-focus-accent .sqs-block-form .form-item input[type="checkbox"]::after,
.form-field-hover-focus-opacity .sqs-block-form .form-item input[type="checkbox"]::after,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input[type="checkbox"]::after {
  position: absolute;
  border: solid var(--fx-checkbox-border-width) hsla(var(--fx-border-color-hsl), calc(var(--fx-border-color-a) * var(--fx-border-alpha, 1)));
  border-radius: inherit;
  content: "";
  inset: calc(var(--fx-checkbox-border-width-active) * -1);
  transition: var(--fx-border-transition-border-width), var(--fx-border-transition-border-color), var(--fx-border-transition-transform);
}
.form-field-hover-focus-accent .sqs-block-form .form-item input[type="checkbox"]:checked::before,
.form-field-hover-focus-opacity .sqs-block-form .form-item input[type="checkbox"]:checked::before,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input[type="checkbox"]:checked::before {
  -webkit-mask-position: -100% 0;
  mask-position: -100% 0;
}
.form-field-hover-focus-accent .sqs-block-form .form-item .form-input-effects-border,
.form-field-hover-focus-opacity .sqs-block-form .form-item .form-input-effects-border,
.form-field-hover-focus-scale-up .sqs-block-form .form-item .form-input-effects-border {
  /* style the border effect node */
  position: absolute;
  border-radius: var(--fx-input-border-radius);
  inset: 0;
  border-width: var(--fx-input-border-width);
  border-style: solid;
  border-color: hsla(var(--fx-border-color-hsl), calc(var(--fx-border-color-a) * var(--fx-border-alpha, 1)));
  transition: var(--fx-border-transition-border-width), var(--fx-border-transition-border-color), var(--fill-hover-transition);
}
.form-field-hover-focus-accent.form-field-radio-type-button .sqs-block-form .form-item .radio-label,
.form-field-hover-focus-opacity.form-field-radio-type-button .sqs-block-form .form-item .radio-label,
.form-field-hover-focus-scale-up.form-field-radio-type-button .sqs-block-form .form-item .radio-label {
  position: relative;
  border-color: transparent !important;
}
.form-field-hover-focus-accent.form-field-radio-type-button .sqs-block-form .form-item .radio-label::before,
.form-field-hover-focus-opacity.form-field-radio-type-button .sqs-block-form .form-item .radio-label::before,
.form-field-hover-focus-scale-up.form-field-radio-type-button .sqs-block-form .form-item .radio-label::before {
  position: absolute;
  border: solid var(--fx-radio-border-width) hsla(var(--fx-border-color-hsl), calc(var(--fx-border-color-a) * var(--fx-border-alpha, 1)));
  border-radius: inherit;
  content: "";
  inset: calc(var(--fx-radio-border-width-default) * -1);
  transition: var(--fx-border-transition-border-width), var(--fx-border-transition-border-color), var(--fx-border-transition-transform);
}
.form-field-hover-focus-accent.form-field-checkbox-type-button .sqs-block-form .form-item.checkbox label span,
.form-field-hover-focus-opacity.form-field-checkbox-type-button .sqs-block-form .form-item.checkbox label span,
.form-field-hover-focus-scale-up.form-field-checkbox-type-button .sqs-block-form .form-item.checkbox label span {
  position: relative;
  border-color: transparent !important;
}
.form-field-hover-focus-accent.form-field-checkbox-type-button .sqs-block-form .form-item.checkbox label span::before,
.form-field-hover-focus-opacity.form-field-checkbox-type-button .sqs-block-form .form-item.checkbox label span::before,
.form-field-hover-focus-scale-up.form-field-checkbox-type-button .sqs-block-form .form-item.checkbox label span::before {
  position: absolute;
  border: solid var(--fx-checkbox-border-width) hsla(var(--fx-border-color-hsl), calc(var(--fx-border-color-a) * var(--fx-border-alpha, 1)));
  border-radius: inherit;
  content: "";
  inset: calc(var(--fx-checkbox-border-width-default) * -1);
  transition: var(--fx-border-transition-border-width), var(--fx-border-transition-border-color), var(--fx-border-transition-transform);
}
.form-field-hover-focus-accent.form-field-checkbox-fill-outline .sqs-block-form .form-item.checkbox,
.form-field-hover-focus-opacity.form-field-checkbox-fill-outline .sqs-block-form .form-item.checkbox,
.form-field-hover-focus-scale-up.form-field-checkbox-fill-outline .sqs-block-form .form-item.checkbox,
.form-field-hover-focus-accent.form-field-checkbox-fill-outline .sqs-block-form .form-item.email,
.form-field-hover-focus-opacity.form-field-checkbox-fill-outline .sqs-block-form .form-item.email,
.form-field-hover-focus-scale-up.form-field-checkbox-fill-outline .sqs-block-form .form-item.email,
.form-field-hover-focus-accent.form-field-radio-fill-outline .sqs-block-form .form-item.radio,
.form-field-hover-focus-opacity.form-field-radio-fill-outline .sqs-block-form .form-item.radio,
.form-field-hover-focus-scale-up.form-field-radio-fill-outline .sqs-block-form .form-item.radio,
.form-field-hover-focus-accent.form-field-survey-fill-outline .sqs-block-form .form-item.likert,
.form-field-hover-focus-opacity.form-field-survey-fill-outline .sqs-block-form .form-item.likert,
.form-field-hover-focus-scale-up.form-field-survey-fill-outline .sqs-block-form .form-item.likert {
  --outline-hover-alpha-modifier: 0;
  --outline-focus-alpha-modifier: 0;
}
.form-field-hover-focus-accent.form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]) ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity.form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]) ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]) ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-accent.form-field-style-solid .sqs-block-form .form-item textarea ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity.form-field-style-solid .sqs-block-form .form-item textarea ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-solid .sqs-block-form .form-item textarea ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-accent.form-field-style-solid .sqs-block-form .form-item select ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity.form-field-style-solid .sqs-block-form .form-item select ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-solid .sqs-block-form .form-item select ~ .form-input-effects .form-input-effects-border {
  background-color: hsla(var(--dynamic-fill-color-hsl, var(--fallback-white-hsl)), calc(var(--dynamic-fill-color-a, 1) * var(--solid-alpha-modifier)));
}
@media (hover: hover) {
  .form-field-hover-focus-accent.form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-opacity.form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-scale-up.form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-accent.form-field-style-solid .sqs-block-form .form-item textarea:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-opacity.form-field-style-solid .sqs-block-form .form-item textarea:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-scale-up.form-field-style-solid .sqs-block-form .form-item textarea:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-accent.form-field-style-solid .sqs-block-form .form-item select:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-opacity.form-field-style-solid .sqs-block-form .form-item select:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-scale-up.form-field-style-solid .sqs-block-form .form-item select:hover ~ .form-input-effects .form-input-effects-border {
    background-color: hsla(var(--dynamic-fill-color-hsl, var(--fallback-white-hsl)), calc(var(--dynamic-fill-color-a, 1) * var(--solid-hover-alpha-modifier)));
  }
}
.form-field-hover-focus-accent.form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity.form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-accent.form-field-style-solid .sqs-block-form .form-item textarea:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity.form-field-style-solid .sqs-block-form .form-item textarea:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-solid .sqs-block-form .form-item textarea:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-accent.form-field-style-solid .sqs-block-form .form-item select:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity.form-field-style-solid .sqs-block-form .form-item select:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-solid .sqs-block-form .form-item select:focus ~ .form-input-effects .form-input-effects-border {
  background-color: hsla(var(--dynamic-fill-color-hsl, var(--fallback-white-hsl)), calc(var(--dynamic-fill-color-a, 1) * var(--solid-focus-alpha-modifier)));
}
.form-field-hover-focus-accent.form-field-style-outline .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]) ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity.form-field-style-outline .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]) ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-outline .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]) ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-accent.form-field-style-outline .sqs-block-form .form-item textarea ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity.form-field-style-outline .sqs-block-form .form-item textarea ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-outline .sqs-block-form .form-item textarea ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-accent.form-field-style-outline .sqs-block-form .form-item select ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity.form-field-style-outline .sqs-block-form .form-item select ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-outline .sqs-block-form .form-item select ~ .form-input-effects .form-input-effects-border {
  --outline-hover-alpha-modifier: 0;
  --outline-focus-alpha-modifier: 0;
  background-color: hsla(var(--dynamic-fill-color-hsl, var(--fallback-white-hsl)), 0);
}
@media (hover: hover) {
  .form-field-hover-focus-accent.form-field-style-outline .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-opacity.form-field-style-outline .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-scale-up.form-field-style-outline .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-accent.form-field-style-outline .sqs-block-form .form-item textarea:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-opacity.form-field-style-outline .sqs-block-form .form-item textarea:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-scale-up.form-field-style-outline .sqs-block-form .form-item textarea:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-accent.form-field-style-outline .sqs-block-form .form-item select:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-opacity.form-field-style-outline .sqs-block-form .form-item select:hover ~ .form-input-effects .form-input-effects-border,
  .form-field-hover-focus-scale-up.form-field-style-outline .sqs-block-form .form-item select:hover ~ .form-input-effects .form-input-effects-border {
    background-color: hsla(var(--dynamic-fill-color-hsl, var(--fallback-white-hsl)), calc(var(--dynamic-fill-color-a, 1) * var(--outline-hover-alpha-modifier)));
  }
}
.form-field-hover-focus-accent.form-field-style-outline .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity.form-field-style-outline .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-outline .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]):focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-accent.form-field-style-outline .sqs-block-form .form-item textarea:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity.form-field-style-outline .sqs-block-form .form-item textarea:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-outline .sqs-block-form .form-item textarea:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-accent.form-field-style-outline .sqs-block-form .form-item select:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity.form-field-style-outline .sqs-block-form .form-item select:focus ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-outline .sqs-block-form .form-item select:focus ~ .form-input-effects .form-input-effects-border {
  background-color: hsla(var(--dynamic-fill-color-hsl, var(--fallback-white-hsl)), calc(var(--dynamic-fill-color-a, 1) * var(--outline-focus-alpha-modifier)));
}
/* Border thickness increase effect
================================================= */
.form-field-hover-focus-accent .sqs-block-form,
.form-field-hover-focus-opacity .sqs-block-form {
  --fx-input-border-width-active: var(--fx-input-border-width-increase);
  --fx-radio-border-width-active: calc(var(--fx-radio-border-width-default) + var(--fx-input-border-width-increase));
  --fx-checkbox-border-width-active: calc(var(--fx-checkbox-border-width-default) + var(--fx-input-border-width-increase));
}
.form-field-hover-focus-accent.form-field-border-all .sqs-block-form,
.form-field-hover-focus-opacity.form-field-border-all .sqs-block-form {
  --fx-input-border-width-active: calc(var(--form-field-border-thickness) + var(--fx-input-border-width-increase));
}
.form-field-hover-focus-accent.form-field-border-bottom .sqs-block-form,
.form-field-hover-focus-opacity.form-field-border-bottom .sqs-block-form {
  --fx-input-border-width-active: 0 0 calc(var(--form-field-border-thickness) + var(--fx-input-border-width-increase)) 0;
}
/* Opacity change effect
================================================= */
.form-field-hover-focus-opacity .sqs-block-form .form-item .form-input-effects-border,
.form-field-hover-focus-scale-up .sqs-block-form .form-item .form-input-effects-border,
.form-field-hover-focus-opacity .sqs-block-form .form-item input[type="radio"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input[type="radio"] ~ *,
.form-field-hover-focus-opacity .sqs-block-form .form-item input[type="checkbox"]::after,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input[type="checkbox"]::after,
.form-field-hover-focus-opacity .sqs-block-form .form-item input[type="checkbox"] ~ *,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input[type="checkbox"] ~ * {
  --fx-border-alpha: 0.55;
  --fx-border-alpha-hover: 0.55;
  --fx-border-alpha-focus: 1;
}
.form-field-hover-focus-opacity.form-field-border-none .sqs-block-form .form-item .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-border-none .sqs-block-form .form-item .form-input-effects-border {
  --fx-border-alpha: 0;
  --fx-border-alpha-hover: 0.55;
  --fx-border-alpha-focus: 1;
}
.form-field-hover-focus-opacity.form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]) ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]) ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity.form-field-style-solid .sqs-block-form .form-item textarea ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-solid .sqs-block-form .form-item textarea ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-opacity.form-field-style-solid .sqs-block-form .form-item select ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-scale-up.form-field-style-solid .sqs-block-form .form-item select ~ .form-input-effects .form-input-effects-border {
  --solid-alpha-modifier: 0.8;
  --solid-hover-alpha-modifier: 1;
}
.form-field-hover-focus-opacity.form-field-checkbox-fill-solid .sqs-block-form .form-item.checkbox,
.form-field-hover-focus-scale-up.form-field-checkbox-fill-solid .sqs-block-form .form-item.checkbox,
.form-field-hover-focus-opacity.form-field-checkbox-fill-solid .sqs-block-form .form-item.email,
.form-field-hover-focus-scale-up.form-field-checkbox-fill-solid .sqs-block-form .form-item.email,
.form-field-hover-focus-opacity.form-field-radio-fill-solid .sqs-block-form .form-item.radio,
.form-field-hover-focus-scale-up.form-field-radio-fill-solid .sqs-block-form .form-item.radio,
.form-field-hover-focus-opacity.form-field-survey-fill-solid .sqs-block-form .form-item.likert,
.form-field-hover-focus-scale-up.form-field-survey-fill-solid .sqs-block-form .form-item.likert {
  --solid-alpha-modifier: 0.8;
  --solid-hover-alpha-modifier: 1;
}
/* Accent color effect (transform)
================================================= */
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus ~ *:not(.option),
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus ~ *:not(.option),
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus ~ *:not(.option),
.form-field-hover-focus-accent .sqs-block-form .form-item input:focus[type="checkbox"]::after,
.form-field-hover-focus-accent .sqs-block-form .form-item textarea:focus[type="checkbox"]::after,
.form-field-hover-focus-accent .sqs-block-form .form-item select:focus[type="checkbox"]::after {
  --fx-border-color-hsl: var(--fx-border-color-accent-hsl);
  --fx-border-color-a: var(--fx-border-color-accent-a);
}
.form-field-hover-focus-accent.form-field-border-none .sqs-block-form .form-item .form-input-effects-border {
  --fx-border-alpha: 0;
}
.form-field-hover-focus-accent.form-field-style-solid .sqs-block-form .form-item input:not([type="checkbox"]):not([type="radio"]) ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-accent.form-field-style-solid .sqs-block-form .form-item textarea ~ .form-input-effects .form-input-effects-border,
.form-field-hover-focus-accent.form-field-style-solid .sqs-block-form .form-item select ~ .form-input-effects .form-input-effects-border {
  --solid-alpha-modifier: 1;
  --solid-hover-alpha-modifier: 1;
}
.form-field-hover-focus-accent.form-field-checkbox-fill-solid .sqs-block-form .form-item.checkbox,
.form-field-hover-focus-accent.form-field-checkbox-fill-solid .sqs-block-form .form-item.email,
.form-field-hover-focus-accent.form-field-radio-fill-solid .sqs-block-form .form-item.radio,
.form-field-hover-focus-accent.form-field-survey-fill-solid .sqs-block-form .form-item.likert {
  --solid-alpha-modifier: 1;
  --solid-hover-alpha-modifier: 1;
}
/* Scale up effect (transform)
================================================= */
.form-field-hover-focus-scale-up .sqs-block-form .form-item {
  --inverted-solid-hover-alpha-modifier: 1;
  --inverted-outline-hover-alpha-modifier: 1;
}
.form-field-hover-focus-scale-up .sqs-block-form .form-item .form-input-effects {
  transition: var(--fx-border-transition-transform);
}
.form-field-hover-focus-scale-up .sqs-block-form .form-item input,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select {
  transition: var(--fill-hover-transition), var(--fx-border-transition-transform) !important;
}
@media (hover: hover) {
  .form-field-hover-focus-scale-up .sqs-block-form .form-item input:hover ~ .form-input-effects,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:hover ~ .form-input-effects,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item select:hover ~ .form-input-effects {
    transform: scale(1.025);
  }
  .form-field-hover-focus-scale-up .sqs-block-form .form-item input:hover ~ .radio-mark,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:hover ~ .radio-mark,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item select:hover ~ .radio-mark,
  .form-field-hover-focus-scale-up .sqs-block-form .form-item input:hover[type="checkbox"],
  .form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:hover[type="checkbox"],
  .form-field-hover-focus-scale-up .sqs-block-form .form-item select:hover[type="checkbox"] {
    transform: scale(1.1);
  }
}
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus ~ .form-input-effects,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus ~ .form-input-effects,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus ~ .form-input-effects {
  transform: scale(1.025);
}
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus-visible ~ .radio-mark,
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus-visible ~ .radio-mark,
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus-visible ~ .radio-mark,
.form-field-hover-focus-scale-up .sqs-block-form .form-item input:focus-visible[type="checkbox"],
.form-field-hover-focus-scale-up .sqs-block-form .form-item textarea:focus-visible[type="checkbox"],
.form-field-hover-focus-scale-up .sqs-block-form .form-item select:focus-visible[type="checkbox"] {
  transform: scale(1.1);
}
.form-field-hover-focus-scale-up.form-field-checkbox-type-button .sqs-block-form .form-item:not(.email) input[type="checkbox"] ~ span {
  transition: var(--fill-hover-transition), var(--fx-border-transition-transform) !important;
}
@media (hover: hover) {
  .form-field-hover-focus-scale-up.form-field-checkbox-type-button .sqs-block-form .form-item:not(.email) input[type="checkbox"]:hover ~ span {
    transform: scale(1.05);
  }
}
.form-field-hover-focus-scale-up.form-field-checkbox-type-button .sqs-block-form .form-item:not(.email) input[type="checkbox"]:focus-visible ~ span {
  transform: scale(1.05);
}
.form-field-hover-focus-scale-up.form-field-radio-type-button .sqs-block-form .form-item input[type="radio"] ~ .radio-label {
  transition: var(--fill-hover-transition), var(--fx-border-transition-transform) !important;
}
@media (hover: hover) {
  .form-field-hover-focus-scale-up.form-field-radio-type-button .sqs-block-form .form-item input[type="radio"]:hover ~ .radio-label {
    transform: scale(1.05);
  }
}
.form-field-hover-focus-scale-up.form-field-radio-type-button .sqs-block-form .form-item input[type="radio"]:focus-visible ~ .radio-label {
  transform: scale(1.05);
}
/* Highlight base config (3 available types - single-trace, double-trace, glow)
================================================= */
.sqs-block-form .field-list {
  /* target valid first fields */
}
.sqs-block-form .field-list .form-input-effects-highlight {
  position: absolute;
  display: none;
  border-radius: var(--fx-input-border-radius);
  inset: 0;
  transition: var(--fx-border-transition-opacity);
}
.sqs-block-form .field-list > .form-item.field:first-child input[value=""] ~ .form-input-effects .form-input-effects-highlight,
.sqs-block-form .field-list > .form-item.field:first-child select.show-placeholder ~ .form-input-effects .form-input-effects-highlight,
.sqs-block-form .field-list > .form-item.field:first-child textarea:empty ~ .form-input-effects .form-input-effects-highlight,
.sqs-block-form .field-list > .form-item.fields.name:first-child > .field:first-of-type input[value=""] ~ .form-input-effects .form-input-effects-highlight,
.sqs-block-form .field-list > .form-item.fields.address:first-child > .field:nth-of-type(3) input[value=""] ~ .form-input-effects .form-input-effects-highlight {
  display: block;
  --fx-highlight-input-animation-play-state: running;
}
@media (prefers-reduced-motion) {
  .sqs-block-form .field-list > .form-item.field:first-child input[value=""] ~ .form-input-effects .form-input-effects-highlight,
  .sqs-block-form .field-list > .form-item.field:first-child select.show-placeholder ~ .form-input-effects .form-input-effects-highlight,
  .sqs-block-form .field-list > .form-item.field:first-child textarea:empty ~ .form-input-effects .form-input-effects-highlight,
  .sqs-block-form .field-list > .form-item.fields.name:first-child > .field:first-of-type input[value=""] ~ .form-input-effects .form-input-effects-highlight,
  .sqs-block-form .field-list > .form-item.fields.address:first-child > .field:nth-of-type(3) input[value=""] ~ .form-input-effects .form-input-effects-highlight {
    display: none;
    --fx-highlight-input-animation-play-state: paused;
  }
}
.sqs-block-form .field-list > .form-item.field:first-child input[value=""]:focus ~ .form-input-effects .form-input-effects-highlight,
.sqs-block-form .field-list > .form-item.field:first-child select.show-placeholder:focus ~ .form-input-effects .form-input-effects-highlight,
.sqs-block-form .field-list > .form-item.field:first-child textarea:empty:focus ~ .form-input-effects .form-input-effects-highlight,
.sqs-block-form .field-list > .form-item.fields.name:first-child > .field:first-of-type input[value=""]:focus ~ .form-input-effects .form-input-effects-highlight,
.sqs-block-form .field-list > .form-item.fields.address:first-child > .field:nth-of-type(3) input[value=""]:focus ~ .form-input-effects .form-input-effects-highlight {
  opacity: 0;
}
/* Single & Double Trace Highlight Styles
================================================= */
@keyframes animation-form-field-fx-highlight-trace {
  0% {
    left: 50%;
    transform: translate3d(-50%, -50%, 0) scaleY(1) scaleX(3) rotate(0deg);
  }
  25% {
    left: min(50%, 2.5em);
    transform: translate3d(-50%, -50%, 0) scaleY(1.5) scaleX(1) rotate(90deg);
  }
  50% {
    transform: translate3d(-50%, -50%, 0) scaleY(1) scaleX(3) rotate(180deg);
  }
  75% {
    left: calc(100% - min(2.5em, 50%));
    transform: translate3d(-50%, -50%, 0) scaleY(1.5) scaleX(1) rotate(270deg);
  }
  100% {
    left: 50%;
    transform: translate3d(-50%, -50%, 0) scaleY(1) scaleX(3) rotate(360deg);
  }
}
/* Note: Separate namespace for double trace so animation progress is not retained on switch */
@keyframes animation-form-field-fx-highlight-double-trace {
  0% {
    left: 50%;
    transform: translate3d(-50%, -50%, 0) scaleY(1) scaleX(3) rotate(0deg);
  }
  25% {
    left: min(50%, 2.5em);
    transform: translate3d(-50%, -50%, 0) scaleY(1.5) scaleX(1) rotate(90deg);
  }
  50% {
    transform: translate3d(-50%, -50%, 0) scaleY(1) scaleX(3) rotate(180deg);
  }
  75% {
    left: calc(100% - min(2.5em, 50%));
    transform: translate3d(-50%, -50%, 0) scaleY(1.5) scaleX(1) rotate(270deg);
  }
  100% {
    left: 50%;
    transform: translate3d(-50%, -50%, 0) scaleY(1) scaleX(3) rotate(360deg);
  }
}
@keyframes animation-form-field-fx-highlight-trace-bottom {
  0% {
    left: calc(100% + 2.5em);
    transform: translate3d(-50%, -50%, 0) scaleY(1) scaleX(3) rotate(0deg);
  }
  100% {
    left: -2.5em;
    transform: translate3d(-50%, -50%, 0) scaleY(1) scaleX(3) rotate(0deg);
  }
}
/* Note: Separate namespace for double trace so animation progress is not retained on switch */
@keyframes animation-form-field-fx-highlight-double-trace-bottom {
  0% {
    left: calc(100% + 2.5em);
    transform: translate3d(-50%, -50%, 0) scaleY(1) scaleX(3) rotate(0deg);
  }
  100% {
    left: -2.5em;
    transform: translate3d(-50%, -50%, 0) scaleY(1) scaleX(3) rotate(0deg);
  }
}
.form-field-border-all .sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-single-trace,
.form-field-border-none .sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-single-trace,
.form-field-border-all .sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-double-trace,
.form-field-border-none .sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-double-trace {
  --fx-animation-form-field-fx-highlight-trace: animation-form-field-fx-highlight-trace;
  --fx-animation-form-field-fx-highlight-double-trace: animation-form-field-fx-highlight-double-trace;
  /* Generates a masked border when all sides */
  overflow: hidden;
  padding: var(--fx-highlight-input-border-width);
  border: solid 0 transparent;
  inset: 0;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  transition: padding var(--fx-border-transition-duration-border-width) var(--fx-ease-out), var(--fx-border-transition-opacity);
}
.form-field-border-bottom .sqs-block-form {
  /* use the border-bottom animation duration */
  --fx-highlight-input-single-trace-animation-duration: var(--fx-highlight-input-border-bottom-single-trace-animation-duration);
  --fx-highlight-input-double-trace-animation-duration: var(--fx-highlight-input-border-bottom-double-trace-animation-duration);
  /* generates a clipped border-bottom while retaining the standard field size */
}
.form-field-border-bottom .sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-single-trace,
.form-field-border-bottom .sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-double-trace {
  --fx-animation-form-field-fx-highlight-trace: animation-form-field-fx-highlight-trace-bottom;
  --fx-animation-form-field-fx-highlight-double-trace: animation-form-field-fx-highlight-double-trace-bottom;
  overflow: hidden;
  clip-path: polygon(0% calc(100% - var(--fx-highlight-input-border-width)), 100% calc(100% - var(--fx-highlight-input-border-width)), 100% 100%, 0% 100%);
  inset: 0;
  transition: clip-path var(--fx-border-transition-duration-border-width) var(--fx-ease-out), var(--fx-border-transition-opacity);
}
.form-field-border-bottom .sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-single-trace::before,
.form-field-border-bottom .sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-double-trace::before,
.form-field-border-bottom .sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-single-trace::after,
.form-field-border-bottom .sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-double-trace::after {
  animation-direction: reverse;
}
.sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-single-trace::before {
  position: absolute;
  top: 50%;
  left: 50%;
  height: max(20em, 300%);
  aspect-ratio: 1 / 1;
  background: transparent;
  background: conic-gradient(from 0deg at 50% 50%, transparent 40%, var(--fx-highlight-input-color), transparent 60%);
  content: "";
  mask-image: linear-gradient(to right, transparent 40%, var(--fx-highlight-input-color), transparent 60%);
  -webkit-mask-image: linear-gradient(to right, transparent 40%, var(--fx-highlight-input-color), transparent 60%);
  transform: translate3d(-50%, -50%, 0) rotate(0deg);
  transform-origin: 50% 50%;
  animation: var(--fx-highlight-input-single-trace-animation-duration) linear infinite var(--fx-animation-form-field-fx-highlight-trace);
  animation-play-state: var(--fx-highlight-input-animation-play-state, paused);
}
.sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-double-trace::before,
.sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-double-trace::after {
  position: absolute;
  top: 50%;
  left: 50%;
  height: max(20em, 300%);
  aspect-ratio: 1 / 1;
  background: transparent;
  background: conic-gradient(from 0deg at 50% 50%, transparent 40%, var(--fx-highlight-input-color), transparent 60%);
  content: "";
  mask-image: linear-gradient(to right, transparent 40%, var(--fx-highlight-input-color), transparent 60%);
  -webkit-mask-image: linear-gradient(to right, transparent 40%, var(--fx-highlight-input-color), transparent 60%);
  transform: translate3d(-50%, -50%, 0) rotate(0deg);
  transform-origin: 50% 50%;
}
.sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-double-trace::before {
  animation: var(--fx-highlight-input-double-trace-animation-duration) linear infinite var(--fx-animation-form-field-fx-highlight-double-trace);
  animation-play-state: var(--fx-highlight-input-animation-play-state, paused);
}
.sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-double-trace::after {
  animation: var(--fx-highlight-input-double-trace-animation-duration) calc(var(--fx-highlight-input-double-trace-animation-duration) * -0.5) linear infinite var(--fx-animation-form-field-fx-highlight-double-trace);
  animation-play-state: var(--fx-highlight-input-animation-play-state, paused);
}
/* Glow Highlight Styles
================================================= */
@keyframes animation-form-field-fx-highlight-glow {
  0% {
    opacity: 0.1;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.1;
  }
}
.sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-glow::before {
  position: absolute;
  border: solid var(--fx-highlight-input-border-width) var(--fx-highlight-input-color);
  border-radius: inherit;
  animation: var(--fx-highlight-input-glow-animation-duration) linear infinite animation-form-field-fx-highlight-glow;
  animation-play-state: var(--fx-highlight-input-animation-play-state, paused);
  content: "";
  inset: 0;
}
.form-field-border-bottom .sqs-block-form .form-item .form-input-effects-highlight.form-field-highlight-glow::before {
  border-width: 0 0 var(--fx-highlight-input-border-width);
}
@keyframes shake-three {
  0% {
    transform: translate3d(0, 0, 0);
  }
  16.666% {
    transform: translate3d(0.333rem, 0, 0);
  }
  33.333% {
    transform: translate3d(-0.333rem, 0, 0);
  }
  50% {
    transform: translate3d(0.333rem, 0, 0);
  }
  66.666% {
    transform: translate3d(-0.333rem, 0, 0);
  }
  83.333% {
    transform: translate3d(0.333rem, 0, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
.form-submit-button-state {
  display: none;
}
.form-submit-button-style-spinner .form-submit-button-label,
.form-submit-button-style-ellipsis .form-submit-button-label,
.form-submit-button-style-bar .form-submit-button-label,
.form-submit-button-style-circle .form-submit-button-label {
  display: none;
}
.form-submit-button-style-spinner .form-submit-button.error,
.form-submit-button-style-ellipsis .form-submit-button.error,
.form-submit-button-style-bar .form-submit-button.error,
.form-submit-button-style-circle .form-submit-button.error {
  animation: shake-three 0.4s cubic-bezier(0.37, 0, 0.63, 1) forwards;
}
.form-submit-button-style-spinner .form-submit-button.none .form-submit-button-state > :nth-child(1),
.form-submit-button-style-ellipsis .form-submit-button.none .form-submit-button-state > :nth-child(1),
.form-submit-button-style-bar .form-submit-button.none .form-submit-button-state > :nth-child(1),
.form-submit-button-style-circle .form-submit-button.none .form-submit-button-state > :nth-child(1),
.form-submit-button-style-spinner .form-submit-button.error .form-submit-button-state > :nth-child(1),
.form-submit-button-style-ellipsis .form-submit-button.error .form-submit-button-state > :nth-child(1),
.form-submit-button-style-bar .form-submit-button.error .form-submit-button-state > :nth-child(1),
.form-submit-button-style-circle .form-submit-button.error .form-submit-button-state > :nth-child(1),
.form-submit-button-style-spinner .form-submit-button.submitting .form-submit-button-state > :nth-child(2),
.form-submit-button-style-ellipsis .form-submit-button.submitting .form-submit-button-state > :nth-child(2),
.form-submit-button-style-bar .form-submit-button.submitting .form-submit-button-state > :nth-child(2),
.form-submit-button-style-circle .form-submit-button.submitting .form-submit-button-state > :nth-child(2),
.form-submit-button-style-spinner .form-submit-button.submitted .form-submit-button-state > :nth-child(3),
.form-submit-button-style-ellipsis .form-submit-button.submitted .form-submit-button-state > :nth-child(3),
.form-submit-button-style-bar .form-submit-button.submitted .form-submit-button-state > :nth-child(3),
.form-submit-button-style-circle .form-submit-button.submitted .form-submit-button-state > :nth-child(3) {
  animation-play-state: running;
  opacity: 1;
  visibility: visible;
  transition-delay: 0.1s;
}
.form-submit-button-style-spinner .form-submit-button-state,
.form-submit-button-style-ellipsis .form-submit-button-state,
.form-submit-button-style-bar .form-submit-button-state,
.form-submit-button-style-circle .form-submit-button-state {
  display: grid;
  align-items: center;
  justify-content: center;
  grid-template-areas: "state";
  pointer-events: none;
}
.form-submit-button-style-spinner .form-submit-button-state > span,
.form-submit-button-style-ellipsis .form-submit-button-state > span,
.form-submit-button-style-bar .form-submit-button-state > span,
.form-submit-button-style-circle .form-submit-button-state > span {
  display: flex;
  align-items: center;
  justify-content: center;
  animation-play-state: paused;
  align-self: center;
  justify-self: center;
  grid-area: state;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s cubic-bezier(0.61, 1, 0.88, 1), visibility 0.2s cubic-bezier(0.61, 1, 0.88, 1);
}
.form-submit-button-style-spinner .form-submit-button-state > span span,
.form-submit-button-style-ellipsis .form-submit-button-state > span span,
.form-submit-button-style-bar .form-submit-button-state > span span,
.form-submit-button-style-circle .form-submit-button-state > span span {
  animation-play-state: inherit;
}
/* SPINNER button (conic gradient)
=================================================*/
.form-submit-button-style-spinner .form-submit-button-state {
  --thickness: 0.12em;
  /* submitting state */
  /* submitted state */
}
@keyframes keyframes-spinner {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
@keyframes keyframes-spinner-check {
  from {
    transform: translate3d(-100%, 0, 0);
  }
  to {
    transform: translate3d(0%, 0, 0);
  }
}
.form-submit-button-style-spinner .form-submit-button-state :nth-child(2) span {
  position: relative;
  width: 1.3em;
  height: 1.3em;
  border-radius: 50%;
  animation: keyframes-spinner 0.75s linear infinite;
  animation-play-state: inherit;
  background: conic-gradient(from 0.25turn, transparent, currentcolor 75%);
  mask-image: radial-gradient(closest-side, transparent 0% calc(100% - var(--thickness) - 1px), currentcolor calc(100% - var(--thickness)));
  -webkit-mask-image: radial-gradient(closest-side, transparent 0% calc(100% - var(--thickness) - 1px), currentcolor calc(100% - var(--thickness)));
  pointer-events: none !important;
}
.form-submit-button-style-spinner .form-submit-button-state :nth-child(3) {
  position: relative;
  width: 1.3em;
  height: 1.3em;
}
.form-submit-button-style-spinner .form-submit-button-state :nth-child(3)::before {
  position: absolute;
  border: solid var(--thickness) currentcolor;
  border-radius: 50%;
  content: "";
  inset: 0;
}
.form-submit-button-style-spinner .form-submit-button-state :nth-child(3) span {
  position: absolute;
  border-radius: 50%;
  inset: 0;
  mask-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'><polyline points='26.7,45.1 45.7,63.7 73.3,36.3' fill='none' stroke='black' stroke-width='8' /></svg>");
  -webkit-mask-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'><polyline points='26.7,45.1 45.7,63.7 73.3,36.3' fill='none' stroke='black' stroke-width='8' /></svg>");
  mask-size: 100%;
  -webkit-mask-size: 100%;
}
.form-submit-button-style-spinner .form-submit-button-state :nth-child(3) span::before {
  position: absolute;
  animation: keyframes-spinner-check 0.25s 0.15s cubic-bezier(0.61, 1, 0.88, 1) forwards;
  animation-play-state: inherit;
  background-color: currentcolor;
  content: "";
  inset: 0;
  transform: translate3d(-100%, 0, 0);
}
/* CIRCLE button (spinning circle)
=================================================*/
.form-submit-button-style-circle .form-submit-button-state {
  /* submitting state */
  /* submitted state */
}
@keyframes keyframes-circle {
  from,
  to {
    animation-timing-function: cubic-bezier(0.5, 0, 1, 0.5);
  }
  from {
    transform: rotateY(0deg);
  }
  to {
    transform: rotateY(1440deg);
  }
}
@keyframes keyframes-circle-check {
  from {
    transform: translate3d(0%, 0, 0);
  }
  to {
    transform: translate3d(100%, 0, 0);
  }
}
.form-submit-button-style-circle .form-submit-button-state :nth-child(2) span {
  width: 1.3em;
  height: 1.3em;
  border-radius: 50%;
  animation: keyframes-circle 2.4s cubic-bezier(0, 0.2, 0.8, 1) infinite;
  animation-play-state: inherit;
  background: currentcolor;
}
.form-submit-button-style-circle .form-submit-button-state :nth-child(3) span {
  position: relative;
  overflow: hidden;
  width: 1.3em;
  height: 1.3em;
  border-radius: 50%;
}
.form-submit-button-style-circle .form-submit-button-state :nth-child(3) span::before,
.form-submit-button-style-circle .form-submit-button-state :nth-child(3) span::after {
  position: absolute;
  content: "";
  inset: 0;
}
.form-submit-button-style-circle .form-submit-button-state :nth-child(3) span::before {
  animation: keyframes-circle-check 0.25s 0.15s cubic-bezier(0.61, 1, 0.88, 1) forwards;
  animation-play-state: inherit;
  background-color: currentcolor;
}
.form-submit-button-style-circle .form-submit-button-state :nth-child(3) span::after {
  background-color: currentcolor;
  inset: 0;
  -webkit-mask-image: url("data:image/svg+xml;utf8,<svg width='100' height='100' xmlns='http://www.w3.org/2000/svg'><defs><mask id='check'><rect width='100%' height='100%' fill='white' /><polyline points='26.7,45.1 45.7,63.7 73.3,36.3' fill='none' stroke='black' stroke-width='8' /></mask></defs><rect width='100%' height='100%' fill='black' mask='url(%23check)' /></svg>");
  -webkit-mask-size: 100%;
  mask-image: url("data:image/svg+xml;utf8,<svg width='100' height='100' xmlns='http://www.w3.org/2000/svg'><defs><mask id='check'><rect width='100%' height='100%' fill='white' /><polyline points='26.7,45.1 45.7,63.7 73.3,36.3' fill='none' stroke='black' stroke-width='8' /></mask></defs><rect width='100%' height='100%' fill='black' mask='url(%23check)' /></svg>");
  mask-size: 100%;
}
/* BAR & ELLIPSIS button submitted state style (shared)
=================================================*/
.form-submit-button-style-bar .form-submit-button-state,
.form-submit-button-style-ellipsis .form-submit-button-state {
  /* submitted state */
}
@keyframes keyframes-check {
  from {
    transform: translate3d(-100%, 0, 0);
  }
  to {
    transform: translate3d(0%, 0, 0);
  }
}
.form-submit-button-style-bar .form-submit-button-state :nth-child(3) span,
.form-submit-button-style-ellipsis .form-submit-button-state :nth-child(3) span {
  position: relative;
  width: 1.3em;
  height: 1.3em;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cpolyline points='10.3,41.6 42.6,73.3 89.7,26.7' fill='none' stroke='black' stroke-width='8' /%3E%3C/svg%3E");
  -webkit-mask-size: 100%;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cpolyline points='10.3,41.6 42.6,73.3 89.7,26.7' fill='none' stroke='black' stroke-width='8' /%3E%3C/svg%3E");
  mask-size: 100%;
}
.form-submit-button-style-bar .form-submit-button-state :nth-child(3) span::before,
.form-submit-button-style-ellipsis .form-submit-button-state :nth-child(3) span::before {
  position: absolute;
  animation: keyframes-check 0.25s 0.15s cubic-bezier(0.61, 1, 0.88, 1) forwards;
  animation-play-state: inherit;
  background-color: currentcolor;
  content: "";
  inset: 0;
  transform: translate3d(-100%, 0, 0);
}
/* BAR button (indeterminate progress)
=================================================*/
.form-submit-button-style-bar .form-submit-button-state {
  /* submitting state */
}
@keyframes keyframes-bar {
  from {
    transform: translate3d(-200%, 0, 0);
  }
  to {
    transform: translate3d(100%, 0, 0);
  }
}
.form-submit-button-style-bar .form-submit-button-state :nth-child(2) {
  width: 100%;
}
.form-submit-button-style-bar .form-submit-button-state :nth-child(2) span {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: max(2px, 0.12em);
  border-radius: 1em;
}
.form-submit-button-style-bar .form-submit-button-state :nth-child(2) span::before,
.form-submit-button-style-bar .form-submit-button-state :nth-child(2) span::after {
  position: absolute;
  border-radius: inherit;
  background: currentcolor;
  content: "";
}
.form-submit-button-style-bar .form-submit-button-state :nth-child(2) span::before {
  inset: 0;
  opacity: 0.5;
}
.form-submit-button-style-bar .form-submit-button-state :nth-child(2) span::after {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 50%;
  animation: keyframes-bar 0.75s linear infinite;
  animation-play-state: inherit;
}
/* ELLIPSIS button (3 dots)
=================================================*/
.form-submit-button-style-ellipsis .form-submit-button-state {
  /* submitting state */
}
@keyframes keyframes-ellipsis-grow {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
@keyframes keyframes-ellipsis-shrink {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0);
  }
}
@keyframes keyframes-ellipsis-shift {
  from {
    transform: translate(0, 0);
  }
  to {
    transform: translate(calc(0.35em * 2), 0);
  }
}
.form-submit-button-style-ellipsis .form-submit-button-state :nth-child(2) {
  position: relative;
  display: flex;
  width: calc(0.35em * 5);
  justify-content: space-between;
}
.form-submit-button-style-ellipsis .form-submit-button-state :nth-child(2) span {
  position: absolute;
  inset: 0;
}
.form-submit-button-style-ellipsis .form-submit-button-state :nth-child(2)::before {
  animation: keyframes-ellipsis-grow 0.75s infinite;
}
.form-submit-button-style-ellipsis .form-submit-button-state :nth-child(2)::after {
  animation: keyframes-ellipsis-shrink 0.75s infinite;
}
.form-submit-button-style-ellipsis .form-submit-button-state :nth-child(2) span::before,
.form-submit-button-style-ellipsis .form-submit-button-state :nth-child(2) span::after {
  position: absolute;
  animation: keyframes-ellipsis-shift 0.75s infinite;
}
.form-submit-button-style-ellipsis .form-submit-button-state :nth-child(2)::before,
.form-submit-button-style-ellipsis .form-submit-button-state :nth-child(2)::after,
.form-submit-button-style-ellipsis .form-submit-button-state :nth-child(2) span::before,
.form-submit-button-style-ellipsis .form-submit-button-state :nth-child(2) span::after {
  display: block;
  width: 0.35em;
  height: 0.35em;
  border-radius: 50%;
  animation-play-state: inherit;
  background: currentcolor;
  content: "";
}
.form-submit-button-style-ellipsis .form-submit-button-state :nth-child(2) span::before {
  left: 0;
}
.form-submit-button-style-ellipsis .form-submit-button-state :nth-child(2) span::after {
  right: calc(0.35em * 2);
}
@keyframes anim-form-post-submit {
  from {
    opacity: 0;
    transform: translate3d(0, 0.5rem, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
body .sqs-block-form {
  --form-post-submit-animation-duration: 0.25s;
  --form-post-submit-animation-delay: 0.25s;
}
.form-wrapper .form-submission-text,
.form-wrapper .form-submission-html {
  opacity: 0;
  transform: translate3d(0, 0.5rem, 0);
  animation: anim-form-post-submit var(--form-post-submit-animation-duration) var(--form-post-submit-animation-delay) cubic-bezier(0.61, 1, 0.88, 1) forwards;
}
@media (prefers-reduced-motion: reduce) {
  .form-wrapper .form-submission-text,
  .form-wrapper .form-submission-html {
    opacity: 1;
    transform: initial;
    animation: none;
  }
}
.sqs-block-form.sqs-edit-dialog-open .form-wrapper .form-submission-text,
.sqs-block-form.sqs-edit-dialog-open .form-wrapper .form-submission-html {
  opacity: 1;
  transform: initial;
  animation: none;
}
/* Button Block Base Style
=================================================*/
.sqs-block-button-container {
  text-align: center;
}
.sqs-block-button.sqs-stretched .sqs-block-content,
.sqs-block-button.sqs-stretched .sqs-block-button-element {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  height: 100%;
  display: flex;
}
.sqs-block-button.sqs-stretched .sqs-block-button-container {
  flex: 1;
}
.sqs-block-button.sqs-stretched .sqs-block-button-element {
  align-items: center;
  box-sizing: border-box;
  justify-content: center;
}
.sqs-block-button:not(.sqs-stretched) .sqs-block-button-container {
  display: flex;
}
.sqs-block-button:not(.sqs-stretched) .sqs-block-button-container--left {
  justify-content: flex-start;
}
.sqs-block-button:not(.sqs-stretched) .sqs-block-button-container--center {
  justify-content: center;
}
.sqs-block-button:not(.sqs-stretched) .sqs-block-button-container--right {
  justify-content: flex-end;
}
.sqs-block-button-element,
.image-button a,
.list-item-content__button {
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  line-height: normal;
  padding: var(--primaryButtonPadding) calc(var(--primaryButtonPadding) * 1.67);
}
@media (hover: hover) {
  .sqs-block-button-element:hover,
  .image-button a:hover,
  .list-item-content__button:hover {
    opacity: 1;
  }
}
.sqs-button-element--primary[disabled],
.sqs-button-element--secondary[disabled],
.sqs-button-element--tertiary[disabled] {
  pointer-events: none !important;
  opacity: 0.8 !important;
}
/* Universal Button Variant Styles
=================================================*/
#siteWrapper.site-wrapper .sqs-button-element--primary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--primary {
  padding: var(--primary-button-padding-y) var(--primary-button-padding-x);
}
#siteWrapper.site-wrapper .sqs-button-element--primary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--primary,
#siteWrapper.site-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary,
body .sqs-block-form-lightbox .form-button-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary {
  border-width: var(--primary-button-stroke);
}
#siteWrapper.site-wrapper .sqs-button-element--secondary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--secondary {
  padding: var(--secondary-button-padding-y) var(--secondary-button-padding-x);
}
#siteWrapper.site-wrapper .sqs-button-element--secondary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--secondary,
#siteWrapper.site-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary,
body .sqs-block-form-lightbox .form-button-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-width: var(--secondary-button-stroke);
}
#siteWrapper.site-wrapper .sqs-button-element--tertiary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--tertiary {
  padding: var(--tertiary-button-padding-y) var(--tertiary-button-padding-x);
}
#siteWrapper.site-wrapper .sqs-button-element--tertiary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--tertiary,
#siteWrapper.site-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary,
body .sqs-block-form-lightbox .form-button-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-width: var(--tertiary-button-stroke);
}
#siteWrapper.site-wrapper .sqs-button-element--primary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--primary,
#siteWrapper.site-wrapper .sqs-button-element--secondary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--secondary,
#siteWrapper.site-wrapper .sqs-button-element--tertiary,
body .sqs-block-form-lightbox .form-button-wrapper .sqs-button-element--tertiary,
#siteWrapper.site-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary,
body .sqs-block-form-lightbox .form-button-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary,
#siteWrapper.site-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary,
body .sqs-block-form-lightbox .form-button-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary,
#siteWrapper.site-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary,
body .sqs-block-form-lightbox .form-button-wrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  line-height: normal;
  border-style: solid;
}
/* Specific Button Style Overrides
=================================================*/
#siteWrapper.site-wrapper div#Tock_widget_container > div.TockWidgetWrapper .TockButton {
  height: auto;
  line-height: 0;
}
#siteWrapper.site-wrapper div#Tock_widget_container > div.TockWidgetWrapper .TockButtonWidgetContainer,
#siteWrapper.site-wrapper div#Tock_widget_container > div.TockWidgetWrapper .TockButton-buttonContainer,
#siteWrapper.site-wrapper div#Tock_widget_container > div.TockWidgetWrapper .TockInlineButton-container {
  width: auto;
}
#siteWrapper.site-wrapper div#Tock_widget_container > div.TockWidgetWrapper .TockSearchBar-container {
  align-items: center;
}
#siteWrapper.site-wrapper div#Tock_widget_container > div.TockWidgetWrapper .TockInlineButton-container {
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 49px;
  min-width: 180px;
  padding: 0px;
  border-radius: 0 3px 3px 0;
}
#siteWrapper.site-wrapper div#Tock_widget_container.Tock_widget_container-columnLayout > div.TockWidgetWrapper .TockInlineButton-container {
  border-radius: 3px;
}
.sqs-modal-lightbox .sqs-modal-lightbox-content .sqs-button-element--primary {
  border-width: var(--primary-button-stroke);
  border-style: solid;
}
.sqs-modal-lightbox .sqs-modal-lightbox-content .sqs-button-element--secondary {
  border-width: var(--secondary-button-stroke);
  border-style: solid;
}
.sqs-modal-lightbox .sqs-modal-lightbox-content .sqs-button-element--tertiary {
  border-width: var(--tertiary-button-stroke);
  border-style: solid;
}
/* Button Block Style Tweak: Solid
=================================================*/
body.primary-button-style-solid .sqs-button-element--primary,
body.primary-button-style-solid .sqs-editable-button.sqs-button-element--primary {
  transition: 0.1s opacity linear;
  -webkit-backface-visibility: hidden;
}
@media (hover: hover) {
  body.primary-button-style-solid .sqs-button-element--primary:hover,
  body.primary-button-style-solid .sqs-editable-button.sqs-button-element--primary:hover {
    opacity: 0.8;
  }
}
/* Button Block Style Tweak: Outline
=================================================*/
.primary-button-style-outline .sqs-button-element--primary,
.primary-button-style-outline .list-item .sqs-block-button-element.list-item-content__button.sqs-button-element--primary,
.primary-button-style-outline .list-item[data-is-card-enabled="true"] .sqs-block-button-element.list-item-content__button.sqs-button-element--primary,
.primary-button-style-outline .sqs-block-form .sqs-editable-button.sqs-button-element--primary,
.primary-button-style-outline .header-menu-cta .btn.sqs-button-element--primary,
.primary-button-style-outline .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary,
.primary-button-style-outline .sqs-modal-lightbox-content .sqs-button-element--primary {
  transition: 0.1s background-color linear, 0.1s color linear;
}
.primary-button-style-outline .sqs-button-element--primary:not(:hover),
.primary-button-style-outline .list-item .sqs-block-button-element.list-item-content__button.sqs-button-element--primary:not(:hover),
.primary-button-style-outline .list-item[data-is-card-enabled="true"] .sqs-block-button-element.list-item-content__button.sqs-button-element--primary:not(:hover),
.primary-button-style-outline .sqs-block-form .sqs-editable-button.sqs-button-element--primary:not(:hover),
.primary-button-style-outline .header-menu-cta .btn.sqs-button-element--primary:not(:hover),
.primary-button-style-outline .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary:not(:hover),
.primary-button-style-outline .sqs-modal-lightbox-content .sqs-button-element--primary:not(:hover) {
  background: transparent;
}
.primary-button-style-outline .newsletter-form-button.sqs-system-button.sqs-button-element--primary:not(:hover) {
  background: transparent !important;
}
/* Primary Button Shape Tweak: Square
=================================================*/
.primary-button-shape-square .sqs-button-element--primary,
.primary-button-shape-square #Tock_widget_container .sqs-button-element--primary.TockButton-blue {
  border-radius: 0;
}
/* Primary Button Shape Tweak: Rounded
=================================================*/
.primary-button-shape-rounded .sqs-button-element--primary,
.primary-button-shape-rounded #Tock_widget_container .sqs-button-element--primary.TockButton-blue {
  border-radius: 0.4rem;
}
/* Primary Button Shape Tweak: Pill
=================================================*/
.primary-button-shape-pill .sqs-button-element--primary,
.primary-button-shape-pill #Tock_widget_container .sqs-button-element--primary.TockButton-blue {
  border-radius: 300px;
}
/* Primary Button Shape Tweak: Oval
=================================================*/
.primary-button-shape-oval .sqs-button-element--primary,
.primary-button-shape-oval #Tock_widget_container .sqs-button-element--primary.TockButton-blue {
  border-radius: 100%;
}
/* Primary Button Shape Tweak: Petal
=================================================*/
.primary-button-shape-petal .sqs-button-element--primary,
.primary-button-shape-petal #Tock_widget_container .sqs-button-element--primary.TockButton-blue {
  border-radius: 16px 0px;
}
/* Button Block Shape Tweak: Underline
=================================================*/
body.primary-button-shape-underline #siteWrapper .sqs-button-element--primary:not(.ma-pricing-option-button),
body.primary-button-shape-underline .sqs-block-form-lightbox .sqs-button-element--primary:not(.ma-pricing-option-button),
body.primary-button-shape-underline #siteWrapper .comment-btn-wrapper .comment-btn.sqs-button-element--primary,
body.primary-button-shape-underline .sqs-block-form-lightbox .comment-btn-wrapper .comment-btn.sqs-button-element--primary,
body.primary-button-shape-underline #siteWrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary,
body.primary-button-shape-underline .sqs-block-form-lightbox .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary {
  border-top: none;
  border-right: none;
  border-left: none;
  border-bottom-style: solid;
  border-radius: 0;
  border-bottom-width: var(--primary-button-stroke);
}
/* Primary Button Shape Tweak: Custom
=================================================*/
.primary-button-shape-custom .sqs-button-element--primary,
.primary-button-shape-custom .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--primary {
  border-top-left-radius: var(--primary-button-rounded-border-top-left-radius);
  border-top-right-radius: var(--primary-button-rounded-border-top-right-radius);
  border-bottom-left-radius: var(--primary-button-rounded-border-bottom-left-radius);
  border-bottom-right-radius: var(--primary-button-rounded-border-bottom-right-radius);
}
.secondary-button-style-solid .sqs-button-element--secondary,
.secondary-button-style-solid .sqs-editable-button.sqs-button-element--secondary,
.secondary-button-style-solid .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  transition: 0.1s opacity linear;
  -webkit-backface-visibility: hidden;
}
.secondary-button-style-solid .sqs-button-element--secondary:hover,
.secondary-button-style-solid .sqs-editable-button.sqs-button-element--secondary:hover,
.secondary-button-style-solid .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary:hover {
  opacity: 0.8;
}
.secondary-button-style-outline .sqs-button-element--secondary,
.secondary-button-style-outline .list-item .sqs-block-button-element.list-item-content__button.sqs-button-element--secondary,
.secondary-button-style-outline .list-item[data-is-card-enabled="true"] .sqs-block-button-element.list-item-content__button.sqs-button-element--secondary,
.secondary-button-style-outline .sqs-block-form .sqs-editable-button.sqs-button-element--secondary,
.secondary-button-style-outline .header-menu-cta .btn.sqs-button-element--secondary,
.secondary-button-style-outline .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary,
.secondary-button-style-outline .sqs-modal-lightbox-content .sqs-button-element--secondary {
  transition: 0.1s background-color linear, 0.1s color linear;
}
.secondary-button-style-outline .sqs-button-element--secondary:not(:hover),
.secondary-button-style-outline .list-item .sqs-block-button-element.list-item-content__button.sqs-button-element--secondary:not(:hover),
.secondary-button-style-outline .list-item[data-is-card-enabled="true"] .sqs-block-button-element.list-item-content__button.sqs-button-element--secondary:not(:hover),
.secondary-button-style-outline .sqs-block-form .sqs-editable-button.sqs-button-element--secondary:not(:hover),
.secondary-button-style-outline .header-menu-cta .btn.sqs-button-element--secondary:not(:hover),
.secondary-button-style-outline .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary:not(:hover),
.secondary-button-style-outline .sqs-modal-lightbox-content .sqs-button-element--secondary:not(:hover) {
  background: transparent;
}
.secondary-button-style-outline .newsletter-form-button.sqs-system-button.sqs-button-element--secondary {
  background: transparent !important;
}
/* Secondary Button Shape Tweak: Square
=================================================*/
.secondary-button-shape-square .sqs-button-element--secondary,
.secondary-button-shape-square .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-radius: 0;
}
/* Secondary Button Shape Tweak: Rounded
=================================================*/
.secondary-button-shape-rounded .sqs-button-element--secondary,
.secondary-button-shape-rounded .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-radius: 0.4rem;
}
/* Secondary Button Shape Tweak: Pill
=================================================*/
.secondary-button-shape-pill .sqs-button-element--secondary,
.secondary-button-shape-pill .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-radius: 300px;
}
/* Secondary Button Shape Tweak: Oval
=================================================*/
.secondary-button-shape-oval .sqs-button-element--secondary,
.secondary-button-shape-oval .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-radius: 100%;
}
/* Secondary Button Shape Tweak: Underline
=================================================*/
body.secondary-button-shape-underline #siteWrapper .sqs-button-element--secondary,
body.secondary-button-shape-underline .sqs-block-form-lightbox .sqs-button-element--secondary,
body.secondary-button-shape-underline #siteWrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary,
body.secondary-button-shape-underline .sqs-block-form-lightbox .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-top: none;
  border-right: none;
  border-left: none;
  border-bottom-style: solid;
  border-radius: 0;
  border-bottom-width: var(--secondary-button-stroke);
}
/* Secondary Button Shape Tweak: Petal
=================================================*/
.secondary-button-shape-petal .sqs-button-element--secondary,
.secondary-button-shape-petal .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-radius: 16px 0px;
}
/* Secondary Button Shape Tweak: Custom
=================================================*/
.secondary-button-shape-custom .sqs-button-element--secondary,
.secondary-button-shape-custom .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--secondary {
  border-top-left-radius: var(--secondary-button-rounded-border-top-left-radius);
  border-top-right-radius: var(--secondary-button-rounded-border-top-right-radius);
  border-bottom-left-radius: var(--secondary-button-rounded-border-bottom-left-radius);
  border-bottom-right-radius: var(--secondary-button-rounded-border-bottom-right-radius);
}
.tertiary-button-style-solid .sqs-button-element--tertiary,
.tertiary-button-style-solid .sqs-editable-button.sqs-button-element--tertiary,
.tertiary-button-style-solid .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  transition: 0.1s opacity linear;
  -webkit-backface-visibility: hidden;
}
.tertiary-button-style-solid .sqs-button-element--tertiary:hover,
.tertiary-button-style-solid .sqs-editable-button.sqs-button-element--tertiary:hover,
.tertiary-button-style-solid .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary:hover {
  opacity: 0.8;
}
.tertiary-button-style-outline .sqs-button-element--tertiary,
.tertiary-button-style-outline .list-item .sqs-block-button-element.list-item-content__button.sqs-button-element--tertiary,
.tertiary-button-style-outline .list-item[data-is-card-enabled="true"] .sqs-block-button-element.list-item-content__button.sqs-button-element--tertiary,
.tertiary-button-style-outline .sqs-block-form .sqs-editable-button.sqs-button-element--tertiary,
.tertiary-button-style-outline .header-menu-cta .btn.sqs-button-element--tertiary,
.tertiary-button-style-outline .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary,
.tertiary-button-style-outline .sqs-modal-lightbox-content .sqs-button-element--tertiary {
  transition: 0.1s background-color linear, 0.1s color linear;
}
.tertiary-button-style-outline .sqs-button-element--tertiary:not(:hover),
.tertiary-button-style-outline .list-item .sqs-block-button-element.list-item-content__button.sqs-button-element--tertiary:not(:hover),
.tertiary-button-style-outline .list-item[data-is-card-enabled="true"] .sqs-block-button-element.list-item-content__button.sqs-button-element--tertiary:not(:hover),
.tertiary-button-style-outline .sqs-block-form .sqs-editable-button.sqs-button-element--tertiary:not(:hover),
.tertiary-button-style-outline .header-menu-cta .btn.sqs-button-element--tertiary:not(:hover),
.tertiary-button-style-outline .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary:not(:hover),
.tertiary-button-style-outline .sqs-modal-lightbox-content .sqs-button-element--tertiary:not(:hover) {
  background: transparent;
}
.tertiary-button-style-outline .newsletter-form-button.sqs-system-button.sqs-button-element--tertiary {
  background: transparent !important;
}
/* Tertiary Button Shape Tweak: Square
=================================================*/
.tertiary-button-shape-square .sqs-button-element--tertiary,
.tertiary-button-shape-square .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-radius: 0;
}
/* Tertiary Button Shape Tweak: Rounded
=================================================*/
.tertiary-button-shape-rounded .sqs-button-element--tertiary,
.tertiary-button-shape-rounded .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-radius: 0.4rem;
}
/* Tertiary Button Shape Tweak: Pill
=================================================*/
.tertiary-button-shape-pill .sqs-button-element--tertiary,
.tertiary-button-shape-pill .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-radius: 300px;
}
/* Tertiary Button Shape Tweak: Oval
=================================================*/
.tertiary-button-shape-oval .sqs-button-element--tertiary,
.tertiary-button-shape-oval .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-radius: 100%;
}
/* Tertiry Button Shape Tweak: Underline
=================x================================*/
body.tertiary-button-shape-underline #siteWrapper .sqs-button-element--tertiary,
body.tertiary-button-shape-underline .sqs-block-form-lightbox .sqs-button-element--tertiary,
body.tertiary-button-shape-underline #siteWrapper .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary,
body.tertiary-button-shape-underline .sqs-block-form-lightbox .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-top: none;
  border-right: none;
  border-left: none;
  border-bottom-style: solid;
  border-radius: 0;
  border-bottom-width: var(--tertiary-button-stroke);
}
/* Tertiary Button Shape Tweak: Petal
=================================================*/
.tertiary-button-shape-petal .sqs-button-element--tertiary,
.tertiary-button-shape-petal .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-radius: 16px 0px;
}
/* Tertiary Button Shape Tweak: Custom
=================================================*/
.tertiary-button-shape-custom .sqs-button-element--tertiary,
.tertiary-button-shape-custom .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockButton-blue.sqs-button-element--tertiary {
  border-top-left-radius: var(--tertiary-button-rounded-border-top-left-radius);
  border-top-right-radius: var(--tertiary-button-rounded-border-top-right-radius);
  border-bottom-left-radius: var(--tertiary-button-rounded-border-bottom-left-radius);
  border-bottom-right-radius: var(--tertiary-button-rounded-border-bottom-right-radius);
}
@media (hover: hover) {
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .image-button a:hover,
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .sqs-add-to-cart-button:hover,
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .sqs-editable-button:not(input):hover,
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .sqs-block-button-element.sqs-block-button-element--primary:hover,
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .ma-pricing-toggle-wrapper .ma-pricing-option-button:hover,
  .tweak-global-animations-animation-type-flex.primary-button-style-outline [data-animation-role="header-element"] .btn:hover {
    background-color: transparent !important;
  }
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .user-items-list-simple {
    background: transparent;
  }
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .user-items-list-simple:hover {
    background-color: transparent;
  }
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .user-items-list-carousel {
    background: transparent;
  }
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .user-items-list-carousel:hover {
    background-color: transparent;
  }
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .user-items-list-banner-slideshow {
    background: transparent;
  }
  .tweak-global-animations-animation-type-flex.primary-button-style-outline .user-items-list-banner-slideshow:hover {
    background-color: transparent;
  }
}
@media (hover: hover) {
  .tweak-global-animations-animation-type-flex.secondary-button-style-outline .sqs-button-element--secondary:hover,
  .tweak-global-animations-animation-type-flex.tertiary-button-style-outline .sqs-button-element--tertiary:hover {
    background: transparent;
  }
}
.sqs-announcement-bar .sqs-announcement-bar-close {
  background: transparent;
}
div#Tock_widget_container > div.TockWidgetWrapper .InlineWidgetDropDown-NoRightBorder {
  border-right: 1px solid #e7e7e7;
}
/*
  Constants
  Role: A single location to organize variables that are utilized globally
*/
/*
  ================
  Z-index
  Role: Organize z-index overrides
  ================
*/
/*
  ================
  Breakpoints
  Role: Organize global breakpoint ranges
  ================
*/
/*
  ================
  Padding
  Role: Organize consistent padding
  ================
*/
/*
  ================
  Button
  Role: Organize button configurations
  ================
*/
/* These appear to be deprecated. */
/*
  ================
  Container
  Role: Container max width configuration
  ================
*/
/*
  ================
  Gallery Caption
  Role: gallery caption configuration
  ================
*/
/*===================================================
      GLOBAL ANIMATIONS

      Notes:
      - The reason why we want to use static CSS for pre-animation
      styling is because we want the pre-animation styling to be
      applied strictly before the post-animation styling is applied
      through the controller. This avoids a whole class of
      asynchronous issues.

      - tweak-global-animations-animation-curve: custom cubic-bezier value is so we can have easeOutExpo CSS style (https://easings.net/en#easeOutExpo)
 ===================================================*/
/*
 * Rule needed in order for clip and flex animations to work consistently for all image animation targets.
 * There are known edge cases in both Safari and Firefox's Intersection Observer when image or its parent container's
 * height is 0. Adding minimum height will allow the Intersection Observer to work in these 2 browsers.
 * This also fixes an issue in Chrome 97 where images with shapes in card/collage/overlap/stack layout do not render
 */
[data-animation-role="image"] {
  min-height: 1px;
}
/*
  keyframes are used here not for animation but as a max-timeout for a temporary hiding style to apply, without relying on JS to unapply it.
*/
@keyframes hideContent {
  0%,
  99% {
    opacity: 0;
  }
}
@keyframes clipAnimation {
  0% {
    -webkit-clip-path: polygon(0 0, 10% 0, 0% 100%, 0% 100%);
    clip-path: polygon(0 0, 10% 0, 0% 100%, 0% 100%);
  }
  100% {
    -webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
  }
}
.preClip {
  clip-path: ellipse(0% 100% at 0 0);
  -webkit-clip-path: ellipse(0% 100% at 0 0);
  transition-property: clip-path, -webkit-clip-path;
  transform: translateZ(0);
}
.clipIn {
  animation: clipAnimation;
  animation-fill-mode: forwards;
  transform: translateZ(0);
}
_:-ms-fullscreen .preClip,
:root .ie11up .preClip {
  opacity: 0;
  transition-property: opacity;
}
_:-ms-fullscreen .clipIn:not([data-override-initial-global-animation]),
:root .ie11up .clipIn:not([data-override-initial-global-animation]) {
  opacity: 1 !important;
}
@supports not (clip-path: ellipse(0% 100% at 0 0)) {
  .preClip {
    opacity: 0;
    transition-property: opacity;
  }
  .clipIn:not([data-override-initial-global-animation]) {
    opacity: 1 !important;
  }
}
.preFade {
  opacity: 0;
  transition-property: opacity;
}
.fadeIn:not([data-override-initial-global-animation]) {
  opacity: 1 !important;
}
.preScale {
  opacity: 0;
  transform: scale(0.9);
  transition-property: transform, opacity;
}
.scaleIn {
  transform: scale(1) !important;
}
.scaleIn:not([data-override-initial-global-animation]) {
  opacity: 1 !important;
}
.preSlide {
  opacity: 0;
  transform: translate(0%, 30%);
  transition-property: transform, opacity;
}
.slideIn {
  transform: translate(0, 0) !important;
}
.slideIn:not([data-override-initial-global-animation]) {
  opacity: 1 !important;
}
.tweak-global-animations-animation-type-fade header,
.tweak-global-animations-animation-type-slide header,
.tweak-global-animations-animation-type-scale header,
.tweak-global-animations-animation-type-clip header,
.tweak-global-animations-animation-type-flex header,
.tweak-global-animations-animation-type-fade footer,
.tweak-global-animations-animation-type-slide footer,
.tweak-global-animations-animation-type-scale footer,
.tweak-global-animations-animation-type-clip footer,
.tweak-global-animations-animation-type-flex footer,
.tweak-global-animations-animation-type-fade section > .content-wrapper,
.tweak-global-animations-animation-type-slide section > .content-wrapper,
.tweak-global-animations-animation-type-scale section > .content-wrapper,
.tweak-global-animations-animation-type-clip section > .content-wrapper,
.tweak-global-animations-animation-type-flex section > .content-wrapper {
  animation: hideContent 2s;
}
.tweak-global-animations-animation-type-fade[data-animation-state="booted"] header,
.tweak-global-animations-animation-type-slide[data-animation-state="booted"] header,
.tweak-global-animations-animation-type-scale[data-animation-state="booted"] header,
.tweak-global-animations-animation-type-clip[data-animation-state="booted"] header,
.tweak-global-animations-animation-type-flex[data-animation-state="booted"] header,
.tweak-global-animations-animation-type-fade[data-animation-state="booted"] footer,
.tweak-global-animations-animation-type-slide[data-animation-state="booted"] footer,
.tweak-global-animations-animation-type-scale[data-animation-state="booted"] footer,
.tweak-global-animations-animation-type-clip[data-animation-state="booted"] footer,
.tweak-global-animations-animation-type-flex[data-animation-state="booted"] footer,
.tweak-global-animations-animation-type-fade[data-animation-state="booted"] section > .content-wrapper,
.tweak-global-animations-animation-type-slide[data-animation-state="booted"] section > .content-wrapper,
.tweak-global-animations-animation-type-scale[data-animation-state="booted"] section > .content-wrapper,
.tweak-global-animations-animation-type-clip[data-animation-state="booted"] section > .content-wrapper,
.tweak-global-animations-animation-type-flex[data-animation-state="booted"] section > .content-wrapper {
  opacity: 1;
  animation: none;
}
.tweak-global-animations-animation-type-flex {
  /* Individual element or element behaviors */
  /* Non-CTA hovers */
  /*
   * Segmented text (currently only available with "Flex")
   */
}
.tweak-global-animations-animation-type-flex .preFlex {
  opacity: 0;
  transition-property: transform, opacity, clip-path;
  transform: translate(0%, 2vh);
}
.tweak-global-animations-animation-type-flex .flexIn {
  opacity: 1;
  transform: translate(0%, 0%);
}
.tweak-global-animations-animation-type-flex [data-animation-role="image"].preFlex {
  transform: none;
  clip-path: polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%);
}
.tweak-global-animations-animation-type-flex [data-animation-role="image"].flexIn {
  transform: none;
  clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%);
}
.tweak-global-animations-animation-type-flex .image-position-right [data-animation-role="image"].preFlex {
  clip-path: polygon(101% 0%, 99.9% 0%, 101% 100%, calc(100% + 25%) 100%);
}
.tweak-global-animations-animation-type-flex .image-position-right [data-animation-role="image"].flexIn {
  clip-path: polygon(0% 0%, 101% 0%, 101% 101%, 0% 101%);
}
.tweak-global-animations-animation-type-flex .image-position-left [data-animation-role="image"].preFlex {
  clip-path: polygon(-1% 0%, 0.1% 0%, -25% 100%, 0% 100%);
}
.tweak-global-animations-animation-type-flex .image-position-left [data-animation-role="image"].flexIn {
  clip-path: polygon(0% 0%, 101% 0%, 101% 101%, 0% 101%);
}
.tweak-global-animations-animation-type-flex.primary-button-style-solid [data-button-type="primary"],
.tweak-global-animations-animation-type-flex.primary-button-style-solid .add-to-cart-button-wrapper,
.tweak-global-animations-animation-type-flex.primary-button-style-solid .image-button-wrapper {
  overflow: hidden;
}
.tweak-global-animations-animation-type-flex.primary-button-style-solid [data-button-type="primary"].preFlex .sqs-button-element--primary,
.tweak-global-animations-animation-type-flex.primary-button-style-solid .add-to-cart-button-wrapper.preFlex .sqs-button-element--primary,
.tweak-global-animations-animation-type-flex.primary-button-style-solid .image-button-wrapper.preFlex .sqs-button-element--primary,
.tweak-global-animations-animation-type-flex.primary-button-style-solid [data-button-type="primary"].preFlex .sqs-add-to-cart-button,
.tweak-global-animations-animation-type-flex.primary-button-style-solid .add-to-cart-button-wrapper.preFlex .sqs-add-to-cart-button,
.tweak-global-animations-animation-type-flex.primary-button-style-solid .image-button-wrapper.preFlex .sqs-add-to-cart-button,
.tweak-global-animations-animation-type-flex.primary-button-style-solid [data-button-type="primary"].preFlex .sqs-editable-button:not(input),
.tweak-global-animations-animation-type-flex.primary-button-style-solid .add-to-cart-button-wrapper.preFlex .sqs-editable-button:not(input),
.tweak-global-animations-animation-type-flex.primary-button-style-solid .image-button-wrapper.preFlex .sqs-editable-button:not(input),
.tweak-global-animations-animation-type-flex.primary-button-style-solid [data-button-type="primary"].preFlex .image-button,
.tweak-global-animations-animation-type-flex.primary-button-style-solid .add-to-cart-button-wrapper.preFlex .image-button,
.tweak-global-animations-animation-type-flex.primary-button-style-solid .image-button-wrapper.preFlex .image-button {
  transition: inherit;
  transform: translate(0%, 100%);
}
.tweak-global-animations-animation-type-flex.primary-button-style-solid [data-button-type="primary"].flexIn .sqs-button-element--primary,
.tweak-global-animations-animation-type-flex.primary-button-style-solid .add-to-cart-button-wrapper.flexIn .sqs-button-element--primary,
.tweak-global-animations-animation-type-flex.primary-button-style-solid .image-button-wrapper.flexIn .sqs-button-element--primary,
.tweak-global-animations-animation-type-flex.primary-button-style-solid [data-button-type="primary"].flexIn .sqs-add-to-cart-button,
.tweak-global-animations-animation-type-flex.primary-button-style-solid .add-to-cart-button-wrapper.flexIn .sqs-add-to-cart-button,
.tweak-global-animations-animation-type-flex.primary-button-style-solid .image-button-wrapper.flexIn .sqs-add-to-cart-button,
.tweak-global-animations-animation-type-flex.primary-button-style-solid [data-button-type="primary"].flexIn .sqs-editable-button:not(input),
.tweak-global-animations-animation-type-flex.primary-button-style-solid .add-to-cart-button-wrapper.flexIn .sqs-editable-button:not(input),
.tweak-global-animations-animation-type-flex.primary-button-style-solid .image-button-wrapper.flexIn .sqs-editable-button:not(input),
.tweak-global-animations-animation-type-flex.primary-button-style-solid [data-button-type="primary"].flexIn .image-button,
.tweak-global-animations-animation-type-flex.primary-button-style-solid .add-to-cart-button-wrapper.flexIn .image-button,
.tweak-global-animations-animation-type-flex.primary-button-style-solid .image-button-wrapper.flexIn .image-button {
  transform: translate(0%, 0%);
}
.tweak-global-animations-animation-type-flex.primary-button-style-solid .header-actions-action--cta {
  overflow: hidden;
}
.tweak-global-animations-animation-type-flex.primary-button-style-solid .header-actions-action--cta.preFlex .btn {
  transition: inherit;
  transform: translate(0%, 100%);
}
.tweak-global-animations-animation-type-flex.primary-button-style-solid .header-actions-action--cta.flexIn .btn {
  transform: translate(0%, 0%);
}
.tweak-global-animations-animation-type-flex.secondary-button-style-solid [data-button-type="secondary"] {
  overflow: hidden;
}
.tweak-global-animations-animation-type-flex.secondary-button-style-solid [data-button-type="secondary"].preFlex .sqs-button-element--secondary {
  transition: inherit;
  transform: translate(0%, 100%);
}
.tweak-global-animations-animation-type-flex.secondary-button-style-solid [data-button-type="secondary"].flexIn .sqs-button-element--secondary {
  transform: translate(0%, 0%);
}
.tweak-global-animations-animation-type-flex.tertiary-button-styled-solid [data-button-type="tertiary"] {
  overflow: hidden;
}
.tweak-global-animations-animation-type-flex.tertiary-button-styled-solid [data-button-type="tertiary"].preFlex .sqs-button-element--tertiary {
  transition: inherit;
  transform: translate(0%, 100%);
}
.tweak-global-animations-animation-type-flex.tertiary-button-styled-solid [data-button-type="tertiary"].flexIn .sqs-button-element--tertiary {
  transform: translate(0%, 0%);
}
@media (hover: hover) {
  .tweak-global-animations-animation-type-flex {
    /* Buttons */
  }
  .tweak-global-animations-animation-type-flex a[data-animation-role="image"] {
    overflow: hidden;
  }
  .tweak-global-animations-animation-type-flex a[data-animation-role="image"] img {
    transition: inherit;
    transform: translate(0%, 0%);
    transform-origin: top;
  }
  .tweak-global-animations-animation-type-flex a[data-animation-role="image"]:hover img {
    transform: scale(1.05) translate(0%, -2.5%);
  }
  .tweak-global-animations-animation-type-flex .sqs-button-element--primary:not(input),
  .tweak-global-animations-animation-type-flex .sqs-button-element--secondary:not(input),
  .tweak-global-animations-animation-type-flex .sqs-button-element--tertiary:not(input) {
    transform: scaleY(1);
    transition: 0.6s cubic-bezier(0.19, 1, 0.22, 1) color;
    position: relative;
  }
  .tweak-global-animations-animation-type-flex .sqs-button-element--primary:not(input)::before,
  .tweak-global-animations-animation-type-flex .sqs-button-element--secondary:not(input)::before,
  .tweak-global-animations-animation-type-flex .sqs-button-element--tertiary:not(input)::before {
    z-index: -1;
    position: absolute;
    content: '';
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    border-style: solid;
    border-width: 2px;
    transition: 0.6s cubic-bezier(0.19, 1, 0.22, 1) clip-path;
    clip-path: polygon(-2% 102%, 102% 102%, 102% 102%, -2% 102%);
    transform: translateZ(0);
  }
  .tweak-global-animations-animation-type-flex .sqs-button-element--primary:not(input):hover,
  .tweak-global-animations-animation-type-flex .sqs-button-element--secondary:not(input):hover,
  .tweak-global-animations-animation-type-flex .sqs-button-element--tertiary:not(input):hover {
    opacity: 1;
  }
  .tweak-global-animations-animation-type-flex .sqs-button-element--primary:not(input):hover::before,
  .tweak-global-animations-animation-type-flex .sqs-button-element--secondary:not(input):hover::before,
  .tweak-global-animations-animation-type-flex .sqs-button-element--tertiary:not(input):hover::before {
    clip-path: polygon(-2% -2%, 102% -2%, 102% 102%, -2% 102%);
  }
  .tweak-global-animations-animation-type-flex .sqs-button-element--primary:not(input).ma-pricing-option-button.right-button::before {
    border-radius: 0px 300px 300px 0px;
    border-width: var(--primary-button-stroke);
  }
  .tweak-global-animations-animation-type-flex .sqs-button-element--primary:not(input).ma-pricing-option-button.left-button::before {
    border-radius: 300px 0px 0px 300px;
    border-width: var(--primary-button-stroke);
  }
  .tweak-global-animations-animation-type-flex .has-background .sqs-button-element--primary:not(input)::before,
  .tweak-global-animations-animation-type-flex .has-background .sqs-button-element--secondary:not(input)::before,
  .tweak-global-animations-animation-type-flex .has-background .sqs-button-element--tertiary:not(input)::before {
    border-width: 0;
  }
  .tweak-global-animations-animation-type-flex .has-background .sqs-button-element--primary:not(input)::before {
    inset: calc(-1 * var(--primary-button-stroke));
  }
  .tweak-global-animations-animation-type-flex .has-background .sqs-button-element--secondary:not(input)::before {
    inset: calc(-1 * var(--secondary-button-stroke));
  }
  .tweak-global-animations-animation-type-flex .has-background .sqs-button-element--tertiary:not(input)::before {
    inset: calc(-1 * var(--tertiary-button-stroke));
  }
  .tweak-global-animations-animation-type-flex.primary-button-style-solid .tock-block div#Tock_widget_container > div.TockWidgetWrapper .TockInlineButton-container.sqs-button-element--primary .MainLabelSpan {
    background: transparent !important;
  }
  .tweak-global-animations-animation-type-flex.primary-button-shape-rounded .sqs-button-element--primary:not(.ma-pricing-option-button)::before {
    border-radius: 0.4rem;
  }
  .tweak-global-animations-animation-type-flex.secondary-button-shape-rounded .sqs-button-element--secondary::before,
  .tweak-global-animations-animation-type-flex.tertiary-button-shape-rounded .sqs-button-element--tertiary::before {
    border-radius: 0.4rem;
  }
  .tweak-global-animations-animation-type-flex.primary-button-shape-pill .sqs-button-element--primary:not(.ma-pricing-option-button)::before {
    border-radius: 300px;
  }
  .tweak-global-animations-animation-type-flex.secondary-button-shape-pill .sqs-button-element--secondary::before,
  .tweak-global-animations-animation-type-flex.tertiary-button-shape-pill .sqs-button-element--tertiary::before {
    border-radius: 300px;
  }
  .tweak-global-animations-animation-type-flex.primary-button-shape-oval .sqs-button-element--primary:not(.ma-pricing-option-button)::before {
    border-radius: 100%;
  }
  .tweak-global-animations-animation-type-flex.secondary-button-shape-oval .sqs-button-element--secondary::before,
  .tweak-global-animations-animation-type-flex.tertiary-button-shape-oval .sqs-button-element--tertiary::before {
    border-radius: 100%;
  }
  .tweak-global-animations-animation-type-flex.primary-button-shape-petal .sqs-button-element--primary:not(.ma-pricing-option-button)::before {
    border-radius: 16px 0px;
  }
  .tweak-global-animations-animation-type-flex.secondary-button-shape-petal .sqs-button-element--secondary::before,
  .tweak-global-animations-animation-type-flex.tertiary-button-shape-petal .sqs-button-element--tertiary::before {
    border-radius: 16px 0px;
  }
  .tweak-global-animations-animation-type-flex.primary-button-style-outline:not(.primary-button-shape-underline) .image-button a::before,
  .tweak-global-animations-animation-type-flex.primary-button-style-outline:not(.primary-button-shape-underline) .sqs-add-to-cart-button::before,
  .tweak-global-animations-animation-type-flex.primary-button-style-outline:not(.primary-button-shape-underline) .sqs-editable-button:not(input):not(.ma-pricing-option-button)::before,
  .tweak-global-animations-animation-type-flex.primary-button-style-outline:not(.primary-button-shape-underline) .sqs-button-element--primary:not(.ma-pricing-option-button)::before,
  .tweak-global-animations-animation-type-flex.primary-button-style-outline:not(.primary-button-shape-underline) [data-animation-role="header-element"] .btn::before {
    border-width: 0;
    bottom: -2px;
    left: -2px;
    right: -2px;
    top: -2px;
  }
  .tweak-global-animations-animation-type-flex.secondary-button-style-outline .sqs-button-element--secondary:not(input)::before {
    border-width: 0;
    bottom: -2px;
    left: -2px;
    right: -2px;
    top: -2px;
  }
  .tweak-global-animations-animation-type-flex.tertiary-button-style-outline .sqs-button-element--tertiary:not(input)::before {
    border-width: 0;
    bottom: -2px;
    left: -2px;
    right: -2px;
    top: -2px;
  }
  .tweak-global-animations-animation-type-flex.primary-button-shape-custom .sqs-button-element--primary:not(.ma-pricing-option-button)::before {
    border-top-left-radius: var(--primary-button-rounded-border-top-left-radius);
    border-top-right-radius: var(--primary-button-rounded-border-top-right-radius);
    border-bottom-left-radius: var(--primary-button-rounded-border-bottom-left-radius);
    border-bottom-right-radius: var(--primary-button-rounded-border-bottom-right-radius);
  }
  .tweak-global-animations-animation-type-flex.secondary-button-shape-custom .sqs-button-element--secondary::before {
    border-top-left-radius: var(--secondary-button-rounded-border-top-left-radius);
    border-top-right-radius: var(--secondary-button-rounded-border-top-right-radius);
    border-bottom-left-radius: var(--secondary-button-rounded-border-bottom-left-radius);
    border-bottom-right-radius: var(--secondary-button-rounded-border-bottom-right-radius);
  }
  .tweak-global-animations-animation-type-flex.tertiary-button-shape-custom .sqs-button-element--tertiary::before {
    border-top-left-radius: var(--tertiary-button-rounded-border-top-left-radius);
    border-top-right-radius: var(--tertiary-button-rounded-border-top-right-radius);
    border-bottom-left-radius: var(--tertiary-button-rounded-border-bottom-left-radius);
    border-bottom-right-radius: var(--tertiary-button-rounded-border-bottom-right-radius);
  }
}
.tweak-global-animations-animation-type-flex .animation-segment-parent-hidden {
  opacity: 0;
}
.tweak-global-animations-animation-type-flex .animation-segment-wrapper {
  display: inline-flex;
  overflow: hidden;
}
.tweak-global-animations-animation-type-flex .animation-segment-wrapper .animation-segment-interior {
  display: inline-block;
}
.tweak-global-animations-animation-type-flex .animation-segmented-flex-primed .animation-segment-interior {
  opacity: 0;
  transition-property: transform, opacity, clip-path;
  transform: translate(0%, 2vh);
  transform: translate(0%, 100%);
}
.tweak-global-animations-animation-type-flex .animation-segmented-flex-fired .animation-segment-interior {
  opacity: 1;
  transform: translate(0%, 0%);
}
.sqs-announcement-bar {
  position: relative;
  top: 0;
  left: 0;
  z-index: 10000;
  text-align: center;
}
.sqs-announcement-bar-url {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.sqs-announcement-bar-text {
  padding: 0.8em 3em;
}
.sqs-announcement-bar-text p {
  margin: 0;
}
.sqs-announcement-bar-text a {
  position: relative;
  text-decoration: underline !important;
}
.sqs-announcement-bar-close {
  cursor: pointer;
  position: absolute;
  top: 0;
  right: 0;
  width: 2.8em;
  height: 2.78em;
  background: rgba(0, 0, 0, 0.15);
}
.sqs-announcement-bar-close:after {
  content: '×';
  display: block;
  font-family: helvetica, arial, sans-serif;
  font-size: 1em;
  font-weight: 100;
  line-height: 2.7em;
  letter-spacing: normal;
  padding: 0;
}
.sqs-announcement-bar-hidden {
  display: none;
}
@media screen and (max-width: 1024px) {
  .sqs-announcement-bar-text,
  .sqs-announcement-bar-text p {
    font-size: 13px;
  }
}
.gdpr-cookie-banner,
.manage-cookies-overlay,
.manage-cookies-bar {
  background-color: var(--siteBackgroundColor);
}
.gdpr-cookie-banner p,
.manage-cookies-overlay p,
.manage-cookies-bar p {
  color: var(--paragraphSmallColor);
}
.gdpr-cookie-banner .close-icon svg > path,
.manage-cookies-overlay .close-icon svg > path,
.manage-cookies-bar .close-icon svg > path {
  fill: var(--paragraphSmallColor);
}
.gdpr-cookie-banner hr,
.manage-cookies-overlay hr,
.manage-cookies-bar hr {
  background-color: var(--paragraphSmallColor);
}
.manage-cookies-bar .manage-bar-action {
  color: var(--paragraphSmallColor);
}
.product-list {
  padding-top: var(--sqs-site-gutter);
  padding-bottom: var(--sqs-site-gutter);
}
.product-list [data-product-list-layout="grid"] {
  padding: 0;
}
.product-list [data-product-list-layout="grid"][data-section-width="full"] {
  padding-left: var(--sqs-site-gutter);
  padding-right: var(--sqs-site-gutter);
}
.product-list [data-product-list-layout="grid"][data-section-width="inset"] {
  max-width: var(--sqs-site-max-width);
  padding-left: var(--sqs-site-gutter);
  padding-right: var(--sqs-site-gutter);
  margin: 0 auto;
}
.product-list [data-product-list-layout="grid"][data-meta-text-alignment="left"] .product-list-item-meta {
  text-align: left;
}
.product-list [data-product-list-layout="grid"][data-meta-text-alignment="left"] .product-list-item-meta .product-list-item-status {
  text-align: right;
}
.product-list [data-product-list-layout="grid"][data-meta-text-alignment="center"] .product-list-item-meta {
  text-align: center;
}
.product-list [data-product-list-layout="grid"][data-meta-text-alignment="right"] .product-list-item-meta {
  text-align: right;
}
.product-list [data-product-list-layout="grid"][data-meta-text-alignment="right"] .product-list-item-meta .product-list-item-status {
  text-align: left;
}
.product-list [data-product-list-layout="grid"][data-category-display-type="sidebar"] .nested-category-children {
  display: none;
}
@media screen and (min-width:  576px ) {
  .product-list [data-product-list-layout="grid"][data-header-text-alignment="left"] .nested-category-title {
    text-align: left;
  }
  .product-list [data-product-list-layout="grid"][data-header-text-alignment="left"] .nested-category-children {
    justify-content: start;
  }
  .product-list [data-product-list-layout="grid"][data-header-text-alignment="left"] .nested-category-breadcrumb-list-item:first-child .nested-category-breadcrumb-link {
    margin-left: 0;
  }
  .product-list [data-product-list-layout="grid"][data-header-text-alignment="center"] .nested-category-title,
  .product-list [data-product-list-layout="grid"][data-header-text-alignment="center"] .nested-category-breadcrumb {
    justify-content: center;
    text-align: center;
  }
  .product-list [data-product-list-layout="grid"][data-header-text-alignment="center"] .nested-category-children {
    justify-content: center;
  }
}
@media screen and (max-width:  767px ) {
  .product-list [data-product-list-layout="grid"][data-section-width="full"] {
    padding-left: var(--sqs-mobile-site-gutter);
    padding-right: var(--sqs-mobile-site-gutter);
  }
  .product-list [data-product-list-layout="grid"][data-section-width="inset"] {
    padding-left: var(--sqs-mobile-site-gutter);
    padding-right: var(--sqs-mobile-site-gutter);
  }
}
@media screen and (max-width:  575px ) {
  .product-list [data-product-list-layout="grid"][data-category-display-type="sidebar"] .nested-category-children {
    display: flex;
  }
}
.product-list [data-product-list-layout="grid"] .nested-category-tree-wrapper {
  float: left;
  min-width: 220px;
  max-width: 300px;
  margin-right: 40px;
}
.product-list [data-product-list-layout="grid"] .nested-category-tree-wrapper ul {
  padding-inline-start: 20px;
  margin-top: 0;
}
.product-list [data-product-list-layout="grid"] .nested-category-tree-wrapper > ul {
  padding-left: 0;
}
.product-list [data-product-list-layout="grid"] .nested-category-tree-wrapper > ul > li:first-child a {
  padding-top: 0;
}
.product-list [data-product-list-layout="grid"] .nested-category-tree-wrapper li {
  list-style: none;
}
.product-list [data-product-list-layout="grid"] .nested-category-tree-wrapper .category-link {
  position: relative;
  display: block;
  padding: 6px 0;
  color: var(--tweak-product-grid-text-below-list-category-nav-color);
}
.product-list [data-product-list-layout="grid"] .nested-category-tree-wrapper .category-link.active {
  font-weight: 700;
}
.product-list [data-product-list-layout="grid"] .nested-category-tree-wrapper .category-link.activeParent {
  font-weight: 700;
}
@media screen and (max-width:  575px ) {
  .product-list [data-product-list-layout="grid"] .nested-category-tree-wrapper {
    display: none;
  }
}
.product-list .product-list-item {
  display: flex;
  flex-direction: column;
  position: relative;
  transform: translateY(-15px);
  transition: opacity 1s ease, transform 1s ease;
}
.product-list .product-list-item.is-loaded {
  opacity: 1;
  transform: translateY(0);
}
.product-list .product-list-item .product-list-item-link {
  display: flex;
  flex-direction: column;
  gap: 1vw;
}
.product-list .product-list-item .product-list-item-link .product-list-image-wrapper {
  position: relative;
}
.product-list .product-list-item .product-list-item-link .product-list-image-wrapper:hover .sqs-product-quick-view-button {
  opacity: 1;
}
.product-list .product-list-item .product-list-item-link .product-list-image-wrapper .sqs-product-quick-view-button-wrapper {
  display: flex;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  z-index: auto;
  padding-bottom: 0;
  align-items: center;
  justify-content: center;
}
.sqs-tweak-overlays-active .product-list .product-list-item .product-list-item-link {
  pointer-events: none;
}
.product-list .product-list-item .product-list-item-link .product-list-item-image .grid-image-wrapper {
  position: relative;
  aspect-ratio: var(--product-list-image-aspect-ratio);
}
.product-list .product-list-item .product-list-item-link .product-list-item-image .grid-item-image {
  height: 100%;
  width: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  transition: opacity 1s ease, transform 1s ease;
}
.product-list .product-list-item .product-list-item-link .product-list-item-image .grid-item-additional-image {
  opacity: 0;
}
.product-list .product-list-item .product-list-item-link .product-list-item-image .grid-image-selected {
  opacity: 1 !important;
}
.product-list .product-list-item .product-list-item-link .product-list-item-image .grid-image-not-selected {
  opacity: 0 !important;
}
.product-list .product-list-item .product-list-item-link .product-list-item-image .grid-image-cover {
  opacity: 1;
}
.product-list .product-list-item .product-list-item-link .product-list-item-image .grid-image-hover {
  opacity: 0;
}
@media (hover: hover) {
  .product-list .product-list-item .product-list-item-link .product-list-item-image:hover .has-hover-img .grid-image-cover {
    opacity: 0;
  }
  .product-list .product-list-item .product-list-item-link .product-list-item-image:hover .grid-image-hover.loaded {
    opacity: 1;
  }
}
.product-list .product-list-item .product-list-item-link .product-list-item-meta {
  margin-top: var(--product-list-image-text-spacing);
}
.product-list .product-list-item .product-list-item-link .product-list-item-meta .product-list-item-title {
  color: var(--tweak-product-grid-text-below-list-title-color);
}
.product-list .product-list-item .product-list-item-link .product-list-item-meta .product-list-item-price {
  color: var(--tweak-product-grid-text-below-list-price-color);
  margin-top: 0.5rem;
}
.product-list .product-list-item .product-list-item-link .product-list-item-meta .product-list-item-price .original-price {
  text-decoration: line-through;
}
.product-list .product-list-item .product-list-item-link .product-list-item-meta .product-list-item-status {
  color: var(--tweak-product-grid-text-below-list-status-color);
  font-weight: bold;
  text-transform: uppercase;
}
.product-list .product-list-item .product-list-item-link .product-list-item-meta .product-list-item-status .sale-status {
  color: var(--tweak-product-grid-text-below-list-sale-price-color);
}
.product-list .product-list-item .product-list-item-link .product-list-item-meta .product-list-item-status .grid-meta-status .product-scarcity {
  color: var(--tweak-product-grid-text-below-list-scarcity-color);
}
.product-list .product-list-item .product-variants {
  display: flex;
  flex-direction: column;
  gap: 11px;
  width: var(--product-content-form-width, "100%");
  margin-top: 22px;
}
.product-list .product-list-item .product-variants .variant-option {
  display: flex;
  flex-direction: column;
  gap: 6px;
  box-sizing: border-box;
  width: 100%;
  color: var(--tweak-product-basic-item-variant-fields-color);
}
.product-list .product-list-item .product-variants .variant-option .variant-option-title {
  display: none;
}
.product-list .product-list-item .product-variants .variant-select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  font-size: 1rem;
  min-height: 48px;
  text-align: left;
  box-sizing: border-box;
  padding: 0 1.3rem;
  line-height: normal;
  color: inherit;
}
.product-list .product-list-item .product-variants .variant-select-wrapper:before {
  content: attr(data-selected-value);
  display: inline-block;
  z-index: 1;
  pointer-events: none;
}
.product-list .product-list-item .product-variants .variant-select-wrapper:after {
  content: '';
  position: absolute;
  right: 1.3rem;
  transform: rotate(45deg);
  border-bottom: 1px solid;
  border-right: 1px solid;
  height: 0.6rem;
  width: 0.6rem;
  transform-origin: top;
  pointer-events: none;
  font-size: 1rem;
}
.product-list .product-list-item .product-variants .variant-select-wrapper select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  border-radius: 0;
  padding: 0;
  background: transparent;
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  font-size: 1rem;
  color: rgba(0, 0, 0, 0);
  border: 1px solid grey;
}
.product-list .product-list-item .product-variants .variant-select-wrapper select::-ms-expand {
  display: none;
}
.product-list .product-list-item .product-variants .variant-select-wrapper select:focus option {
  color: black;
}
.product-list .product-list-item .product-variants .variant-radiobtn-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  width: 100%;
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper {
  width: var(--product-content-form-width, "100%");
  display: flex;
  flex-direction: column;
  margin-top: auto;
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-view-options-button {
  width: 100%;
  align-items: center;
  margin-top: 22px;
  padding-left: 1.2rem !important;
  padding-right: 1.2rem !important;
  cursor: pointer;
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-view-options-button .sqs-view-options-button-inner {
  display: flex;
  justify-content: center;
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button {
  width: 100%;
  align-items: center;
  margin-top: 22px;
  padding-left: 1.2rem !important;
  padding-right: 1.2rem !important;
  cursor: pointer;
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .sqs-add-to-cart-button-inner {
  display: flex;
  justify-content: center;
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .cart-loader {
  border: 3px solid transparent;
  border-radius: 50%;
  border-top: 3px solid currentColor;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.sqs-button-element--primary .cart-loader {
  width: calc((var(--primary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
  height: calc((var(--primary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.sqs-button-element-secondary .cart-loader {
  width: calc((var(--secondary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
  height: calc((var(--secondary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.sqs-button-element-tertiary .cart-loader {
  width: calc((var(--tertiary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
  height: calc((var(--tertiary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .add-to-cart-text {
  display: block;
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .cart-loader {
  display: none;
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .cart-added-text {
  display: none;
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-adding .add-to-cart-text {
  display: none;
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-adding .cart-loader {
  display: block;
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-adding .cart-added-text {
  display: none;
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-added .add-to-cart-text {
  display: none;
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-added .cart-loader {
  display: none;
}
.product-list .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-added .cart-added-text {
  display: block;
}
@media screen and (max-width:  767px ) {
  .product-list .category-filter-container {
    height: 35px;
    overflow: hidden;
    margin-bottom: 10px;
    width: calc(100% + (var(--sqs-site-gutter) * 2));
    position: relative;
    left: calc(-1 * var(--sqs-site-gutter));
  }
}
.product-list .category-filter-wrapper {
  padding-bottom: 6vw;
}
@media screen and (max-width:  767px ) {
  .product-list .category-filter-wrapper {
    height: 60px;
    position: relative;
    width: 100%;
    overflow-x: scroll;
  }
  .product-list .category-filter-wrapper:before {
    content: "";
    display: block;
    width: var(--sqs-site-gutter);
    height: 1px;
    position: relative;
    float: left;
  }
}
.product-list .category-filter-list {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-content: center;
}
@media screen and (min-width:  768px ) {
  .product-list .category-filter-list {
    flex-wrap: wrap;
  }
}
@media screen and (max-width:  767px ) {
  .product-list .category-filter-list {
    justify-content: left;
  }
}
.product-list .category-filter-item {
  padding: 0 1.5vw;
  text-transform: capitalize;
}
.product-list .category-filter-item:first-child {
  padding-left: 0;
}
@media screen and (max-width:  767px ) {
  .product-list .category-filter-item {
    padding: 0 20px;
  }
}
.product-list .category-filter-delimiter {
  color: var(--tweak-product-grid-text-below-list-category-nav-color);
  opacity: 0;
}
.product-list .category-filter-delimiter.has-category {
  opacity: 1;
}
.product-list .category-filter-link {
  position: relative;
  transition: opacity 0.3s ease;
  white-space: nowrap;
}
.product-list .category-filter-link::after {
  content: "";
  position: absolute;
  width: 0px;
  height: 1px;
  left: 50%;
  bottom: -2px;
  transition: all 0.3s ease;
  background: var(--tweak-product-grid-text-below-list-category-nav-color);
}
.product-list .category-filter-link:hover,
.product-list .category-filter-link.category-filter-link-all,
.product-list .category-filter-link.active {
  opacity: 1;
  color: var(--tweak-product-grid-text-below-list-category-nav-color);
}
.product-list .category-filter-link:hover::after,
.product-list .category-filter-link.category-filter-link-all::after,
.product-list .category-filter-link.active::after {
  width: 100%;
  left: 0;
}
.product-list .category-filter-list.other-active .category-filter-link {
  opacity: 0.3;
  color: var(--tweak-product-grid-text-below-list-category-nav-color);
}
.product-list .category-filter-list.other-active .category-filter-link.category-filter-link-all:after {
  width: 0;
  left: 50%;
}
.product-list .category-filter-list.other-active .category-filter-link:hover,
.product-list .category-filter-list.other-active .category-filter-link.active {
  opacity: 1;
  color: var(--tweak-product-grid-text-below-list-category-nav-color);
}
.product-list .category-filter-list.other-active .category-filter-link:hover::after,
.product-list .category-filter-list.other-active .category-filter-link.active::after {
  width: 100%;
  left: 0;
}
.product-list .nested-category-children {
  padding-bottom: 54px;
  padding-top: 54px;
  padding-left: 0;
  margin-bottom: 37px;
  margin-top: 0;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  align-content: center;
  position: relative;
}
.product-list .nested-category-children .active {
  text-decoration: underline;
  font-weight: 700;
}
@media screen and (min-width:  576px ) {
  .product-list .nested-category-children::after {
    content: "";
    opacity: 0.2;
    border-bottom: 1px solid;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
}
@media screen and (min-width:  576px ) and screen and (max-width:  575px ) {
  .product-list .nested-category-children::after {
    content: none;
  }
}
@media screen and (max-width:  575px ) {
  .product-list .nested-category-children {
    padding-top: 44px;
    padding-bottom: 30px;
    margin-bottom: 0;
    flex-wrap: nowrap;
    overflow-x: scroll;
    justify-content: start;
  }
}
.product-list .nested-category-title-padding {
  padding-bottom: 54px;
  margin-bottom: 37px;
  position: relative;
  display: flex;
  flex-direction: row;
}
.product-list .nested-category-title-padding::after {
  content: "";
  opacity: 0.2;
  border-bottom: 1px solid;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}
@media screen and (max-width:  575px ) {
  .product-list .nested-category-title-padding::after {
    content: none;
  }
}
@media screen and (max-width:  575px ) {
  .product-list .nested-category-title-padding {
    padding-bottom: 30px;
    margin-bottom: 0;
  }
}
.product-list .nested-category-title {
  margin-top: 0;
  margin-bottom: 0;
}
.product-list .nested-category-title.nested-category-title-padding {
  padding-bottom: 54px;
  margin-bottom: 37px;
  position: relative;
  display: flex;
  flex-direction: row;
}
.product-list .nested-category-title.nested-category-title-padding::after {
  content: "";
  opacity: 0.2;
  border-bottom: 1px solid;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}
@media screen and (max-width:  575px ) {
  .product-list .nested-category-title.nested-category-title-padding::after {
    content: none;
  }
}
@media screen and (max-width:  575px ) {
  .product-list .nested-category-title.nested-category-title-padding {
    padding-bottom: 30px;
    margin-bottom: 0;
  }
}
@media screen and (min-width:  576px ) {
  .product-list .nested-category-title.sidebar-title-padding {
    padding-bottom: 54px;
    margin-bottom: 37px;
    position: relative;
    display: flex;
    flex-direction: row;
  }
  .product-list .nested-category-title.sidebar-title-padding::after {
    content: "";
    opacity: 0.2;
    border-bottom: 1px solid;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
}
@media screen and (min-width:  576px ) and screen and (max-width:  575px ) {
  .product-list .nested-category-title.sidebar-title-padding::after {
    content: none;
  }
}
@media screen and (min-width:  576px ) and screen and (max-width:  575px ) {
  .product-list .nested-category-title.sidebar-title-padding {
    padding-bottom: 30px;
    margin-bottom: 0;
  }
}
@media screen and (max-width:  575px ) {
  .product-list .nested-category-title {
    text-align: left;
  }
}
.product-list .nested-category-breadcrumb-list-item {
  display: flex;
}
.product-list .nested-category-breadcrumb-list-item .nested-category-breadcrumb-link {
  color: var(--tweak-product-grid-text-below-list-category-nav-color);
  margin: 0 1.5vw;
}
.product-list .nested-category-breadcrumb-list-item .nested-category-breadcrumb-link.bold {
  font-weight: 700;
}
.product-list .nested-category-breadcrumb-list-item:last-child .breadcrumb-separator {
  display: none;
}
@media screen and (max-width:  575px ) {
  .product-list .nested-category-breadcrumb-list-item {
    flex: 0 0 auto;
  }
}
.product-list .products-flex-container {
  display: flex;
  flex-direction: column;
}
.product-list .breadcrumb-separator {
  opacity: 0.2;
}
@media screen and (min-width:  576px ) {
  .product-list .nested-category-breadcrumb.extra-padding {
    padding-bottom: 54px;
    margin-bottom: 37px;
    position: relative;
    display: flex;
    flex-direction: row;
    gap: 4px;
  }
  .product-list .nested-category-breadcrumb.extra-padding::after {
    content: "";
    opacity: 0.2;
    border-bottom: 1px solid;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
}
@media screen and (min-width:  576px ) and screen and (max-width:  575px ) {
  .product-list .nested-category-breadcrumb.extra-padding::after {
    content: none;
  }
}
@media screen and (min-width:  576px ) and screen and (max-width:  575px ) {
  .product-list .nested-category-breadcrumb.extra-padding {
    padding-bottom: 30px;
    margin-bottom: 0;
  }
}
@media screen and (max-width:  575px ) {
  .product-list .nested-category-breadcrumb {
    text-align: left;
    text-transform: uppercase;
    opacity: 0.3;
  }
}
.product-list .tweak-global-animations-animation-type-flex .products .nested-category-breadcrumb-link {
  position: relative;
  display: inline-block;
  text-decoration: none;
}
.product-list .tweak-global-animations-animation-type-flex .products .nested-category-breadcrumb-link::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  left: 0;
  bottom: 0px;
  background-color: currentColor;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.6s cubic-bezier(0.19, 1, 0.22, 1);
}
@media (hover: hover) {
  .product-list .tweak-global-animations-animation-type-flex .products .nested-category-breadcrumb-link:hover::after {
    transform: scaleX(1);
    transform-origin: left;
  }
}
.product-list .tweak-global-animations-animation-type-flex .products .nested-category-tree-wrapper .category-link {
  position: relative;
  display: inline-block;
  text-decoration: none;
}
.product-list .tweak-global-animations-animation-type-flex .products .nested-category-tree-wrapper .category-link::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  left: 0;
  bottom: 6px;
  background-color: currentColor;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.6s cubic-bezier(0.19, 1, 0.22, 1);
}
@media (hover: hover) {
  .product-list .tweak-global-animations-animation-type-flex .products .nested-category-tree-wrapper .category-link:hover::after {
    transform: scaleX(1);
    transform-origin: left;
  }
}
.product-list .tweak-global-animations-animation-type-flex .products .nested-category-tree-wrapper .category-link.active::after,
.product-list .tweak-global-animations-animation-type-flex .products .nested-category-tree-wrapper .category-link.activeParent::after {
  transform: scaleX(1);
}
.product-list .list-pagination {
  padding-top: 6vw;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.product-list .list-pagination a {
  display: flex;
  flex: 1 0 50%;
}
.product-list .list-pagination a.list-pagination-next {
  justify-content: flex-end;
}
.product-list .list-pagination-nested-categories {
  text-transform: uppercase;
}
.product-list .list-pagination-nested-categories .product-list-pagination-icon {
  height: 15px;
  width: 15px;
}
.product-list .list-pagination-nested-categories .list-pagination-next {
  text-align: right;
  align-items: center;
}
.product-list .list-pagination-nested-categories .list-pagination-prev {
  align-items: center;
}
.product-list-container {
  display: grid;
  padding: 0;
}
@media screen and (min-width:  768px ) {
  .product-list-container {
    grid-template-columns: repeat(var(--product-list-num-columns-desktop), minmax(0, 1fr));
    grid-row-gap: var(--product-list-row-spacing);
    grid-column-gap: var(--product-list-column-spacing);
  }
}
@media screen and (max-width:  767px ) {
  .product-list-container {
    grid-template-columns: repeat(var(--product-list-num-columns-mobile), minmax(0, 1fr));
    grid-row-gap: 30px;
    grid-column-gap: 30px;
  }
}
.product-list-container .product-list-item {
  display: flex;
  flex-direction: column;
  position: relative;
  transform: translateY(-15px);
  transition: opacity 1s ease, transform 1s ease;
}
.product-list-container .product-list-item.is-loaded {
  opacity: 1;
  transform: translateY(0);
}
.product-list-container .product-list-item .product-list-item-link {
  display: flex;
  flex-direction: column;
  gap: 1vw;
}
.product-list-container .product-list-item .product-list-item-link .product-list-image-wrapper {
  position: relative;
}
.product-list-container .product-list-item .product-list-item-link .product-list-image-wrapper:hover .sqs-product-quick-view-button {
  opacity: 1;
}
.product-list-container .product-list-item .product-list-item-link .product-list-image-wrapper .sqs-product-quick-view-button-wrapper {
  display: flex;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  z-index: auto;
  padding-bottom: 0;
  align-items: center;
  justify-content: center;
}
.sqs-tweak-overlays-active .product-list-container .product-list-item .product-list-item-link {
  pointer-events: none;
}
.product-list-container .product-list-item .product-list-item-link .product-list-item-image .grid-image-wrapper {
  position: relative;
  aspect-ratio: var(--product-list-image-aspect-ratio);
}
.product-list-container .product-list-item .product-list-item-link .product-list-item-image .grid-item-image {
  height: 100%;
  width: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  transition: opacity 1s ease, transform 1s ease;
}
.product-list-container .product-list-item .product-list-item-link .product-list-item-image .grid-item-additional-image {
  opacity: 0;
}
.product-list-container .product-list-item .product-list-item-link .product-list-item-image .grid-image-selected {
  opacity: 1 !important;
}
.product-list-container .product-list-item .product-list-item-link .product-list-item-image .grid-image-not-selected {
  opacity: 0 !important;
}
.product-list-container .product-list-item .product-list-item-link .product-list-item-image .grid-image-cover {
  opacity: 1;
}
.product-list-container .product-list-item .product-list-item-link .product-list-item-image .grid-image-hover {
  opacity: 0;
}
@media (hover: hover) {
  .product-list-container .product-list-item .product-list-item-link .product-list-item-image:hover .has-hover-img .grid-image-cover {
    opacity: 0;
  }
  .product-list-container .product-list-item .product-list-item-link .product-list-item-image:hover .grid-image-hover.loaded {
    opacity: 1;
  }
}
.product-list-container .product-list-item .product-list-item-link .product-list-item-meta {
  margin-top: var(--product-list-image-text-spacing);
}
.product-list-container .product-list-item .product-list-item-link .product-list-item-meta .product-list-item-title {
  color: var(--tweak-product-grid-text-below-list-title-color);
}
.product-list-container .product-list-item .product-list-item-link .product-list-item-meta .product-list-item-price {
  color: var(--tweak-product-grid-text-below-list-price-color);
  margin-top: 0.5rem;
}
.product-list-container .product-list-item .product-list-item-link .product-list-item-meta .product-list-item-price .original-price {
  text-decoration: line-through;
}
.product-list-container .product-list-item .product-list-item-link .product-list-item-meta .product-list-item-status {
  color: var(--tweak-product-grid-text-below-list-status-color);
  font-weight: bold;
  text-transform: uppercase;
}
.product-list-container .product-list-item .product-list-item-link .product-list-item-meta .product-list-item-status .sale-status {
  color: var(--tweak-product-grid-text-below-list-sale-price-color);
}
.product-list-container .product-list-item .product-list-item-link .product-list-item-meta .product-list-item-status .grid-meta-status .product-scarcity {
  color: var(--tweak-product-grid-text-below-list-scarcity-color);
}
.product-list-container .product-list-item .product-variants {
  display: flex;
  flex-direction: column;
  gap: 11px;
  width: var(--product-content-form-width, "100%");
  margin-top: 22px;
}
.product-list-container .product-list-item .product-variants .variant-option {
  display: flex;
  flex-direction: column;
  gap: 6px;
  box-sizing: border-box;
  width: 100%;
  color: var(--tweak-product-basic-item-variant-fields-color);
}
.product-list-container .product-list-item .product-variants .variant-option .variant-option-title {
  display: none;
}
.product-list-container .product-list-item .product-variants .variant-select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  font-size: 1rem;
  min-height: 48px;
  text-align: left;
  box-sizing: border-box;
  padding: 0 1.3rem;
  line-height: normal;
  color: inherit;
}
.product-list-container .product-list-item .product-variants .variant-select-wrapper:before {
  content: attr(data-selected-value);
  display: inline-block;
  z-index: 1;
  pointer-events: none;
  max-width: 100%;
  word-break: break-word;
  padding-right: 20px;
}
.product-list-container .product-list-item .product-variants .variant-select-wrapper:after {
  content: '';
  position: absolute;
  right: 1.3rem;
  transform: rotate(45deg);
  border-bottom: 1px solid;
  border-right: 1px solid;
  height: 0.6rem;
  width: 0.6rem;
  transform-origin: top;
  pointer-events: none;
  font-size: 1rem;
}
.product-list-container .product-list-item .product-variants .variant-select-wrapper select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  border-radius: 0;
  padding: 0;
  background: transparent;
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  font-size: 1rem;
  color: rgba(0, 0, 0, 0);
  border: 1px solid grey;
}
.product-list-container .product-list-item .product-variants .variant-select-wrapper select::-ms-expand {
  display: none;
}
.product-list-container .product-list-item .product-variants .variant-select-wrapper select:focus option {
  color: black;
}
.product-list-container .product-list-item .product-variants .variant-radiobtn-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  width: 100%;
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper {
  width: var(--product-content-form-width, "100%");
  display: flex;
  flex-direction: column;
  margin-top: auto;
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-view-options-button {
  width: 100%;
  align-items: center;
  margin-top: 22px;
  padding-left: 1.2rem !important;
  padding-right: 1.2rem !important;
  cursor: pointer;
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-view-options-button .sqs-view-options-button-inner {
  display: flex;
  justify-content: center;
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button {
  width: 100%;
  align-items: center;
  margin-top: 22px;
  padding-left: 1.2rem !important;
  padding-right: 1.2rem !important;
  cursor: pointer;
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .sqs-add-to-cart-button-inner {
  display: flex;
  justify-content: center;
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .cart-loader {
  border: 3px solid transparent;
  border-radius: 50%;
  border-top: 3px solid currentColor;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.sqs-button-element--primary .cart-loader {
  width: calc((var(--primary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
  height: calc((var(--primary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.sqs-button-element-secondary .cart-loader {
  width: calc((var(--secondary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
  height: calc((var(--secondary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.sqs-button-element-tertiary .cart-loader {
  width: calc((var(--tertiary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
  height: calc((var(--tertiary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .add-to-cart-text {
  display: block;
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .cart-loader {
  display: none;
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .cart-added-text {
  display: none;
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-adding .add-to-cart-text {
  display: none;
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-adding .cart-loader {
  display: block;
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-adding .cart-added-text {
  display: none;
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-added .add-to-cart-text {
  display: none;
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-added .cart-loader {
  display: none;
}
.product-list-container .product-list-item .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-added .cart-added-text {
  display: block;
}
.mixin-clear-input-styles {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  border-radius: 0;
  padding: 0;
  background: transparent;
}
.mixin-clear-input-styles::-ms-expand {
  display: none;
}
.product-detail [hidden] {
  display: none !important;
}
@media screen and (min-width:  576px ) {
  .product-detail .hidden-up-sm {
    display: none !important;
  }
}
@media screen and (max-width:  575px ) {
  .product-detail .hidden-down-sm {
    display: none !important;
  }
}
@media screen and (min-width:  768px ) {
  .product-detail .hidden-up-md {
    display: none !important;
  }
}
@media screen and (max-width:  767px ) {
  .product-detail .hidden-down-md {
    display: none !important;
  }
}
.product-detail .sr-only:not(:focus):not(:active) {
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}
.product-detail > [data-product-detail-layout="simple"] {
  display: flex;
  flex-direction: column;
  gap: 22px;
  padding: 22px var(--sqs-site-gutter);
}
@media screen and (max-width:  767px ) {
  .product-detail > [data-product-detail-layout="simple"] {
    padding-left: var(--sqs-mobile-site-gutter);
    padding-right: var(--sqs-mobile-site-gutter);
  }
  .product-detail > [data-product-detail-layout="simple"] .product-content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 22px;
  }
}
@media screen and (min-width:  768px ) {
  .product-detail > [data-product-detail-layout="simple"] .product-content-wrapper {
    display: flex;
    flex-direction: row;
    align-items: start;
    gap: var(--product-content-horizontal-spacing);
  }
  .product-detail > [data-product-detail-layout="simple"] .product-content-wrapper .product-meta {
    flex: 1;
  }
}
.product-detail > [data-product-detail-layout="simple"][data-section-width="inset"] {
  max-width: var(--sqs-site-max-width);
  margin: 0 auto;
}
@media screen and (min-width:  768px ) {
  .product-detail > [data-product-detail-layout="simple"][data-gallery-placement="right"] .product-content-wrapper {
    flex-direction: row-reverse;
  }
}
@media screen and (min-width:  768px ) {
  .product-detail > [data-product-detail-layout="simple"][data-gallery-design="stacked"] .product-meta {
    position: -webkit-sticky;
    position: sticky;
    top: calc(var(--header-fixed-top-offset, 0px) + 20px);
  }
}
.product-detail > [data-product-detail-layout="simple"][data-content-vertical-alignment="center"]:not([data-gallery-design="stacked"]) .product-content-wrapper {
  align-items: center;
}
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="left"] .product-meta {
  text-align: left;
}
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="left"] .product-variants,
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="left"] .sqs-add-to-cart-button-wrapper,
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="left"] .quick-view-item-link {
  align-self: start;
}
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="left"] .variant-radiobtn-wrapper,
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="left"] .product-add-to-cart-layout-wrapper,
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="left"] .product-review-summary {
  justify-content: start;
}
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="center"] .product-meta {
  text-align: center;
}
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="center"] .product-variants,
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="center"] .sqs-add-to-cart-button-wrapper,
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="center"] .quick-view-item-link {
  align-self: center;
}
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="center"] .variant-radiobtn-wrapper,
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="center"] .product-add-to-cart-layout-wrapper,
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="center"] .product-review-summary {
  justify-content: center;
}
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="right"] .product-meta {
  text-align: right;
}
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="right"] .product-variants,
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="right"] .sqs-add-to-cart-button-wrapper,
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="right"] .quick-view-item-link {
  align-self: end;
}
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="right"] .variant-radiobtn-wrapper,
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="right"] .product-add-to-cart-layout-wrapper,
.product-detail > [data-product-detail-layout="simple"][data-content-horizontal-alignment="right"] .product-review-summary {
  justify-content: end;
}
.product-detail > [data-product-detail-layout="simple"][data-payment-plan-select-border-shape="pill"] .subs-otp-radio-wrapper {
  border-radius: 33px;
}
.product-detail > [data-product-detail-layout="simple"][data-payment-plan-select-border-shape="pill"] .subs-otp-radio-wrapper .one-time-purchase-option.otp-button-wrapper .subs-otp-button-label {
  border-radius: 33px;
}
.product-detail > [data-product-detail-layout="simple"][data-payment-plan-select-border-shape="pill"] .subs-otp-radio-wrapper .subscription-option.subs-button-wrapper .subs-otp-button-label {
  border-radius: 33px;
}
.product-detail > [data-product-detail-layout="simple"][data-payment-plan-select-border-shape="petal"] .subs-otp-radio-wrapper {
  border-radius: 16px 0px;
}
.product-detail > [data-product-detail-layout="simple"][data-payment-plan-select-border-shape="petal"] .subs-otp-radio-wrapper .one-time-purchase-option.otp-button-wrapper .subs-otp-button-label {
  border-radius: 16px 0px;
}
.product-detail > [data-product-detail-layout="simple"][data-payment-plan-select-border-shape="petal"] .subs-otp-radio-wrapper .subscription-option.subs-button-wrapper .subs-otp-button-label {
  border-radius: 16px 0px;
}
.product-detail > [data-product-detail-layout="simple"][data-payment-plan-select-border-shape="rounded-rectangle"] .subs-otp-radio-wrapper {
  border-radius: 10px;
}
.product-detail > [data-product-detail-layout="simple"][data-payment-plan-select-border-shape="rounded-rectangle"] .subs-otp-radio-wrapper .one-time-purchase-option.otp-button-wrapper .subs-otp-button-label {
  border-radius: 10px;
}
.product-detail > [data-product-detail-layout="simple"][data-payment-plan-select-border-shape="rounded-rectangle"] .subs-otp-radio-wrapper .subscription-option.subs-button-wrapper .subs-otp-button-label {
  border-radius: 10px;
}
.product-detail > [data-product-detail-layout="simple"] .quick-view-item-link {
  width: fit-content;
  opacity: 0.6;
  margin-top: 22px;
  border-bottom: 1px solid #b3b3b3;
  display: inline-block;
}
.product-detail > [data-product-detail-layout="half"] {
  display: flex;
  flex-direction: row;
  width: 100%;
}
@media screen and (max-width:  767px ) {
  .product-detail > [data-product-detail-layout="half"] {
    flex-direction: column;
  }
  .product-detail > [data-product-detail-layout="half"] > * {
    width: 100% !important;
  }
}
.product-detail > [data-product-detail-layout="half"] > * {
  width: 50%;
}
.product-detail > [data-product-detail-layout="half"] .product-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 22px;
  padding: 22px 2rem;
  box-sizing: border-box;
}
@media screen and (max-width:  767px ) {
  .product-detail > [data-product-detail-layout="half"] .product-content-wrapper {
    padding: var(--sqs-mobile-site-gutter);
  }
}
.product-detail > [data-product-detail-layout="full"] .product-meta {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: var(--sqs-site-gutter);
}
@media screen and (max-width:  767px ) {
  .product-detail > [data-product-detail-layout="full"] .product-meta {
    flex-direction: column;
    padding: var(--sqs-mobile-site-gutter);
  }
}
.product-detail > [data-product-detail-layout="full"] .product-meta .product-meta-section {
  display: flex;
  flex-direction: column;
  gap: 11px;
}
.product-detail > [data-product-detail-layout="full"] .product-meta .product-meta-section:first-child {
  width: 50%;
}
@media screen and (min-width:  768px ) {
  .product-detail > [data-product-detail-layout="full"] .product-meta .product-meta-section:last-child {
    padding-left: 2rem;
  }
}
@media screen and (min-width:  992px ) {
  .product-detail > [data-product-detail-layout="full"] .product-meta .product-meta-section:last-child {
    min-width: 450px;
  }
}
.product-detail > [data-product-detail-layout="wrap"][data-is-mobile] .pdp-form-wrapper {
  padding: var(--sqs-mobile-site-gutter);
}
.product-detail > [data-product-detail-layout="wrap"] .product-nav {
  margin: 22px 0;
}
.product-detail > [data-product-detail-layout="wrap"] .pdp-form-wrapper {
  display: flex;
  flex-direction: column;
}
.product-detail .product-meta {
  display: flex;
  flex-direction: column;
  gap: 11px;
  box-sizing: border-box;
}
.product-detail .product-meta .product-review-summary {
  display: flex;
  align-items: center;
  gap: 9px;
}
.product-detail .product-meta .product-review-summary .avgStars {
  display: flex;
  gap: 6px;
}
.product-detail .product-meta .product-review-summary .summaryLink {
  font-size: 1rem;
  padding: 0;
  margin: 0;
  text-decoration: underline;
  text-transform: capitalize;
  white-space: nowrap;
}
.product-detail .product-meta .product-review-summary .summaryText {
  margin: 0px;
}
.product-detail .product-meta .product-title {
  color: var(--tweak-product-basic-item-title-color);
  margin: 0;
}
.product-detail .product-meta .product-payment-method-messaging {
  color: var(--tweak-product-basic-item-description-color);
  background-color: var(--siteBackgroundColor);
  margin-top: 11px;
}
.product-detail .product-meta .product-price {
  color: var(--tweak-product-basic-item-price-color);
  margin-bottom: 5px;
}
.product-detail .product-meta .product-price .original-price {
  text-decoration: line-through;
}
.product-detail .product-meta .product-scarcity {
  margin-bottom: 5px;
  color: var(--tweak-product-basic-item-scarcity-color);
}
.product-detail .product-meta .product-status {
  color: var(--tweak-product-basic-item-description-color);
  font-weight: bold;
  text-transform: uppercase;
}
.product-detail .product-meta .product-description {
  color: var(--tweak-product-basic-item-description-color);
  margin-bottom: 5px;
}
.product-detail .product-meta .product-description > * {
  margin: 0;
}
.product-detail .product-meta .product-variants {
  display: flex;
  flex-direction: column;
  gap: 11px;
  width: var(--product-content-form-width, "100%");
}
.product-detail .product-meta .product-variants .variant-option {
  display: flex;
  flex-direction: column;
  gap: 6px;
  box-sizing: border-box;
  width: 100%;
  color: var(--tweak-product-basic-item-variant-fields-color);
}
.product-detail .product-meta .product-variants .variant-select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  font-size: 1rem;
  min-height: 66px;
  text-align: left;
  box-sizing: border-box;
  padding: 0 1.3rem;
  line-height: normal;
  color: inherit;
}
.product-detail .product-meta .product-variants .variant-select-wrapper:before {
  content: attr(data-selected-value);
  display: inline-block;
  z-index: 1;
  pointer-events: none;
  max-width: 100%;
  word-break: break-word;
  padding-right: 20px;
}
.product-detail .product-meta .product-variants .variant-select-wrapper:after {
  content: '';
  position: absolute;
  right: 1.3rem;
  transform: rotate(45deg);
  border-bottom: 1px solid;
  border-right: 1px solid;
  height: 0.6rem;
  width: 0.6rem;
  transform-origin: top;
  pointer-events: none;
  font-size: 1rem;
}
.product-detail .product-meta .product-variants .variant-select-wrapper select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  border-radius: 0;
  padding: 0;
  background: transparent;
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  font-size: 1rem;
  color: rgba(0, 0, 0, 0);
  border: 1px solid grey;
}
.product-detail .product-meta .product-variants .variant-select-wrapper select::-ms-expand {
  display: none;
}
.product-detail .product-meta .product-variants .variant-select-wrapper select:focus option {
  color: black;
}
.product-detail .product-meta .product-variants .variant-radiobtn-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  width: 100%;
}
.product-detail .product-meta .product-variants .variant-radiobtn-wrapper input[type="radio"] {
  display: none;
}
.product-detail .product-meta .product-variants .variant-radiobtn-wrapper input[type="radio"]:disabled + label {
  opacity: 30%;
  cursor: not-allowed;
}
.product-detail .product-meta .product-variants .variant-radiobtn-wrapper input[type="radio"]:checked + label {
  cursor: default;
}
.secondary-button-style-solid .product-detail .product-meta .product-variants .variant-radiobtn-wrapper input[type="radio"]:checked + label {
  background: var(--secondaryButtonTextColor);
  color: var(--secondaryButtonBackgroundColor);
}
.secondary-button-style-outline .product-detail .product-meta .product-variants .variant-radiobtn-wrapper input[type="radio"]:checked + label,
.secondary-button-style-solid.secondary-button-shape-underline .product-detail .product-meta .product-variants .variant-radiobtn-wrapper input[type="radio"]:checked + label {
  background: var(--secondaryButtonBackgroundColor);
  color: var(--secondaryButtonTextColor);
}
.product-detail .product-meta .product-variants .variant-radiobtn-wrapper label {
  cursor: pointer;
}
.product-detail .product-meta .product-restock-notification {
  display: flex;
  flex-direction: column;
  gap: 11px;
  margin-top: 16px;
  margin-bottom: 5px;
}
.product-detail .product-meta .product-restock-notification:not([hidden]) ~ .product-add-to-cart {
  display: none !important;
}
.product-detail .product-meta .product-restock-notification .product-restock-cta {
  line-height: 1;
  font-size: 1rem;
  color: var(--tweak-product-basic-item-description-color);
}
.product-detail .product-meta .product-restock-notification .product-restock-form {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}
@media screen and (max-width:  575px ) {
  .product-detail .product-meta .product-restock-notification .product-restock-form {
    display: block;
  }
  .product-detail .product-meta .product-restock-notification .product-restock-form input[type="submit"] {
    margin-top: 1rem;
  }
}
.product-detail .product-meta .product-restock-notification .product-restock-form input {
  font-size: 1rem;
  border: 1px solid;
  padding: 0.75rem;
}
.product-detail .product-meta .product-restock-notification .product-restock-form input[type="text"] {
  min-width: 250px;
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.12);
  padding: 1.2rem 2rem;
  box-sizing: border-box;
}
.product-detail .product-meta .product-restock-notification .product-restock-form input[type="text"]::placeholder {
  color: rgba(0, 0, 0, 0.3);
}
.product-detail .product-meta .product-restock-notification .product-restock-form input[type="submit"] {
  line-height: 1.25;
  padding: 1.2rem 2rem;
}
.product-detail .product-meta .product-restock-notification .product-restock-mailing-list {
  line-height: normal;
  font-size: 0.75rem;
}
.product-detail .product-meta .product-restock-notification .product-restock-mailing-list input {
  margin-right: 0.3rem;
  vertical-align: text-bottom;
}
.product-detail .product-meta .product-restock-notification .product-restock-success-message {
  padding: 14px;
  background-color: rgba(0, 0, 0, 0.05);
  font-size: 1rem;
  text-align: center;
  word-wrap: break-word;
}
.product-detail .product-meta .product-restock-notification .product-restock-error-message {
  font-size: 1rem;
  line-height: normal;
  color: #F0523D;
}
.product-detail .product-meta .product-restock-notification .captcha-container:empty {
  display: none;
}
.product-detail .product-meta .product-add-ons {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin: 5px 0;
}
.product-detail .product-meta .product-add-ons .add-on-card {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 11px;
  border: 1px solid grey;
  padding: 16px;
}
.product-detail .product-meta .product-add-ons .product-variants {
  flex: 1;
  align-self: auto;
}
@media screen and (max-width:  767px ) {
  .product-detail .product-meta .product-add-ons .product-variants {
    flex-basis: 100%;
    order: 100;
  }
}
.product-detail .product-meta .product-add-ons .product-variants .variant-option {
  margin-bottom: 0;
}
.product-detail .product-meta .product-add-ons .product-variants .variant-option-title,
.product-detail .product-meta .product-add-ons .product-variants .variant-out-of-stock {
  display: none;
}
.product-detail .product-meta .product-add-ons .product-variants .variant-select-wrapper {
  padding: 0 13px;
  min-height: 50px;
  font-size: 15px;
}
.product-detail .product-meta .product-add-ons .product-variants .variant-select-wrapper:after {
  right: 13px;
}
.product-detail .product-meta .product-add-ons .add-on-details {
  flex: 1;
  text-align: start;
}
.product-detail .product-meta .product-add-ons .add-on-details .add-on-title-link {
  display: block;
}
.product-detail .product-meta .product-add-ons .add-on-details .add-on-title {
  font-size: 18px;
  margin-bottom: 6px;
  color: var(--tweak-product-basic-item-description-color);
}
.product-detail .product-meta .product-add-ons .add-on-details .product-price {
  font-size: 15px;
  color: var(--tweak-product-basic-item-description-color);
  margin: 0;
}
.product-detail .product-meta .product-add-ons .add-on-thumbnail {
  width: 66px;
  height: 66px;
  object-fit: cover;
}
.product-detail .product-meta .product-add-ons .add-on-thumbnail.add-on-thumbnail-not-selected {
  display: none !important;
}
.product-detail .product-meta .product-add-ons .sqs-add-to-cart-button-wrapper {
  margin: auto;
  width: auto;
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper.hidden,
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper:before {
  display: none;
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button {
  border-radius: 50%;
  padding: 0 !important;
  height: 43px;
  width: 43px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 0;
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button .sqs-add-to-cart-button-inner,
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button .icons-container {
  display: flex;
  align-items: center;
  justify-content: center;
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button .icons-container > *:not(.add-icon) {
  display: none;
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button.cart-adding .add-icon {
  display: none;
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button.cart-adding .loading-icon {
  display: flex;
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button.cart-added .add-icon {
  display: none;
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button.cart-added .complete-icon {
  display: block;
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button .complete-icon {
  margin-left: 2px;
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button .loading-icon {
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 4px;
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button .loading-icon .loading-dot {
  background: var(--primaryButtonTextColor);
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: relative;
}
.primary-button-style-outline .product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button .loading-icon .loading-dot,
.primary-button-style-solid.primary-button-shape-underline .product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button .loading-icon .loading-dot {
  background: var(--primaryButtonBackgroundColor);
}
@keyframes loading-dot-2 {
  from {
    opacity: 0;
  }
  25% {
    opacity: 1;
  }
}
@keyframes loading-dot-3 {
  from {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button .loading-icon .loading-dot:nth-child(2) {
  animation: loading-dot-2 2.1s infinite steps(1);
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button .loading-icon .loading-dot:nth-child(3) {
  animation: loading-dot-3 2.1s infinite steps(1);
}
.primary-button-style-outline .product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button:hover .loading-dot {
  background: var(--primaryButtonTextColor);
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button svg {
  border-radius: 100%;
  width: 15px;
  height: 15px;
  fill: var(--primaryButtonTextColor);
}
.primary-button-style-outline .product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button svg,
.primary-button-style-solid.primary-button-shape-underline .product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button svg {
  fill: var(--primaryButtonBackgroundColor);
}
.product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button svg.add-icon {
  stroke: var(--primaryButtonTextColor);
  stroke-width: 2px;
}
.primary-button-style-outline .product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button svg.add-icon,
.primary-button-style-solid.primary-button-shape-underline .product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button svg.add-icon {
  stroke: var(--primaryButtonBackgroundColor);
}
.primary-button-style-outline .product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button:hover svg {
  fill: var(--primaryButtonTextColor);
}
.primary-button-style-outline .product-detail .product-meta .product-add-ons .add-on-add-to-cart-wrapper .sqs-add-to-cart-button:hover svg.add-icon {
  stroke: var(--primaryButtonTextColor);
}
.product-detail .product-meta .product-add-to-cart {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin: 5px 0;
}
.product-detail .product-meta .product-add-to-cart .product-quantity-input-wrapper {
  color: var(--tweak-product-basic-item-variant-fields-color);
}
.product-detail .product-meta .product-add-to-cart .product-quantity-input-wrapper label {
  display: block;
  margin-bottom: 6px;
}
.product-detail .product-meta .product-add-to-cart .product-quantity-input-wrapper input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  border-radius: 0;
  padding: 0;
  background: transparent;
  text-align: left;
  box-sizing: border-box;
  padding: 1.1rem 1.3rem;
  line-height: normal;
  color: inherit;
  border: 1px solid grey;
}
.product-detail .product-meta .product-add-to-cart .product-quantity-input-wrapper input::-ms-expand {
  display: none;
}
.product-detail .product-meta .product-add-to-cart .product-add-to-cart-layout-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
@media screen and (max-width:  767px ) {
  .product-detail .product-meta .product-add-to-cart .product-add-to-cart-layout-wrapper.add-to-cart-inline-md-down {
    flex-direction: row;
    align-items: end;
    gap: 6px;
  }
  .product-detail .product-meta .product-add-to-cart .product-add-to-cart-layout-wrapper.add-to-cart-inline-md-down .sqs-add-to-cart-button-wrapper {
    align-self: auto;
  }
}
@media screen and (min-width:  768px ) {
  .product-detail .product-meta .product-add-to-cart .product-add-to-cart-layout-wrapper.add-to-cart-inline-md-up {
    flex-direction: row;
    align-items: end;
    gap: 6px;
  }
  .product-detail .product-meta .product-add-to-cart .product-add-to-cart-layout-wrapper.add-to-cart-inline-md-up .sqs-add-to-cart-button-wrapper {
    align-self: auto;
  }
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper {
  width: var(--product-content-form-width, "100%");
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button {
  width: 100%;
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .sqs-add-to-cart-button-inner {
  display: flex;
  justify-content: center;
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .cart-loader {
  border: 3px solid transparent;
  border-radius: 50%;
  border-top: 3px solid currentColor;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.sqs-button-element--primary .cart-loader {
  width: calc((var(--primary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
  height: calc((var(--primary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.sqs-button-element-secondary .cart-loader {
  width: calc((var(--secondary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
  height: calc((var(--secondary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.sqs-button-element-tertiary .cart-loader {
  width: calc((var(--tertiary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
  height: calc((var(--tertiary-button-font-font-size-value, 1) - 1) * 1.2vw + 1.2rem);
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .add-to-cart-text {
  display: block;
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .cart-loader {
  display: none;
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button .cart-added-text {
  display: none;
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-adding .add-to-cart-text {
  display: none;
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-adding .cart-loader {
  display: block;
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-adding .cart-added-text {
  display: none;
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-added .add-to-cart-text {
  display: none;
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-added .cart-loader {
  display: none;
}
.product-detail .product-meta .product-add-to-cart .sqs-add-to-cart-button-wrapper .sqs-add-to-cart-button.cart-added .cart-added-text {
  display: block;
}
.product-detail .product-meta .product-subs-otp {
  display: block;
}
.product-detail .product-meta .product-subs-otp .frequency-label {
  margin-bottom: 6px;
  color: var(--tweak-product-basic-item-variant-fields-color);
}
.product-detail .product-meta .product-subs-otp .subs-otp-radio-wrapper {
  border: 1px solid gray;
}
.product-detail .product-meta .product-subs-otp .subs-otp-radio-wrapper .subscription-option {
  border-top: 1px solid gray;
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .subs-and-otp-label {
  display: flex;
  flex: 1;
  align-items: center;
  color: var(--product-detail-subscriptions-title-color);
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .radio-wrapper {
  display: flex;
  align-items: center;
  text-align: left;
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .radio-wrapper input[name="otp-subs-radio"] {
  appearance: none;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background-clip: content-box;
  background-color: #fff;
  border: 1px solid #9b9b9b;
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .radio-wrapper input[name="otp-subs-radio"]:checked {
  background-color: #d6d6d6;
  border: none;
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .radio-wrapper input[name="otp-subs-radio"]:checked:after {
  content: "";
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: black;
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .radio-wrapper label {
  padding-left: 11px;
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .product-price,
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .subscription-product-price {
  text-align: end;
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .one-time-purchase-product-price {
  color: var(--product-detail-one-time-purchase-price-text-color);
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .one-time-purchase-product-price.no-percentage {
  display: none;
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .subscription-product-price {
  margin-bottom: 6px;
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .subscription-product-price .subscription-price {
  color: var(--product-detail-subscription-price-text-color);
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .subscription-product-price.no-percentage {
  display: none;
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .sale-percentage,
.product-detail .product-meta .product-subs-otp .subs-and-otp-option select {
  margin-top: 6px;
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option select {
  background: transparent;
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .sale-percentage {
  color: var(--product-detail-subscriptions-description-text-color);
}
.product-detail .product-meta .product-subs-otp .subs-and-otp-option .sale-percentage.no-percentage {
  display: none;
}
.product-detail .product-meta .product-subs-otp .subscription-option {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.product-detail .product-meta .product-subs-otp .subscription-option select {
  border: none;
  color: var(--product-detail-subscriptions-frequency-text-color);
}
.product-detail .product-meta .product-subs-otp .subscription-option select {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  font-size: 1rem;
  color: rgba(0, 0, 0, 0);
  appearance: none;
}
@media screen and (max-width:  767px ) {
  .product-detail .product-meta .product-subs-otp .subscription-option select {
    overflow-x: hidden;
  }
}
.product-detail .product-meta .product-subs-otp .subscription-option select:focus option {
  color: black;
}
@media screen and (max-width:  767px ) {
  .product-detail .product-meta .product-subs-otp {
    order: 3;
  }
}
.product-detail .product-meta .product-subs-otp .subscription-frequency {
  margin-top: 6px;
  color: var(--product-detail-subscriptions-frequency-text-color);
}
.product-detail .product-meta .product-subs-otp .subscription-frequency.no-percentage {
  margin-top: 0;
}
.product-detail .product-meta .product-subs-otp .subscription-frequency-select-wrapper {
  display: flex;
  gap: 5px;
  position: relative;
  opacity: 0;
  transition: opacity 0.1s ease-out;
  color: var(--product-detail-subscriptions-frequency-text-color);
}
.product-detail .product-meta .product-subs-otp .subscription-frequency-select-wrapper select {
  margin-top: 0;
}
.product-detail .product-meta .product-subs-otp .subscription-frequency-select-wrapper[data-text] {
  opacity: 1;
}
.product-detail .product-meta .product-subs-otp .subscription-frequency-select-wrapper:before {
  content: attr(data-text);
  display: inline-block;
  text-align: right;
}
.product-detail .product-meta .product-subs-otp .subscription-frequency-select-wrapper .dropdown-icon {
  display: flex;
  align-items: center;
  width: 20px;
}
.product-detail .product-meta .product-subs-otp .subscription-frequency-select-wrapper .dropdown-icon:after {
  content: '';
  position: absolute;
  right: 0;
  transform: rotate(45deg);
  border-bottom: 1px solid;
  border-right: 1px solid;
  height: 10px;
  width: 10px;
  transform-origin: top;
  pointer-events: none;
  font-size: 1rem;
  color: var(--product-detail-subscriptions-frequency-text-color);
  margin-bottom: 4px;
}
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .subscription-product-price {
  margin-bottom: 6px;
}
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .otp-button-wrapper,
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .subs-button-wrapper {
  padding: 0;
}
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .otp-button-wrapper .subs-otp-button-label,
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .subs-button-wrapper .subs-otp-button-label {
  width: 100%;
  padding: 1.5rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  border-style: solid;
  border-color: grey;
  background-color: var(--product-detail-subscriptions-button-background-color);
  border-color: var(--product-detail-subscriptions-button-text-color);
}
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .otp-button-wrapper .subs-otp-button-label *,
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .subs-button-wrapper .subs-otp-button-label *,
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .otp-button-wrapper .subs-otp-button-label .dropdown-icon::after,
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .subs-button-wrapper .subs-otp-button-label .dropdown-icon::after {
  color: var(--product-detail-subscriptions-button-text-color);
}
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .otp-button-wrapper .subs-otp-button-label .subscription-frequency,
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .subs-button-wrapper .subs-otp-button-label .subscription-frequency {
  color: transparent;
}
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .otp-button-wrapper .subs-otp-button-label .subscription-frequency:focus,
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .subs-button-wrapper .subs-otp-button-label .subscription-frequency:focus {
  outline-color: var(--product-detail-subscriptions-button-text-color);
}
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .otp-button-wrapper .subs-otp-button-label .subs-and-otp-label,
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .subs-button-wrapper .subs-otp-button-label .subs-and-otp-label {
  padding-left: 0;
}
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .otp-button-wrapper input[name="subs-otp-button"],
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .subs-button-wrapper input[name="subs-otp-button"] {
  display: none;
}
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .otp-button-wrapper input[name="subs-otp-button"]:checked + .subs-otp-button-label,
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .subs-button-wrapper input[name="subs-otp-button"]:checked + .subs-otp-button-label {
  background-color: var(--product-detail-subscriptions-button-text-color);
  border-color: var(--product-detail-subscriptions-button-background-color);
}
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .otp-button-wrapper input[name="subs-otp-button"]:checked + .subs-otp-button-label *,
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .subs-button-wrapper input[name="subs-otp-button"]:checked + .subs-otp-button-label *,
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .otp-button-wrapper input[name="subs-otp-button"]:checked + .subs-otp-button-label .dropdown-icon::after,
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .subs-button-wrapper input[name="subs-otp-button"]:checked + .subs-otp-button-label .dropdown-icon::after {
  color: var(--product-detail-subscriptions-button-background-color);
}
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .otp-button-wrapper input[name="subs-otp-button"]:checked + .subs-otp-button-label .subscription-frequency:focus,
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .subs-button-wrapper input[name="subs-otp-button"]:checked + .subs-otp-button-label .subscription-frequency:focus {
  outline-color: var(--product-detail-subscriptions-button-background-color);
}
.product-detail .product-meta .product-subs-otp .subs-otp-button-wrapper .subs-button-wrapper {
  margin-top: 11px;
}
.product-detail .product-nav {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  z-index: 1;
}
@media screen and (max-width:  767px ) {
  .product-detail .product-nav {
    display: none;
  }
}
.product-detail .ProductItem-additional {
  width: 100%;
  padding: var(--sqs-site-gutter);
  margin: 0 auto;
  box-sizing: border-box;
}
.product-detail .product-related-products {
  padding: 0 var(--sqs-site-gutter);
}
.product-detail .product-related-products [data-product-list-layout="grid"] {
  max-width: unset !important;
  padding: 0 !important;
}
.product-detail .star {
  width: 1rem;
  height: 1rem;
  fill: var(--paragraphMediumColor);
}
.product-detail .star path {
  fill: var(--paragraphMediumColor);
}
.product-detail .product-reviews {
  padding: 0 var(--sqs-site-gutter);
}
@media screen and (max-width:  767px ) {
  .product-detail .product-reviews {
    padding: 0 var(--sqs-mobile-site-gutter);
  }
}
.product-detail .product-reviews .loader {
  height: 20px;
  width: 20px;
  perspective: 80px;
  margin: auto;
  padding: 10vw;
}
.product-detail .product-reviews .loader .indicator {
  background: black;
  height: 20px;
  width: 20px;
  animation-name: product-reviews-loader-flip-square;
  animation-duration: 1.6s;
  animation-iteration-count: infinite;
  animation-timing-function: cubic-bezier(0.66, 0, 0.34, 1);
  transform-style: preserve-3d;
}
@keyframes product-reviews-loader-flip-square {
  0% {
    transform: rotateY(0deg);
  }
  12.5% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(180deg);
  }
  62.5% {
    transform: rotateY(180deg);
  }
  100% {
    transform: rotateZ(180deg);
  }
}
.product-detail .product-reviews .reviewTabSectionContainer * {
  color: inherit;
}
.product-detail .product-reviews .reviewsSection {
  padding-top: 2rem;
  padding-bottom: 9rem;
}
.product-detail .product-reviews .reviewsContainer {
  padding-inline-start: 0px;
  list-style-type: none;
  list-style-position: outside;
}
.product-detail .product-reviews .productLink {
  text-decoration: underline;
}
.product-detail .product-reviews .reviewInfo {
  padding-right: 12px;
}
.product-detail .product-reviews .reviewStars {
  display: inline-flex;
  gap: 2px;
  width: 50%;
  margin: 0;
}
.product-detail .product-reviews .reviewDesc {
  margin: 8px 0 0 0;
  text-transform: none;
}
.product-detail .product-reviews .reviewName {
  margin: 0;
  font-weight: bold;
  padding-bottom: 11px;
}
.product-detail .product-reviews .reviewStamp {
  font-size: 1rem;
}
.product-detail .product-reviews .reviewSeparator {
  padding: 3px;
  font-weight: bold;
}
.product-detail .product-reviews .reviewDivider {
  width: 100%;
  opacity: 25%;
}
.product-detail .product-reviews .reviewImgContainer {
  margin: 0;
  padding: 0;
  background: none;
  border: 0;
  display: flex;
}
.product-detail .product-reviews .reviewTitle {
  display: flex;
  align-items: center;
  gap: 11px;
  margin-top: 22px;
  line-height: 1;
}
.product-detail .product-reviews .reviewTitle p {
  margin: 0;
}
.product-detail .product-reviews .reviewImg {
  height: 33px;
}
.product-detail .product-reviews .showMoreButton {
  display: block;
  margin: 33px auto;
  text-transform: uppercase;
  border: none;
  text-decoration: underline;
  text-underline-offset: 0.2em;
  background-color: transparent;
  color: inherit;
}
.product-detail .product-reviews .showMoreReviewsButton {
  display: block;
  margin: 33px auto;
  text-transform: uppercase;
  border: none;
  text-underline-offset: 0.2em;
  background-color: transparent;
  color: inherit;
  border: 1px solid;
  border-color: #E7E7E7;
  padding: 16px;
}
.product-detail .product-reviews .reviewSource {
  display: flex;
  align-items: center;
  margin-top: 11px;
}
.product-detail .product-reviews .sourceText {
  margin: 0;
  opacity: 70%;
}
.product-detail .product-reviews .ReviewTabsTitle {
  margin-bottom: 15px;
}
.product-detail .product-reviews .ReviewTabsTitle .averageStarsContainer {
  display: flex;
  gap: 11px;
  line-height: 22px;
}
.product-detail .product-reviews .ReviewTabsTitle .averageStarsContainer .averageStars {
  display: flex;
  gap: 6px;
}
.product-detail .product-reviews .ReviewTabsTitle .averageStarsContainer .averageStars .star {
  transform: scale(1.25);
  width: 22px;
}
.product-detail .product-reviews .averageStarsNumber {
  font-weight: bold;
  font-size: 1.125rem;
}
.product-detail .product-reviews .reviewsTabButtonContainer {
  float: left;
  border: none;
  outline: none;
  padding: 14px 0 16px;
  margin-right: 22px;
}
.product-detail .product-reviews .reviewsTabButton {
  border: none;
  background-color: transparent;
  padding-left: 0;
  padding-right: 0;
}
.product-detail .product-reviews .activeReviewsTabButton {
  border-width: 0 0 2px 0;
  border-color: #000;
  border-style: solid;
}
.product-detail .product-reviews .activeReviewsTabButtonText {
  font-weight: bold;
}
.product-detail .product-reviews .activeReviewsTab {
  display: block;
}
.product-detail .product-reviews .inactiveReviewsTab {
  display: none;
}
.product-detail .product-reviews .reviewsTab {
  border: none;
}
.product-detail .product-reviews .a11yNotification {
  height: 0;
}
.product-detail .product-reviews .reviewsSectionTitle {
  margin-bottom: 22px;
}
.product-detail .product-reviews .reviewDD {
  margin: 0px;
}
@media only screen and (min-width: 430px) {
  .product-detail .product-reviews .leftColumn {
    width: 20%;
    float: left;
  }
  .product-detail .product-reviews .rightColumn {
    float: right;
    width: 80%;
  }
  .product-detail .product-reviews .reviewDetails {
    display: inline-block;
    width: 100%;
    padding: 13px 0;
  }
}
@media only screen and (max-width: 430px) {
  .product-detail .product-reviews .reviewsSection p,
  .product-detail .product-reviews .reviewsSection div,
  .product-detail .product-reviews .reviewsSection button {
    font-size: 0.875rem;
  }
  .product-detail .product-reviews .reviewsSection .reviewName {
    display: inline;
  }
  .product-detail .product-reviews .reviewsContainer dl {
    margin: 0;
  }
  .product-detail .product-reviews .reviewsTabButton {
    font-size: 0.875rem;
  }
  .product-detail .product-reviews .ReviewTabsTitle {
    margin-bottom: 5px;
  }
  .product-detail .product-reviews .ReviewTabsTitle .averageStarsContainer {
    flex-direction: column;
  }
  .product-detail .product-reviews .ReviewTabsTitle .averageStarsContainer .averageStarsNumber {
    font-size: 1.125rem;
  }
  .product-detail .product-reviews .rightColumn {
    margin-top: 11px;
  }
  .product-detail .product-reviews .reviewTitle {
    margin-top: 11px;
  }
  .product-detail .product-reviews .reviewDivider {
    margin: 0;
  }
  .product-detail .product-reviews .reviewDetails {
    display: inline-block;
    width: 100%;
    padding: 22px 0;
  }
}
.reviewImgLightbox {
  top: 50%;
  position: relative;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  max-width: 90%;
  max-height: 90%;
}
@keyframes gallery-image-enter-from-right {
  from {
    transform: translateX(101%);
  }
  to {
    transform: translateX(0);
  }
}
@keyframes gallery-image-exit-to-left {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-101%);
  }
}
@keyframes gallery-image-enter-from-left {
  from {
    transform: translateX(-101%);
  }
  to {
    transform: translateX(0);
  }
}
@keyframes gallery-image-exit-to-right {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(101%);
  }
}
.product-gallery {
  display: flex;
  gap: 10px;
  align-items: flex-start;
  overflow: hidden;
  user-select: none;
  margin: 0;
}
@media screen and (max-width:  767px ) {
  .product-gallery {
    width: 100%;
  }
}
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .product-gallery {
    width: min(var(--product-gallery-width), 50%);
  }
}
@media screen and (min-width: 1025px) and (max-width: 1280px) {
  .product-gallery {
    width: min(var(--product-gallery-width), 60%);
  }
}
@media screen and (min-width: 1281px) {
  .product-gallery {
    width: var(--product-gallery-width);
  }
}
[data-gallery-placement="right"] .product-gallery {
  flex-direction: row-reverse;
}
[data-gallery-thumbnail-placement="below"] .product-gallery {
  flex-flow: column-reverse;
}
[data-gallery-placement="right"][data-gallery-thumbnail-placement="below"] .product-gallery {
  align-items: flex-end;
}
.product-gallery .enable-on-first-image-load {
  opacity: 0;
  transition: opacity 0.5s;
  pointer-events: none;
}
.product-gallery .enable-on-first-image-load.enabled {
  opacity: 1;
  pointer-events: auto;
}
.product-gallery-current-slide-indicator {
  position: absolute;
  top: 18px;
  right: 20px;
  display: inline-block;
  z-index: 3;
  font-size: 12px;
  color: var(--tweak-product-basic-item-gallery-controls-color);
}
@media screen and (min-width:  768px ) {
  .product-gallery-current-slide-indicator {
    display: none;
  }
}
.product-gallery-carousel-controls {
  --product-gallery-carousel-control-click-area-size: calc(var(--product-gallery-arrow-size) + 20px);
  position: absolute;
  width: 100%;
  left: 0;
  z-index: 3;
  justify-content: space-between;
  top: 50%;
  transform: translateY(-50%);
  display: none;
}
.product-gallery-carousel-controls.enable-on-first-image-load.enabled {
  pointer-events: none;
}
.product-gallery-carousel-controls.hidden {
  display: none !important;
}
@media screen and (max-width:  767px ) {
  .product-gallery-carousel-controls {
    display: flex;
  }
}
@media screen and (min-width:  768px ) {
  [data-gallery-design="carousel"] .product-gallery-carousel-controls,
  [data-gallery-design="slideshow"] .product-gallery-carousel-controls {
    display: flex;
  }
}
.product-gallery-carousel-controls .product-gallery-carousel-control {
  padding: 0;
  border: none;
  background-color: transparent;
  width: var(--product-gallery-carousel-control-click-area-size);
  height: var(--product-gallery-carousel-control-click-area-size);
  min-width: 50px;
  min-height: 50px;
  cursor: pointer;
  margin: 0 1.5vw;
  display: flex;
  align-items: center;
  pointer-events: auto;
}
.product-gallery-carousel-controls .product-gallery-carousel-control:after {
  display: block;
  content: '';
  width: var(--product-gallery-arrow-size);
  height: var(--product-gallery-arrow-size);
  border-style: solid;
  border-color: var(--tweak-product-basic-item-gallery-controls-color);
  border-width: 1px 1px 0 0;
  margin: calc((var(--product-gallery-carousel-control-click-area-size) - var(--product-gallery-arrow-size)) / 2);
}
.product-gallery-carousel-controls .product-gallery-carousel-control:focus {
  outline-color: var(--tweak-product-basic-item-gallery-controls-color);
}
.product-gallery-carousel-controls .product-gallery-carousel-control.product-gallery-prev {
  left: 0;
  justify-content: flex-start;
}
.product-gallery-carousel-controls .product-gallery-carousel-control.product-gallery-prev:after {
  transform: rotate(225deg);
}
.product-gallery-carousel-controls .product-gallery-carousel-control.product-gallery-next {
  right: 0;
  justify-content: flex-end;
}
.product-gallery-carousel-controls .product-gallery-carousel-control.product-gallery-next:after {
  transform: rotate(45deg);
}
.product-gallery-slides {
  position: relative;
  flex-grow: 1;
  max-width: 100%;
  aspect-ratio: var(--product-gallery-aspect-ratio);
}
@media screen and (min-width:  768px ) {
  .product-gallery-slides {
    overflow: hidden;
  }
  [data-gallery-design="stacked"] .product-gallery-slides {
    aspect-ratio: unset;
  }
}
[data-gallery-thumbnail-placement="below"] .product-gallery-slides {
  width: 100%;
}
.product-gallery-slides-item {
  width: 100%;
}
.product-gallery-slides-item.selected {
  z-index: 2;
}
.product-gallery-slides-item:focus {
  outline-width: 0;
}
.product-gallery-slides-item:focus::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  outline-width: var(--sqs-focus-outline-width);
  outline-style: var(--sqs-focus-outline-style);
  outline-offset: var(--sqs-focus-outline-offset-flush);
  outline-color: currentcolor;
}
.product-gallery-slides-item:focus:not(:focus-visible)::after {
  outline-style: none;
}
@media screen and (max-width:  767px ) {
  .product-gallery-slides-item {
    visibility: hidden;
  }
  .product-gallery-slides-item.going-prev.next-slide {
    display: block;
    visibility: visible;
  }
  .product-gallery-slides-item.going-prev.next-slide.prev-loaded {
    animation: gallery-image-exit-to-right 0.5s ease-in-out both;
    z-index: 1;
  }
  .product-gallery-slides-item.going-prev.selected {
    display: block;
    visibility: visible;
  }
  .product-gallery-slides-item.going-prev.selected.loaded {
    animation: gallery-image-enter-from-left 0.5s ease-in-out both;
  }
  .product-gallery-slides-item.going-next.prev-slide {
    display: block;
    visibility: visible;
  }
  .product-gallery-slides-item.going-next.prev-slide.next-loaded {
    animation: gallery-image-exit-to-left 0.5s ease-in-out both;
    z-index: 1;
  }
  .product-gallery-slides-item.going-next.selected {
    display: block;
    visibility: visible;
  }
  .product-gallery-slides-item.going-next.selected.loaded {
    animation: gallery-image-enter-from-right 0.5s ease-in-out both;
  }
}
@media screen and (min-width:  768px ) {
  [data-gallery-design="slideshow"] .product-gallery-slides-item:not(.selected) {
    opacity: 0;
  }
  [data-gallery-design="carousel"] .product-gallery-slides-item {
    visibility: hidden;
  }
  [data-gallery-design="carousel"] .product-gallery-slides-item.going-prev.next-slide {
    display: block;
    visibility: visible;
  }
  [data-gallery-design="carousel"] .product-gallery-slides-item.going-prev.next-slide.prev-loaded {
    animation: gallery-image-exit-to-right 0.5s ease-in-out both;
    z-index: 1;
  }
  [data-gallery-design="carousel"] .product-gallery-slides-item.going-prev.selected {
    display: block;
    visibility: visible;
  }
  [data-gallery-design="carousel"] .product-gallery-slides-item.going-prev.selected.loaded {
    animation: gallery-image-enter-from-left 0.5s ease-in-out both;
  }
  [data-gallery-design="carousel"] .product-gallery-slides-item.going-next.prev-slide {
    display: block;
    visibility: visible;
  }
  [data-gallery-design="carousel"] .product-gallery-slides-item.going-next.prev-slide.next-loaded {
    animation: gallery-image-exit-to-left 0.5s ease-in-out both;
    z-index: 1;
  }
  [data-gallery-design="carousel"] .product-gallery-slides-item.going-next.selected {
    display: block;
    visibility: visible;
  }
  [data-gallery-design="carousel"] .product-gallery-slides-item.going-next.selected.loaded {
    animation: gallery-image-enter-from-right 0.5s ease-in-out both;
  }
  [data-gallery-design="stacked"] .product-gallery-slides-item {
    margin-bottom: 25px;
    position: relative;
    height: 100%;
    overflow: hidden;
  }
  [data-gallery-design="stacked"] .product-gallery-slides-item:last-child {
    margin-bottom: 0;
  }
}
[data-gallery-design="slideshow"] .product-gallery-slides-item,
[data-gallery-design="carousel"] .product-gallery-slides-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding-bottom: 0;
  display: none;
}
[data-gallery-design="slideshow"] .product-gallery-slides-item.selected,
[data-gallery-design="carousel"] .product-gallery-slides-item.selected,
[data-gallery-design="slideshow"] .product-gallery-slides-item.thumbnail-hovered,
[data-gallery-design="carousel"] .product-gallery-slides-item.thumbnail-hovered {
  display: block;
}
[data-gallery-design="slideshow"] .product-gallery-slides-item.prev-slide,
[data-gallery-design="carousel"] .product-gallery-slides-item.prev-slide,
[data-gallery-design="slideshow"] .product-gallery-slides-item.next-slide,
[data-gallery-design="carousel"] .product-gallery-slides-item.next-slide {
  display: block;
}
@media screen and (max-width:  767px ) {
  .product-gallery-slides-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding-bottom: 0;
    display: none;
  }
  .product-gallery-slides-item.selected,
  .product-gallery-slides-item.thumbnail-hovered {
    display: block;
  }
  .product-gallery-slides-item.prev-slide,
  .product-gallery-slides-item.next-slide {
    display: block;
  }
}
[data-gallery-click-action="lightbox"] .product-gallery-slides-item {
  cursor: pointer;
}
[data-gallery-click-action="zoom"]:not([data-gallery-hover-action="zoom"]) .product-gallery-slides-item,
[data-gallery-click-action="zoom"][data-gallery-design="carousel"] .product-gallery-slides-item {
  cursor: zoom-in;
}
[data-gallery-click-action="zoom"]:not([data-gallery-hover-action="zoom"]) .product-gallery-slides-item.is-zoomed,
[data-gallery-click-action="zoom"][data-gallery-design="carousel"] .product-gallery-slides-item.is-zoomed {
  cursor: zoom-out;
}
.is-zoomed .product-gallery-slides-item-image {
  visibility: hidden;
}
@media screen and (min-width:  768px ) {
  [data-gallery-design="stacked"] .product-gallery-slides-item-image {
    display: block;
    width: 100%;
    /*
              These override imageloader's left and right, which can be computed incorrectly when the user switches
              from slideshow to stacked and ImageLoader.load runs and measures the container before the layout reflows.
             */
    left: 0 !important;
    right: 0 !important;
  }
}
.product-gallery-slides-item:not(.loaded) .product-gallery-slides-item-image {
  opacity: 0;
}
.product-gallery-scroll {
  display: none;
  position: relative;
  scrollbar-width: none;
}
.product-gallery-scroll:after {
  display: block;
  content: '';
  height: 10vh;
  width: 100%;
  position: sticky;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, var(--siteBackgroundColor) 100%);
}
@media screen and (min-width:  768px ) {
  [data-gallery-design="slideshow"] .product-gallery-scroll {
    display: block;
  }
}
[data-gallery-thumbnail-placement="side"] .product-gallery-scroll {
  width: auto;
  max-height: 80vh;
  overflow-y: auto;
}
@media screen and (max-width:  767px ) {
  [data-gallery-thumbnail-placement="side"] .product-gallery-scroll {
    margin-right: 0;
  }
}
[data-gallery-thumbnail-placement="below"] .product-gallery-scroll {
  width: 100%;
}
[data-gallery-thumbnail-placement="below"] .product-gallery-scroll:after {
  display: none;
}
.product-gallery-thumbnails {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  line-height: 0;
}
[data-gallery-thumbnail-placement="below"] .product-gallery-thumbnails {
  flex-direction: row;
}
[data-gallery-design="slideshow"] .product-gallery-thumbnails {
  margin-bottom: 10vh;
}
[data-gallery-design="slideshow"][data-gallery-thumbnail-placement="below"] .product-gallery-thumbnails {
  overflow-x: auto;
  white-space: nowrap;
}
.product-gallery-thumbnails-item {
  background: transparent;
  border: 0;
  width: 50px;
  cursor: pointer;
  padding: 0;
  transition: opacity 0.3s ease-in;
}
.product-gallery-thumbnails-item:not(.loaded) {
  opacity: 0;
}
.product-gallery-thumbnails-item img {
  aspect-ratio: var(--product-gallery-aspect-ratio);
}
.product-gallery .product-image-zoom-duplicate {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}
.product-gallery .product-image-zoom-duplicate img {
  width: 100%;
}
.product-gallery .product-gallery-slides-item.loaded.is-zoomed .product-image-zoom-duplicate {
  opacity: 1;
}
.product-detail .pdp-gallery .selected {
  z-index: 2;
  visibility: visible;
}
.product-detail .pdp-gallery .pdp-gallery-wrapper {
  position: relative;
  overflow: hidden;
}
.product-detail .pdp-gallery .pdp-carousel-controls {
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 3;
}
.product-detail .pdp-gallery .pdp-carousel-controls.hidden {
  display: none !important;
}
.product-detail .pdp-gallery .pdp-carousel-controls .chevron-prev,
.product-detail .pdp-gallery .pdp-carousel-controls .chevron-next {
  border: none;
  position: absolute;
  padding: 1.5rem;
  min-height: 44px;
  min-width: 44px;
  color: var(--paragraphMediumColor);
  background: var(--siteBackgroundColor);
}
.product-detail .pdp-gallery .pdp-carousel-controls .chevron-prev:disabled,
.product-detail .pdp-gallery .pdp-carousel-controls .chevron-next:disabled {
  color: var(--paragraphMediumColor);
}
.product-detail .pdp-gallery .pdp-carousel-controls .chevron-prev:disabled span,
.product-detail .pdp-gallery .pdp-carousel-controls .chevron-next:disabled span {
  opacity: 0.25;
}
.product-detail .pdp-gallery .pdp-carousel-controls .chevron-prev .chevron,
.product-detail .pdp-gallery .pdp-carousel-controls .chevron-next .chevron {
  display: block;
  width: 1rem;
  height: 1rem;
  margin: 0;
}
.product-detail .pdp-gallery .pdp-carousel-controls .chevron-prev {
  left: 0;
}
.product-detail .pdp-gallery .pdp-carousel-controls .chevron-prev .chevron--left {
  transform: translateX(0.25rem) rotate(225deg);
}
.product-detail .pdp-gallery .pdp-carousel-controls .chevron-next {
  right: 0;
}
.product-detail .pdp-gallery .pdp-carousel-controls .chevron-next .chevron--right {
  transform: translateX(-0.25rem) rotate(45deg);
}
.product-detail .pdp-gallery .pdp-gallery-slide-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 3;
  font-size: 0.75rem;
}
@media screen and (max-width:  767px ) {
  .product-detail .pdp-gallery .pdp-gallery-wrapper::before {
    content: '';
    display: block;
    padding-bottom: 100%;
  }
  .product-detail .pdp-gallery .pdp-gallery-images {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    max-height: 100%;
  }
  .product-detail .pdp-gallery .pdp-gallery-images::before {
    content: '';
    display: block;
    padding-bottom: 100%;
  }
  .product-detail .pdp-gallery .pdp-gallery-images .pdp-gallery-slides {
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    visibility: hidden;
  }
  .product-detail .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-prev.next-slide {
    display: block;
    visibility: visible;
  }
  .product-detail .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-prev.next-slide.prev-loaded {
    animation: gallery-image-exit-to-right 0.5s ease-in-out both;
    z-index: 1;
  }
  .product-detail .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-prev.selected {
    display: block;
    visibility: visible;
  }
  .product-detail .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-prev.selected.loaded {
    animation: gallery-image-enter-from-left 0.5s ease-in-out both;
  }
  .product-detail .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-next.prev-slide {
    display: block;
    visibility: visible;
  }
  .product-detail .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-next.prev-slide.next-loaded {
    animation: gallery-image-exit-to-left 0.5s ease-in-out both;
    z-index: 1;
  }
  .product-detail .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-next.selected {
    display: block;
    visibility: visible;
  }
  .product-detail .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-next.selected.loaded {
    animation: gallery-image-enter-from-right 0.5s ease-in-out both;
  }
}
@media screen and (min-width:  768px ) {
  .product-detail > [data-product-detail-layout="full"] .pdp-gallery .pdp-gallery-images {
    display: flex;
    flex-direction: row;
    transition: all 0.45s cubic-bezier(0.25, 0.1, 0.25, 1);
    max-height: 80vh;
  }
  .product-detail > [data-product-detail-layout="full"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides {
    flex: 0 0 auto;
    order: 1;
    display: flex;
    align-items: center;
    width: 50%;
  }
  .product-detail > [data-product-detail-layout="full"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides[data-landscape="true"] {
    width: 100%;
  }
  .product-detail > [data-product-detail-layout="half"] .pdp-gallery .pdp-gallery-images::before {
    content: '';
    display: block;
    padding-bottom: 100%;
  }
  .product-detail > [data-product-detail-layout="half"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    visibility: hidden;
  }
  .product-detail > [data-product-detail-layout="half"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-prev.next-slide {
    display: block;
    visibility: visible;
  }
  .product-detail > [data-product-detail-layout="half"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-prev.next-slide.prev-loaded {
    animation: gallery-image-exit-to-right 0.5s ease-in-out both;
    z-index: 1;
  }
  .product-detail > [data-product-detail-layout="half"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-prev.selected {
    display: block;
    visibility: visible;
  }
  .product-detail > [data-product-detail-layout="half"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-prev.selected.loaded {
    animation: gallery-image-enter-from-left 0.5s ease-in-out both;
  }
  .product-detail > [data-product-detail-layout="half"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-next.prev-slide {
    display: block;
    visibility: visible;
  }
  .product-detail > [data-product-detail-layout="half"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-next.prev-slide.next-loaded {
    animation: gallery-image-exit-to-left 0.5s ease-in-out both;
    z-index: 1;
  }
  .product-detail > [data-product-detail-layout="half"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-next.selected {
    display: block;
    visibility: visible;
  }
  .product-detail > [data-product-detail-layout="half"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.going-next.selected.loaded {
    animation: gallery-image-enter-from-right 0.5s ease-in-out both;
  }
  .product-detail > [data-product-detail-layout="wrap"] .pdp-gallery .pdp-carousel-controls {
    display: none;
  }
  .product-detail > [data-product-detail-layout="wrap"] .pdp-gallery .pdp-gallery-images {
    display: inline-flex;
    flex-direction: row;
    width: 100%;
    flex-wrap: wrap;
    overflow: hidden;
    padding: 0 4vw;
    box-sizing: border-box;
    margin: -10px;
  }
  .product-detail > [data-product-detail-layout="wrap"] .pdp-gallery .pdp-gallery-images > * {
    border: solid 10px rgba(0, 0, 0, 0);
    box-sizing: border-box;
  }
  .product-detail > [data-product-detail-layout="wrap"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides {
    position: relative;
    overflow: hidden;
    width: 50%;
    order: 2;
  }
  .product-detail > [data-product-detail-layout="wrap"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides.selected {
    order: 0;
  }
  .product-detail > [data-product-detail-layout="wrap"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides:not(.loaded) .product-detail > [data-product-detail-layout="wrap"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides-image {
    opacity: 0;
  }
  .product-detail > [data-product-detail-layout="wrap"] .pdp-gallery .pdp-gallery-images .pdp-gallery-slides[data-landscape="true"]:not(.selected) {
    width: 100%;
  }
  .product-detail > [data-product-detail-layout="wrap"] .pdp-gallery .pdp-gallery-images .pdp-form-wrapper {
    width: 50%;
    order: 1;
  }
}
