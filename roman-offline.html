<!doctype html>
<html lang="it-IT">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Roman Fontana nutrizionista</title>
    <meta name="description" content="Percorso personalizzato di nutrizione, allenamento e coaching con Roman, esperto in biologia e fitness, per il tuo equilibrio fisico e mentale duraturo.">
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    <link rel="canonical" href="https://www.romanfontana.com/">
    
    <!-- Essential Meta Tags -->
    <meta property="og:title" content="Roman Fontana Nutrizionista">
    <meta property="og:description" content="Percorso personalizzato di nutrizione, allenamento e coaching con Roman, esperto in biologia e fitness, per il tuo equilibrio fisico e mentale duraturo.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://romanfontana.com/">
    
    <!-- Essential Fonts -->
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=EB+Garamond:ital,wght@0,400..800;1,400..800&display=swap" rel="stylesheet">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "LocalBusiness",
      "name": "Roman Fontana",
      "description": "Nutrizionista e coach specializzato in percorsi personalizzati",
      "address": "Parma, Emilia-Romagna, Italy",
      "email": "<EMAIL>",
      "telephone": "+393484940741",
      "url": "https://www.romanfontana.com"
    }
    </script>
    
    <!-- Essential Styles -->
    <style>
      /* Reset and Base Styles */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'EB Garamond', serif;
        line-height: 1.6;
        color: #33322e;
        background-color: #fff;
      }
      
      .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 4vw;
      }
      
      /* Hero Section */
      .hero-section {
        position: relative;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
      }
      
      .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
      }
      
      .hero-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: 77.5% 16.8%;
      }
      
      .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.1);
      }
      
      .hero-content {
        position: relative;
        z-index: 2;
        text-align: center;
        max-width: 800px;
        padding: 2rem;
      }
      
      .hero-shape {
        background: hsla(36, 50%, 96%, 1);
        border-radius: 50px;
        padding: 3rem;
        margin-bottom: 2rem;
      }
      
      .hero-title {
        font-size: clamp(3rem, 5vw, 4.5rem);
        font-weight: 600;
        margin-bottom: 1.5rem;
        color: #33322e;
      }
      
      .hero-subtitle h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: #33322E;
      }
      
      .highlight {
        text-decoration: underline;
        text-decoration-color: #c17a47;
        text-decoration-thickness: 0.1em;
      }
      
      .hero-description {
        font-size: 1.25rem;
        margin-bottom: 2rem;
        line-height: 1.7;
      }
      
      .btn {
        display: inline-block;
        padding: 1rem 2rem;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 2px solid transparent;
      }
      
      .btn-primary {
        background: #6E7D58;
        color: white;
      }
      
      .btn-primary:hover {
        background: #5a6647;
        transform: translateY(-2px);
      }
      
      .btn-secondary {
        background: transparent;
        color: #6E7D58;
        border-color: #6E7D58;
      }
      
      .btn-secondary:hover {
        background: #6E7D58;
        color: white;
      }
      
      /* About Section */
      .about-section {
        padding: 5rem 0;
        background: #f9f9f9;
      }
      
      .quote-text {
        font-size: clamp(2rem, 4vw, 3.6rem);
        font-style: italic;
        font-weight: 600;
        text-align: center;
        color: #5C3D2E;
        margin-bottom: 4rem;
        max-width: 900px;
        margin-left: auto;
        margin-right: auto;
      }
      
      .about-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: center;
      }
      
      .about-image {
        text-align: center;
      }
      
      .profile-image {
        width: 100%;
        max-width: 400px;
        height: auto;
        border-radius: 250px 250px 250px 50px;
        object-fit: cover;
        object-position: 27.9% 43.2%;
      }
      
      .about-title {
        font-size: clamp(2.5rem, 4vw, 4.5rem);
        font-weight: 600;
        color: #6E7D58;
        margin-bottom: 1.5rem;
      }
      
      .about-description p {
        font-size: 1.25rem;
        margin-bottom: 1.5rem;
        line-height: 1.7;
      }
      
      .credentials {
        margin: 2rem 0;
        padding: 1.5rem;
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
      
      .credentials p {
        font-size: 1.1rem;
        line-height: 1.8;
      }
      
      /* Process Section */
      .process-section {
        padding: 5rem 0;
        background: white;
      }
      
      .section-title {
        font-size: clamp(2.5rem, 4vw, 4rem);
        font-weight: 600;
        text-align: center;
        margin-bottom: 3rem;
        color: #33322e;
      }
      
      .process-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 3rem;
        margin-top: 3rem;
      }
      
      .process-step {
        text-align: center;
        padding: 2rem;
        background: #f9f9f9;
        border-radius: 20px;
        transition: transform 0.3s ease;
      }
      
      .process-step:hover {
        transform: translateY(-5px);
      }
      
      .step-number {
        width: 60px;
        height: 60px;
        background: #6E7D58;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: bold;
        margin: 0 auto 1.5rem;
      }
      
      .process-step h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: #33322e;
      }
      
      .process-step p {
        font-size: 1.1rem;
        line-height: 1.6;
        color: #666;
      }
      
      /* Services Section */
      .services-section {
        padding: 5rem 0;
        background: #f9f9f9;
      }

      .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
      }

      .service-card {
        background: white;
        padding: 2.5rem;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .service-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      }

      .service-icon {
        font-size: 3rem;
        margin-bottom: 1.5rem;
      }

      .service-card h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: #6E7D58;
      }

      .service-card p {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 1.5rem;
        color: #666;
      }

      .service-card ul {
        list-style: none;
        padding: 0;
      }

      .service-card li {
        padding: 0.5rem 0;
        border-bottom: 1px solid #eee;
        color: #666;
      }

      .service-card li:before {
        content: "✓ ";
        color: #6E7D58;
        font-weight: bold;
      }

      /* Contact Section */
      .contact-section {
        padding: 5rem 0;
        background: #33322e;
        color: white;
      }

      .contact-section .section-title {
        color: white;
      }

      .contact-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        margin-top: 3rem;
      }

      .contact-info h3 {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
        color: #c17a47;
      }

      .contact-details p {
        margin-bottom: 1rem;
        font-size: 1.1rem;
      }

      .contact-details a {
        color: #c17a47;
        text-decoration: none;
      }

      .contact-details a:hover {
        text-decoration: underline;
      }

      .studios {
        margin-top: 2rem;
      }

      .studios h4 {
        font-size: 1.2rem;
        margin-bottom: 1rem;
        color: #c17a47;
      }

      /* Form Styles */
      .contact-form h3 {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
        color: #c17a47;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
      }

      .form-group input,
      .form-group textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 1rem;
        font-family: inherit;
      }

      .form-group input:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: #6E7D58;
        box-shadow: 0 0 0 2px rgba(110, 125, 88, 0.2);
      }

      .checkbox-group {
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
      }

      .checkbox-label {
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
        cursor: pointer;
        font-size: 0.9rem;
        line-height: 1.4;
      }

      .checkbox-label input[type="checkbox"] {
        width: auto;
        margin: 0;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .about-grid,
        .contact-grid {
          grid-template-columns: 1fr;
          gap: 2rem;
        }

        .hero-content {
          padding: 1rem;
        }

        .hero-shape {
          padding: 2rem;
        }

        .quote-text {
          font-size: 2.25rem;
        }
      }

      /* Smooth scrolling */
      html {
        scroll-behavior: smooth;
      }
    </style>
  </head>
  
  <body class="roman-fontana-site">
    <div id="siteWrapper" class="site-wrapper">
      <main id="page" class="container" role="main">
        <article class="sections" id="sections">
          
          <!-- Hero Section -->
          <section class="hero-section">
            <div class="hero-background">
              <img src="assets/images/roman_fontana_slider_home.jpg" 
                   alt="Roman Fontana nutrizionista" 
                   class="hero-image"
                   loading="eager">
              <div class="hero-overlay"></div>
            </div>
            
            <div class="hero-content">
              <div class="hero-shape">
                <h1 class="hero-title">Ritrova il tuo equilibrio fisico e mentale:</h1>
                
                <div class="hero-subtitle">
                  <h3><span class="highlight">Inizia il percorso studiato per te:</span></h3>
                  <p class="hero-description">In qualità di Biologo specializzato in <strong>nutrizione, allenamento e coaching del cambiamento</strong>, ti guiderò nel tuo percorso di trasformazione per garantirti <strong>risultati concreti e definitivi.</strong></p>
                </div>
                
                <div class="hero-button">
                  <a href="#contact" class="btn btn-primary">Prenota un appuntamento</a>
                </div>
              </div>
            </div>
          </section>

          <!-- About Section -->
          <section class="about-section">
            <div class="container">
              <div class="about-content">
                <h2 class="quote-text">"il momento ideale per cambiare era ieri ma adesso è il momento migliore per iniziare la tua trasformazione."</h2>

                <div class="about-grid">
                  <div class="about-image">
                    <img src="assets/images/roman_fontana_nutrizionista_felice.jpg"
                         alt="Roman Fontana nutrizionista sorridente"
                         class="profile-image"
                         loading="lazy">
                  </div>

                  <div class="about-text">
                    <h3 class="about-title">Soluzioni personalizzate per risultati duraturi</h3>

                    <div class="about-description">
                      <p><strong>Mi chiamo Roman Fontana</strong>, sono un esperto in Biologia, Nutrizione e Scienze del Fitness, con una formazione accademica in questi ambiti e <strong>oltre dieci anni di esperienza.</strong></p>
                      <p>Integro alimentazione, movimento e crescita personale per offrire <strong>soluzioni personalizzate e sostenibili, pensate per migliorare il tuo benessere</strong> con un approccio su misura.</p>
                    </div>

                    <div class="credentials">
                      <p>✓ Laurea in Scienze biologiche<br>
                      ✓ Laurea in Scienze del Fitness<br>
                      ✓ Diploma da Personal Trainer<br>
                      ✓ Master in Nutrizione Sportiva<br>
                      ✓ Master in Medicina Biointegrata<br>
                      ✓ Specializzazione in Coaching Trasformazionale<br>
                      ✓ Membro dell'Ordine Nazionale dei Biologi</p>
                    </div>

                    <div class="about-button">
                      <a href="#contact" class="btn btn-secondary">Prenota un appuntamento</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Process Section -->
          <section class="process-section">
            <div class="container">
              <h2 class="section-title">Come funziona il percorso?</h2>

              <div class="process-grid">
                <div class="process-step">
                  <div class="step-number">1</div>
                  <h3>Consulenza iniziale</h3>
                  <p>Analizziamo insieme i tuoi obiettivi, le tue abitudini attuali e definiamo un piano personalizzato per te.</p>
                </div>

                <div class="process-step">
                  <div class="step-number">2</div>
                  <h3>Piano personalizzato</h3>
                  <p>Ricevi un programma su misura che integra nutrizione, allenamento e coaching per risultati duraturi.</p>
                </div>

                <div class="process-step">
                  <div class="step-number">3</div>
                  <h3>Supporto continuo</h3>
                  <p>Ti accompagno passo dopo passo con monitoraggio costante e aggiustamenti del piano quando necessario.</p>
                </div>
              </div>
            </div>
          </section>

          <!-- Services Section -->
          <section class="services-section">
            <div class="container">
              <h2 class="section-title">Come posso aiutarti?</h2>

              <div class="services-grid">
                <div class="service-card">
                  <div class="service-icon">🥗</div>
                  <h3>Nutrizione: Mangiare sano non è mai stato così facile</h3>
                  <p>Piani alimentari personalizzati che si adattano al tuo stile di vita, senza rinunce estreme ma con risultati concreti.</p>
                  <ul>
                    <li>Analisi delle abitudini alimentari</li>
                    <li>Piani nutrizionali personalizzati</li>
                    <li>Educazione alimentare</li>
                    <li>Supporto per disturbi alimentari</li>
                  </ul>
                </div>

                <div class="service-card">
                  <div class="service-icon">💪</div>
                  <h3>Allenamento: definisci il tuo corpo</h3>
                  <p>Programmi di allenamento studiati per i tuoi obiettivi specifici, che puoi seguire in palestra o a casa.</p>
                  <ul>
                    <li>Programmi di allenamento personalizzati</li>
                    <li>Coaching per la forma fisica</li>
                    <li>Supporto motivazionale</li>
                    <li>Monitoraggio dei progressi</li>
                  </ul>
                </div>

                <div class="service-card">
                  <div class="service-icon">🧠</div>
                  <h3>Coaching: trasforma la tua mentalità</h3>
                  <p>Lavoriamo insieme sui blocchi mentali e sulle abitudini che ti impediscono di raggiungere i tuoi obiettivi.</p>
                  <ul>
                    <li>Coaching del cambiamento</li>
                    <li>Gestione dello stress</li>
                    <li>Sviluppo di abitudini sane</li>
                    <li>Supporto psicologico</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          <!-- Contact Section -->
          <section id="contact" class="contact-section">
            <div class="container">
              <h2 class="section-title">Sei pronto a iniziare?</h2>

              <div class="contact-grid">
                <div class="contact-info">
                  <h3>Contattami a questi recapiti:</h3>
                  <div class="contact-details">
                    <p><strong>Tel.</strong> +39 ************</p>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>?subject=Sono%20pronto%20sensei!"><EMAIL></a></p>

                    <div class="studios">
                      <h4>I miei studi:</h4>
                      <p><a href="https://www.google.com/maps/search/?api=1&query=Via+Maestri+del+Lavoro+17,+60033,+Chiaravalle+(AN)" target="_blank">Via Maestri del Lavoro 17, 60033, Chiaravalle (AN)</a></p>
                      <p><a href="https://www.google.com/maps/search/?api=1&query=Via+S.+Martino+25C,+60131,+Ancona+(AN)" target="_blank">Via S. Martino 25C, 60131, Ancona (AN)</a></p>
                      <p><a href="https://www.google.com/maps/search/?api=1&query=Via+Che+Guevara+79/B,+60022,+Castelfidardo+(AN)" target="_blank">Via Che Guevara 79/B, 60022, Castelfidardo (AN)</a></p>
                    </div>
                  </div>
                </div>

                <div class="contact-form">
                  <h3>Oppure scrivimi direttamente qui:</h3>
                  <form id="contactForm" action="#" method="post">
                    <div class="form-group">
                      <label for="name">Nome *</label>
                      <input type="text" id="name" name="name" required>
                    </div>

                    <div class="form-group">
                      <label for="email">Email *</label>
                      <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                      <label for="phone">Telefono</label>
                      <input type="tel" id="phone" name="phone">
                    </div>

                    <div class="form-group">
                      <label for="message">Messaggio *</label>
                      <textarea id="message" name="message" rows="5" required placeholder="Raccontami i tuoi obiettivi..."></textarea>
                    </div>

                    <div class="form-group checkbox-group">
                      <label class="checkbox-label">
                        <input type="checkbox" id="privacy" name="privacy" required>
                        <span class="checkmark"></span>
                        Accetto la <a href="#" target="_blank">privacy policy</a> *
                      </label>
                    </div>

                    <div class="form-group checkbox-group">
                      <label class="checkbox-label">
                        <input type="checkbox" id="newsletter" name="newsletter">
                        <span class="checkmark"></span>
                        Desidero ricevere comunicazioni dopo il corso
                      </label>
                    </div>

                    <button type="submit" class="btn btn-primary">Invia messaggio</button>
                  </form>
                </div>
              </div>
            </div>
          </section>
        </article>
      </main>
    </div>

    <!-- Essential JavaScript -->
    <script>
      // Simple form handling
      document.getElementById('contactForm').addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        // Basic validation
        if (!data.name || !data.email || !data.message) {
          alert('Per favore compila tutti i campi obbligatori.');
          return;
        }

        if (!data.privacy) {
          alert('Devi accettare la privacy policy per continuare.');
          return;
        }

        // Simulate form submission
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        submitButton.textContent = 'Invio in corso...';
        submitButton.disabled = true;

        // Simulate API call
        setTimeout(() => {
          alert('Messaggio inviato con successo! Ti contatterò presto.');
          this.reset();
          submitButton.textContent = originalText;
          submitButton.disabled = false;
        }, 2000);
      });

      // Smooth scroll for anchor links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      });

      // Simple lazy loading for images
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target;
              if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
              }
              observer.unobserve(img);
            }
          });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
          imageObserver.observe(img);
        });
      }
    </script>
  </body>
</html>
