<!doctype html>
<html xmlns:og="http://opengraphprotocol.org/schema/" xmlns:fb="http://www.facebook.com/2008/fbml" lang="it-IT"  >
  <head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
<base href="">
<meta charset="utf-8" />
<title>Roman Fontana nutrizionista</title>
<meta http-equiv="Accept-CH" content="Sec-CH-UA-Platform-Version, Sec-CH-UA-Model" /><link rel="icon" type="image/x-icon" href="https://images.squarespace-cdn.com/content/v1/644653fc8d0e044dd96cc334/25c9ca7e-31d8-4287-a7df-0672489e732c/favicon.ico?format=100w"/>
<link rel="canonical" href="https://www.romanfontana.com/"/>
<meta property="og:site_name" content="Roman Fontana Nutrizionista"/>
<meta property="og:title" content="Roman Fontana Nutrizionista"/>
<meta property="og:url" content="https://romanfontana.com/"/>
<meta property="og:type" content="website"/>
<meta property="og:description" content="Percorso personalizzato di nutrizione, allenamento e coaching con Roman, esperto in biologia e fitness, per il tuo equilibrio fisico e mentale duraturo."/>
<meta itemprop="name" content="Roman Fontana Nutrizionista"/>
<meta itemprop="url" content="https://www.romanfontana.com/"/>
<meta itemprop="description" content="Percorso personalizzato di nutrizione, allenamento e coaching con Roman, esperto in biologia e fitness, per il tuo equilibrio fisico e mentale duraturo."/>
<meta name="description" content="Percorso personalizzato di nutrizione, allenamento e coaching con Roman, 
esperto in biologia e fitness, per il tuo equilibrio fisico e mentale 
duraturo." />
<link rel="preconnect" href="https://images.squarespace-cdn.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,700;0,800;1,700;1,800&family=Oswald:ital,wght@0,500&family=Poppins:ital,wght@0,400;0,500;0,700;1,400;1,700&family=Roboto+Condensed:ital,wght@0,500;0,700;1,500;1,700"><script type="text/javascript" crossorigin="anonymous" defer="true" nomodule="nomodule" src="//assets.squarespace.com/@sqs/polyfiller/1.6/legacy.js"></script>
<script type="text/javascript" crossorigin="anonymous" defer="true" src="//assets.squarespace.com/@sqs/polyfiller/1.6/modern.js"></script>
<script type="text/javascript">SQUARESPACE_ROLLUPS = {};</script>
<script>(function(rollups, name) { if (!rollups[name]) { rollups[name] = {}; } rollups[name].js = ["//assets.squarespace.com/universal/scripts-compressed/extract-css-runtime-102c15efa995afb3-min.it-IT.js"]; })(SQUARESPACE_ROLLUPS, 'squarespace-extract_css_runtime');</script>
<script crossorigin="anonymous" src="//assets.squarespace.com/universal/scripts-compressed/extract-css-runtime-102c15efa995afb3-min.it-IT.js" defer ></script><script>(function(rollups, name) { if (!rollups[name]) { rollups[name] = {}; } rollups[name].js = ["//assets.squarespace.com/universal/scripts-compressed/extract-css-moment-js-vendor-49489c1ccd518c39-min.it-IT.js"]; })(SQUARESPACE_ROLLUPS, 'squarespace-extract_css_moment_js_vendor');</script>
<script crossorigin="anonymous" src="//assets.squarespace.com/universal/scripts-compressed/extract-css-moment-js-vendor-49489c1ccd518c39-min.it-IT.js" defer ></script><script>(function(rollups, name) { if (!rollups[name]) { rollups[name] = {}; } rollups[name].js = ["//assets.squarespace.com/universal/scripts-compressed/cldr-resource-pack-cc19606df5706673-min.it-IT.js"]; })(SQUARESPACE_ROLLUPS, 'squarespace-cldr_resource_pack');</script>
<script crossorigin="anonymous" src="//assets.squarespace.com/universal/scripts-compressed/cldr-resource-pack-cc19606df5706673-min.it-IT.js" defer ></script><script>(function(rollups, name) { if (!rollups[name]) { rollups[name] = {}; } rollups[name].js = ["//assets.squarespace.com/universal/scripts-compressed/common-vendors-stable-4b9dbe05ab739b5e-min.it-IT.js"]; })(SQUARESPACE_ROLLUPS, 'squarespace-common_vendors_stable');</script>
<script crossorigin="anonymous" src="//assets.squarespace.com/universal/scripts-compressed/common-vendors-stable-4b9dbe05ab739b5e-min.it-IT.js" defer ></script><script>(function(rollups, name) { if (!rollups[name]) { rollups[name] = {}; } rollups[name].js = ["//assets.squarespace.com/universal/scripts-compressed/common-vendors-ddb1b381f60a1e00-min.it-IT.js"]; })(SQUARESPACE_ROLLUPS, 'squarespace-common_vendors');</script>
<script crossorigin="anonymous" src="//assets.squarespace.com/universal/scripts-compressed/common-vendors-ddb1b381f60a1e00-min.it-IT.js" defer ></script><script>(function(rollups, name) { if (!rollups[name]) { rollups[name] = {}; } rollups[name].js = ["//assets.squarespace.com/universal/scripts-compressed/common-29eeda60cfecaac9-min.it-IT.js"]; })(SQUARESPACE_ROLLUPS, 'squarespace-common');</script>
<script crossorigin="anonymous" src="//assets.squarespace.com/universal/scripts-compressed/common-29eeda60cfecaac9-min.it-IT.js" defer ></script><script>(function(rollups, name) { if (!rollups[name]) { rollups[name] = {}; } rollups[name].js = ["//assets.squarespace.com/universal/scripts-compressed/commerce-47ff315870d4e44f-min.it-IT.js"]; })(SQUARESPACE_ROLLUPS, 'squarespace-commerce');</script>
<script crossorigin="anonymous" src="//assets.squarespace.com/universal/scripts-compressed/commerce-47ff315870d4e44f-min.it-IT.js" defer ></script><script>(function(rollups, name) { if (!rollups[name]) { rollups[name] = {}; } rollups[name].css = ["//assets.squarespace.com/universal/styles-compressed/commerce-8f5ae491a383883f-min.it-IT.css"]; })(SQUARESPACE_ROLLUPS, 'squarespace-commerce');</script>
<link rel="stylesheet" type="text/css" href="//assets.squarespace.com/universal/styles-compressed/commerce-8f5ae491a383883f-min.it-IT.css"><script>(function(rollups, name) { if (!rollups[name]) { rollups[name] = {}; } rollups[name].js = ["//assets.squarespace.com/universal/scripts-compressed/user-account-core-9d7367e027eeda78-min.it-IT.js"]; })(SQUARESPACE_ROLLUPS, 'squarespace-user_account_core');</script>
<script crossorigin="anonymous" src="//assets.squarespace.com/universal/scripts-compressed/user-account-core-9d7367e027eeda78-min.it-IT.js" defer ></script><script>(function(rollups, name) { if (!rollups[name]) { rollups[name] = {}; } rollups[name].css = ["//assets.squarespace.com/universal/styles-compressed/user-account-core-eb63eb0b4f42a565-min.it-IT.css"]; })(SQUARESPACE_ROLLUPS, 'squarespace-user_account_core');</script>
<link rel="stylesheet" type="text/css" href="//assets.squarespace.com/universal/styles-compressed/user-account-core-eb63eb0b4f42a565-min.it-IT.css"><script>(function(rollups, name) { if (!rollups[name]) { rollups[name] = {}; } rollups[name].js = ["//assets.squarespace.com/universal/scripts-compressed/performance-02d9356a671e0089-min.it-IT.js"]; })(SQUARESPACE_ROLLUPS, 'squarespace-performance');</script>
<script crossorigin="anonymous" src="//assets.squarespace.com/universal/scripts-compressed/performance-02d9356a671e0089-min.it-IT.js" defer ></script><script data-name="static-context">Static = window.Static || {}; Static.SQUARESPACE_CONTEXT = {"betaFeatureFlags":["marketing_automations","campaigns_new_image_layout_picker","i18n_beta_website_locales","supports_versioned_template_assets","campaigns_import_discounts","campaigns_thumbnail_layout","nested_categories","new_stacked_index","campaigns_discount_section_in_blasts","campaigns_merch_state","campaigns_discount_section_in_automations","section-sdk-product-pages","contacts_and_campaigns_redesign","scripts_defer","enable_form_submission_trigger","override_block_styles","member_areas_feature","marketing_landing_page"],"facebookAppId":"314192535267336","facebookApiVersion":"v6.0","rollups":{"squarespace-announcement-bar":{"js":"//assets.squarespace.com/universal/scripts-compressed/announcement-bar-6c3d1dede1f86db2-min.it-IT.js"},"squarespace-audio-player":{"css":"//assets.squarespace.com/universal/styles-compressed/audio-player-5d864aadea1060d1-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/audio-player-3bd8c71cf39049ae-min.it-IT.js"},"squarespace-blog-collection-list":{"css":"//assets.squarespace.com/universal/styles-compressed/blog-collection-list-b4046463b72f34e2-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/blog-collection-list-adc614d35d91194a-min.it-IT.js"},"squarespace-calendar-block-renderer":{"css":"//assets.squarespace.com/universal/styles-compressed/calendar-block-renderer-b72d08ba4421f5a0-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/calendar-block-renderer-4f7ae6a6ce0f2c6a-min.it-IT.js"},"squarespace-chartjs-helpers":{"css":"//assets.squarespace.com/universal/styles-compressed/chartjs-helpers-96b256171ee039c1-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/chartjs-helpers-ce77ae027f39f32b-min.it-IT.js"},"squarespace-comments":{"css":"//assets.squarespace.com/universal/styles-compressed/comments-864ea8b102260eda-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/comments-5d887d1536983262-min.it-IT.js"},"squarespace-custom-css-popup":{"css":"//assets.squarespace.com/universal/styles-compressed/custom-css-popup-de16edf94995f5ef-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/custom-css-popup-7cb0a598a34a4e0a-min.it-IT.js"},"squarespace-dialog":{"css":"//assets.squarespace.com/universal/styles-compressed/dialog-f9093f2d526b94df-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/dialog-1025f8d82bcdf48b-min.it-IT.js"},"squarespace-events-collection":{"css":"//assets.squarespace.com/universal/styles-compressed/events-collection-b72d08ba4421f5a0-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/events-collection-44b0ed372f21effb-min.it-IT.js"},"squarespace-form-rendering-utils":{"js":"//assets.squarespace.com/universal/scripts-compressed/form-rendering-utils-6ec5f698f90cd356-min.it-IT.js"},"squarespace-forms":{"css":"//assets.squarespace.com/universal/styles-compressed/forms-0afd3c6ac30bbab1-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/forms-9376b164d946c57c-min.it-IT.js"},"squarespace-gallery-collection-list":{"css":"//assets.squarespace.com/universal/styles-compressed/gallery-collection-list-b4046463b72f34e2-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/gallery-collection-list-ca68df1ca0ae7ebe-min.it-IT.js"},"squarespace-image-zoom":{"css":"//assets.squarespace.com/universal/styles-compressed/image-zoom-b4046463b72f34e2-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/image-zoom-5d9206846a71ca49-min.it-IT.js"},"squarespace-pinterest":{"css":"//assets.squarespace.com/universal/styles-compressed/pinterest-b4046463b72f34e2-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/pinterest-b21f622cfa980a40-min.it-IT.js"},"squarespace-popup-overlay":{"css":"//assets.squarespace.com/universal/styles-compressed/popup-overlay-b742b752f5880972-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/popup-overlay-e5623587f0e4cb24-min.it-IT.js"},"squarespace-product-quick-view":{"css":"//assets.squarespace.com/universal/styles-compressed/product-quick-view-0ba5bac716923b8e-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/product-quick-view-8f5310e31f38e50c-min.it-IT.js"},"squarespace-products-collection-item-v2":{"css":"//assets.squarespace.com/universal/styles-compressed/products-collection-item-v2-b4046463b72f34e2-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/products-collection-item-v2-de5994f48f4e50b3-min.it-IT.js"},"squarespace-products-collection-list-v2":{"css":"//assets.squarespace.com/universal/styles-compressed/products-collection-list-v2-b4046463b72f34e2-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/products-collection-list-v2-960f78544f54c1c8-min.it-IT.js"},"squarespace-search-page":{"css":"//assets.squarespace.com/universal/styles-compressed/search-page-90a67fc09b9b32c6-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/search-page-5ff3e4140a2c2b4c-min.it-IT.js"},"squarespace-search-preview":{"js":"//assets.squarespace.com/universal/scripts-compressed/search-preview-fac1cce900348bbc-min.it-IT.js"},"squarespace-simple-liking":{"css":"//assets.squarespace.com/universal/styles-compressed/simple-liking-701bf8bbc05ec6aa-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/simple-liking-4f195f9f5c89395f-min.it-IT.js"},"squarespace-social-buttons":{"css":"//assets.squarespace.com/universal/styles-compressed/social-buttons-95032e5fa98e47a5-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/social-buttons-94484d38141932d0-min.it-IT.js"},"squarespace-tourdates":{"css":"//assets.squarespace.com/universal/styles-compressed/tourdates-b4046463b72f34e2-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/tourdates-b5b3fd4914c5298b-min.it-IT.js"},"squarespace-website-overlays-manager":{"css":"//assets.squarespace.com/universal/styles-compressed/website-overlays-manager-07ea5a4e004e6710-min.it-IT.css","js":"//assets.squarespace.com/universal/scripts-compressed/website-overlays-manager-f83f80be2c82f2e2-min.it-IT.js"}},"pageType":2,"website":{"id":"644653fc8d0e044dd96cc334","identifier":"","websiteType":4,"contentModifiedOn":1748891564840,"cloneable":false,"hasBeenCloneable":false,"siteStatus":{},"language":"it-IT","timeZone":"Europe/Rome","machineTimeZoneOffset":7200000,"timeZoneOffset":7200000,"timeZoneAbbr":"CEST","siteTitle":"Roman Fontana Nutrizionista","fullSiteTitle":"Roman Fontana Nutrizionista","siteDescription":"","location":{"mapLat":44.7944423,"mapLng":10.3101576,"addressTitle":"Roman Fontana Nutrizionista","addressLine1":"","addressLine2":"","addressCountry":"Italy"},"logoImageId":"6838245b5a01005435a5865d","shareButtonOptions":{"8":true,"1":true,"2":true,"3":true,"4":true,"7":true,"6":true},"logoImageUrl":"//images.squarespace-cdn.com/content/v1/644653fc8d0e044dd96cc334/7da28899-2f68-4568-a650-************/fabio+bertozzi+logov2_white.png","authenticUrl":"https://www.romanfontana.com","internalUrl":"https://fabiobertozzi.squarespace.com","baseUrl":"https://www.romanfontana.com","primaryDomain":"www.romanfontana.com","sslSetting":3,"isHstsEnabled":true,"socialAccounts":[{"serviceId":60,"screenname":"Facebook","addedOn":*************,"profileUrl":"https://www.facebook.com/FabioBertozziWHMinstructor/","iconEnabled":true,"serviceName":"facebook-unauth"},{"serviceId":64,"screenname":"Instagram","addedOn":*************,"profileUrl":"https://www.instagram.com/fabio_whm_events","iconEnabled":true,"serviceName":"instagram-unauth"}],"typekitId":"","statsMigrated":false,"imageMetadataProcessingEnabled":false,"screenshotId":"7c1b9c77bfae5bc55d00fff19c1931cda7d66d615df2e0e451e6e6420df57ac6","captchaSettings":{"siteKey":"","enabledForDonations":false},"showOwnerLogin":false},"websiteSettings":{"id":"644653fc8d0e044dd96cc337","websiteId":"644653fc8d0e044dd96cc334","subjects":[],"country":"IT","state":"","simpleLikingEnabled":true,"mobileInfoBarSettings":{"isContactEmailEnabled":false,"isContactPhoneNumberEnabled":false,"isLocationEnabled":false,"isBusinessHoursEnabled":false},"announcementBarSettings":{"style":0,"text":"","clickthroughUrl":{"url":"/wim-hof-weekend","newWindow":false}},"popupOverlaySettings":{"style":2,"showOnScroll":true,"scrollPercentage":50,"showOnTimer":true,"timerDelay":5000,"showUntilSignup":true,"displayFrequency":1,"enableMobile":true,"enabledPages":["6446578c95e28d410beeec0f","646f5f3672dd8b4ae8cb8ace","6446544c3a9ca65967753d3a","65140569c5c770303f0388a0","655dcd54de202c207a4a7529","6514359b7f6c3565da808d7f","65154c0f6575685f315b14a2","6450cd8059467e51fb123257","64465435a6b95b0578474cf3"],"showOnAllPages":false,"version":2},"commentLikesAllowed":true,"commentAnonAllowed":true,"commentThreaded":true,"commentApprovalRequired":false,"commentAvatarsOn":true,"commentSortType":2,"commentFlagThreshold":0,"commentFlagsAllowed":true,"commentEnableByDefault":true,"commentDisableAfterDaysDefault":0,"disqusShortname":"","commentsEnabled":false,"contactPhoneNumber":"+393484940741","businessHours":{"monday":{"text":"","ranges":[{}]},"tuesday":{"text":"","ranges":[{}]},"wednesday":{"text":"","ranges":[{}]},"thursday":{"text":"","ranges":[{}]},"friday":{"text":"","ranges":[{}]},"saturday":{"text":"","ranges":[{}]},"sunday":{"text":"","ranges":[{}]}},"storeSettings":{"returnPolicy":"<p>Informazioni di contatto</p><p>+<a href=\"mailto:***********\<EMAIL>\">***********<br><EMAIL></a></p>","termsOfService":"<p><strong>Condizioni generali di vendita</strong></p><p><strong>Condizioni di cancellazione delle attivit\u00E0 acquistate da parte del partecipante:</strong></p><p><strong>Corso base</strong></p><p>se la cancellazione avviene:</p><p>Fino a 14 giorni prima della data di inizio del Corso: rimborso del 50% dell'importo del Corso o il 100% dell\u2019importo dell\u2019attivit\u00E0 acquistata pu\u00F2 essere utilizzato sotto forma di gift card per un\u2019attivit\u00E0 successiva (valido per 6 mesi)*.</p><p>Fino a 7 giorni prima dell'inizio di Corso: a scelta il partecipante pu\u00F2 decidere se l'intero importo \u00E8 dovuto o il 50% dell'importo dell\u2019attivit\u00E0 acquistata pu\u00F2 essere utilizzato sotto forma di gift card per un\u2019attivit\u00E0 successiva.</p><p>se i partecipanti non si presentano alla data di arrivo, l'intero importo sar\u00E0 dovuto.</p><p>riduzione del numero di partecipanti: salvo comunicazione di Roman Fontana, l'intero importo sar\u00E0 dovuto</p><p>validit\u00E0 6 mesi dalla data di inizio dell'attivit\u00E0 inizialmente prenotata, oltre la quale il coupon non si ritiene pi\u00F9 valido.</p><p><strong>Corso Weekend</strong></p><p>se la cancellazione avviene:</p><p>Fino a 30 giorni prima della data di inizio del Corso: rimborso del 70% dell'importo del Corso o il 100% dell\u2019importo dell\u2019attivit\u00E0 acquistata pu\u00F2 essere utilizzato sotto forma di gift card per un\u2019attivit\u00E0 successiva (valido per 6 mesi)*.</p><p>Fino a 14 giorni prima dell'inizio di Corso: a scelta il partecipante pu\u00F2 decidere se l'intero importo \u00E8 dovuto o il 50% dell'importo dell\u2019attivit\u00E0 acquistata pu\u00F2 essere utilizzato sotto forma di gift card per un\u2019attivit\u00E0 successiva.</p><p>se i partecipanti non si presentano alla data di arrivo, l'intero importo sar\u00E0 dovuto.</p><p>riduzione del numero di partecipanti: salvo comunicazione di Roman Fontana, l'intero importo sar\u00E0 dovuto</p><p>validit\u00E0 6 mesi dalla data di inizio dell'attivit\u00E0 inizialmente prenotata, oltre la quale il coupon non si ritiene pi\u00F9 valido.</p><p><strong>Corso Intensivo</strong></p><p>se la cancellazione avviene:</p><p>Fino a 30 giorni prima della data di inizio del Corso: rimborso del 50% dell'importo del Corso o il 100% dell\u2019importo dell\u2019attivit\u00E0 acquistata pu\u00F2 essere utilizzato sotto forma di gift card per un\u2019attivit\u00E0 successiva (valido per 6 mesi)*.</p><p>Fino a 14 giorni prima dell'inizio di Corso: a scelta il partecipante pu\u00F2 decidere se l'intero importo \u00E8 dovuto o il 30% dell'importo dell\u2019attivit\u00E0 acquistata pu\u00F2 essere utilizzato sotto forma di gift card per un\u2019attivit\u00E0 successiva.</p><p>se i partecipanti non si presentano alla data di arrivo, l'intero importo sar\u00E0 dovuto.</p><p>riduzione del numero di partecipanti: salvo comunicazione di Roman Fontana, l'intero importo sar\u00E0 dovuto</p><p>validit\u00E0 6 mesi dalla data di inizio dell'attivit\u00E0 inizialmente prenotata, oltre la quale il coupon non si ritiene pi\u00F9 valido.</p><p><strong>Condizioni di cancellazione da parte di Roman Fontana</strong></p><p>Roman Fontana pu\u00F2 cancellare i corsi e rimborsare l'intero importo pagato dai partecipanti.</p>","privacyPolicy":"<p class=\"\" style=\"white-space:pre-wrap;\">https://www.iubenda.com/privacy-policy/33093975</p>","expressCheckout":true,"continueShoppingLinkUrl":"corsi","useLightCart":false,"showNoteField":false,"shippingCountryDefaultValue":"IT","billToShippingDefaultValue":true,"showShippingPhoneNumber":true,"isShippingPhoneRequired":true,"showBillingPhoneNumber":true,"isBillingPhoneRequired":true,"currenciesSupported":["USD","ARS","AUD","BRL","CAD","CHF","COP","CZK","DKK","EUR","GBP","HKD","IDR","ILS","INR","JPY","MXN","MYR","NOK","NZD","PHP","PLN","RUB","SEK","SGD","THB","ZAR"],"defaultCurrency":"EUR","selectedCurrency":"EUR","measurementStandard":2,"showCustomCheckoutForm":false,"checkoutPageMarketingOptInEnabled":true,"enableMailingListOptInByDefault":true,"mailingListDescription":"Comunicazioni dopo il corso","sameAsRetailLocation":false,"merchandisingSettings":{"scarcityEnabledOnProductItems":true,"scarcityEnabledOnProductBlocks":true,"scarcityMessageType":"CUSTOM_SCARCITY_MESSAGE","customScarcityMessage":"Rimangono pochi posti!","scarcityThreshold":10,"multipleQuantityAllowedForServices":true,"restockNotificationsEnabled":false,"restockNotificationsSuccessText":"","restockNotificationsMailingListSignUpEnabled":true,"relatedProductsEnabled":true,"relatedProductsOrdering":"random","customSoldOutText":"SOLD OUT","soldOutVariantsDropdownDisabled":false,"productComposerOptedIn":false,"productComposerABTestOptedOut":false,"productReviewsEnabled":false,"displayNativeProductReviewsEnabled":true,"displayImportedProductReviewsEnabled":false,"productReviewsDisplayScope":"STORE_ONLY","productReviewsMerchantEmailEnabled":true,"hasOptedToCollectNativeReviews":false},"minimumOrderSubtotalEnabled":false,"minimumOrderSubtotal":{"currency":"EUR","value":"0.00"},"isLive":true,"multipleQuantityAllowedForServices":true},"useEscapeKeyToLogin":false,"ssBadgeType":1,"ssBadgePosition":4,"ssBadgeVisibility":1,"ssBadgeDevices":1,"pinterestOverlayOptions":{"mode":"disabled","size":"small","shape":"rect"},"userAccountsSettings":{"loginAllowed":true,"signupAllowed":true}},"cookieSettings":{"isCookieBannerEnabled":false,"isRestrictiveCookiePolicyEnabled":true,"cookieBannerText":"By using this website, you agree to our use of cookies. We use cookies to provide you with a great experience and to help our website run effectively.","cookieBannerThemeName":"dark-bold","cookieBannerVariant":"BAR","cookieBannerPosition":"BOTTOM","cookieBannerCtaVariant":"TEXT","cookieBannerCtaText":"Accetta","cookieBannerAcceptType":"OPT_IN","cookieBannerOptOutCtaText":"Rifiuta","cookieBannerHasOptOut":true,"cookieBannerHasManageCookies":true,"cookieBannerManageCookiesLabel":"Gestisci i cookie","cookieBannerSavedPreferencesText":"Preferenze sui cookie","cookieBannerSavedPreferencesLayout":"PILL"},"websiteCloneable":false,"collection":{"title":"Roman","id":"67fcb8c434e8564dcabc023a","fullUrl":"/roman","type":10,"permissionType":1},"subscribed":false,"appDomain":"squarespace.com","templateTweakable":true,"tweakJSON":{"form-use-theme-colors":"false","header-logo-height":"65px","header-mobile-logo-max-height":"55px","header-vert-padding":"1vw","header-width":"Full","maxPageWidth":"1400px","mobile-header-vert-padding":"6vw","pagePadding":"4vw","tweak-blog-alternating-side-by-side-image-aspect-ratio":"3:2 Standard","tweak-blog-alternating-side-by-side-image-spacing":"4%","tweak-blog-alternating-side-by-side-meta-spacing":"5px","tweak-blog-alternating-side-by-side-primary-meta":"Categories","tweak-blog-alternating-side-by-side-read-more-spacing":"0px","tweak-blog-alternating-side-by-side-secondary-meta":"Date","tweak-blog-basic-grid-columns":"3","tweak-blog-basic-grid-image-aspect-ratio":"3:2 Standard","tweak-blog-basic-grid-image-spacing":"50px","tweak-blog-basic-grid-meta-spacing":"10px","tweak-blog-basic-grid-primary-meta":"Date","tweak-blog-basic-grid-read-more-spacing":"24px","tweak-blog-basic-grid-secondary-meta":"Author","tweak-blog-item-custom-width":"60","tweak-blog-item-show-author-profile":"false","tweak-blog-item-width":"Medium","tweak-blog-masonry-columns":"2","tweak-blog-masonry-horizontal-spacing":"30px","tweak-blog-masonry-image-spacing":"20px","tweak-blog-masonry-meta-spacing":"20px","tweak-blog-masonry-primary-meta":"Categories","tweak-blog-masonry-read-more-spacing":"20px","tweak-blog-masonry-secondary-meta":"Date","tweak-blog-masonry-vertical-spacing":"30px","tweak-blog-side-by-side-image-aspect-ratio":"16:9 Widescreen","tweak-blog-side-by-side-image-spacing":"5%","tweak-blog-side-by-side-meta-spacing":"20px","tweak-blog-side-by-side-primary-meta":"Categories","tweak-blog-side-by-side-read-more-spacing":"40px","tweak-blog-side-by-side-secondary-meta":"Date","tweak-blog-single-column-image-spacing":"50px","tweak-blog-single-column-meta-spacing":"30px","tweak-blog-single-column-primary-meta":"Categories","tweak-blog-single-column-read-more-spacing":"30px","tweak-blog-single-column-secondary-meta":"Date","tweak-events-stacked-show-thumbnails":"true","tweak-events-stacked-thumbnail-size":"1:1 Square","tweak-fixed-header":"false","tweak-fixed-header-style":"Basic","tweak-global-animations-animation-curve":"ease","tweak-global-animations-animation-delay":"0.1s","tweak-global-animations-animation-duration":"0.1s","tweak-global-animations-animation-style":"fade","tweak-global-animations-animation-type":"none","tweak-global-animations-complexity-level":"detailed","tweak-global-animations-enabled":"false","tweak-portfolio-grid-basic-custom-height":"50","tweak-portfolio-grid-overlay-custom-height":"50","tweak-portfolio-hover-follow-acceleration":"10%","tweak-portfolio-hover-follow-animation-duration":"Medium","tweak-portfolio-hover-follow-animation-type":"Fade","tweak-portfolio-hover-follow-delimiter":"Forward Slash","tweak-portfolio-hover-follow-front":"false","tweak-portfolio-hover-follow-layout":"Inline","tweak-portfolio-hover-follow-size":"75","tweak-portfolio-hover-follow-text-spacing-x":"1.5","tweak-portfolio-hover-follow-text-spacing-y":"1.5","tweak-portfolio-hover-static-animation-duration":"Medium","tweak-portfolio-hover-static-animation-type":"Scale Up","tweak-portfolio-hover-static-delimiter":"Forward Slash","tweak-portfolio-hover-static-front":"false","tweak-portfolio-hover-static-layout":"Stacked","tweak-portfolio-hover-static-size":"75","tweak-portfolio-hover-static-text-spacing-x":"1.5","tweak-portfolio-hover-static-text-spacing-y":"1.5","tweak-portfolio-index-background-animation-duration":"Medium","tweak-portfolio-index-background-animation-type":"Fade","tweak-portfolio-index-background-custom-height":"50","tweak-portfolio-index-background-delimiter":"None","tweak-portfolio-index-background-height":"Large","tweak-portfolio-index-background-horizontal-alignment":"Center","tweak-portfolio-index-background-link-format":"Stacked","tweak-portfolio-index-background-persist":"false","tweak-portfolio-index-background-vertical-alignment":"Middle","tweak-portfolio-index-background-width":"Full","tweak-product-basic-item-click-action":"None","tweak-product-basic-item-gallery-aspect-ratio":"1:1 Square","tweak-product-basic-item-gallery-design":"Slideshow","tweak-product-basic-item-gallery-width":"50%","tweak-product-basic-item-hover-action":"None","tweak-product-basic-item-image-spacing":"2vw","tweak-product-basic-item-image-zoom-factor":"2","tweak-product-basic-item-product-variant-display":"Dropdown","tweak-product-basic-item-thumbnail-placement":"Side","tweak-product-basic-item-variant-picker-layout":"Dropdowns","tweak-products-add-to-cart-button":"false","tweak-products-columns":"3","tweak-products-gutter-column":"0.5vw","tweak-products-gutter-row":"1vw","tweak-products-header-text-alignment":"Left","tweak-products-image-aspect-ratio":"1:1 Square","tweak-products-image-text-spacing":"0.5vw","tweak-products-mobile-columns":"1","tweak-products-text-alignment":"Middle","tweak-products-width":"Inset","tweak-transparent-header":"false"},"templateId":"5c5a519771c10ba3470d8101","templateVersion":"7.1","pageFeatures":[1,2,4],"gmRenderKey":"QUl6YVN5Q0JUUk9xNkx1dkZfSUUxcjQ2LVQ0QWVUU1YtMGQ3bXk4","templateScriptsRootUrl":"https://static1.squarespace.com/static/vta/5c5a519771c10ba3470d8101/scripts/","impersonatedSession":false,"tzData":{"zones":[[60,"EU","CE%sT",null]],"rules":{"EU":[[1981,"max",null,"Mar","lastSun","1:00u","1:00","S"],[1996,"max",null,"Oct","lastSun","1:00u","0",null]]}},"showAnnouncementBar":false,"recaptchaEnterpriseContext":{"recaptchaEnterpriseSiteKey":"6LdDFQwjAAAAAPigEvvPgEVbb7QBm-TkVJdDTlAv"},"i18nContext":{"timeZoneData":{"id":"Europe/Rome","name":"Ora dell\u2019Europa centrale"}},"env":"PRODUCTION"};</script><script type="application/ld+json">{"url":"https://www.romanfontana.com","name":"Roman Fontana Wim Hof Method Instructor","description":"","image":"//images.squarespace-cdn.com/content/v1/644653fc8d0e044dd96cc334/7da28899-2f68-4568-a650-************/fabio+bertozzi+logov2_white.png","@context":"http://schema.org","@type":"WebSite"}</script><script type="application/ld+json">{"legalName":"Roman Fontana","address":"11 Via Ippolito Nievo\nParma, Emilia-Romagna, 43125\nItaly","email":"<EMAIL>","telephone":"+393484940741","sameAs":["https://www.facebook.com/FabioBertozziWHMinstructor/","https://www.instagram.com/fabio_whm_events"],"@context":"http://schema.org","@type":"Organization"}</script><script type="application/ld+json">{"address":"11 Via Ippolito Nievo\nParma, Emilia-Romagna, 43125\nItaly","image":"https://static1.squarespace.com/static/644653fc8d0e044dd96cc334/t/6838245b5a01005435a5865d/1748891564840/","name":"Roman Fontana","openingHours":", , , , , , ","@context":"http://schema.org","@type":"LocalBusiness"}</script><link rel="stylesheet" type="text/css" href="assets/css/site.css"/><script data-sqs-type="cookiepreferencesgetter">(function(){window.getSquarespaceCookies = function() {    const getCookiesAllowed = function(cookieName){ return ('; '+document.cookie).split('; ' + cookieName + '=').pop().split(';')[0] === 'true'};    return {      performance: getCookiesAllowed('ss_performanceCookiesAllowed') ? 'accepted' : 'declined',      marketing: getCookiesAllowed('ss_marketingCookiesAllowed') ? 'accepted' : 'declined'    }}})()</script><!-- Google Tag Manager -->
<script src="https://kit.fontawesome.com/5085a8a40f.js" crossorigin="anonymous"></script>
<!-- Share this icons -->
<script type="text/javascript" src="https://platform-api.sharethis.com/js/sharethis.js#property=68397abceb2dda0012da57bf&product=inline-share-buttons&source=platform" async="async"></script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=EB+Garamond:ital,wght@0,400..800;1,400..800&display=swap" rel="stylesheet"><meta name="ROBOTS" content="NOINDEX">
<script>Static.COOKIE_BANNER_CAPABLE = true;</script>
<script>window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}gtag('js', new Date());gtag('set', 'developer_id.dZjQwMz', true);gtag('consent', 'default', { 'analytics_storage': 'denied', 'wait_for_update': 500 });window.googleAnalyticsRequiresConsentUpdates = true;(function(){let squarespaceCookies = {};if (window.getSquarespaceCookies) {  squarespaceCookies = window.getSquarespaceCookies();}const consentValue = squarespaceCookies.performance === 'accepted' ? 'granted' :  'denied';gtag('consent', 'update', { 'analytics_storage': consentValue })})();gtag('config', 'G-2YLT371FG7');</script><script>!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';n.agent='plsquarespace';n.queue=[];t=b.createElement(e);t.async=!0;t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,document,'script','https://connect.facebook.net/en_US/fbevents.js');window.facebookPixelRequiresConsentUpdates = true;(function(){let squarespaceCookies = {};if (window.getSquarespaceCookies) {  squarespaceCookies = window.getSquarespaceCookies();}if (squarespaceCookies.marketing !== 'accepted') { fbq('consent', 'revoke'); }})();fbq('init', '952479275775441');fbq('track', "PageView");</script><link rel="stylesheet" type="text/css" href="assets/css/shape-styles.css"/><script defer src="assets/js/shape-visitor.js"></script><!-- End of Squarespace Headers -->
    <link rel="stylesheet" type="text/css" href="assets/css/static.css">
  </head>
  <body
    id="collection-67fcb8c434e8564dcabc023a"
    class="
      primary-button-style-solid primary-button-shape-pill secondary-button-style-solid secondary-button-shape-pill tertiary-button-style-solid tertiary-button-shape-pill  form-field-style-solid form-field-shape-square form-field-border-all form-field-checkbox-type-icon form-field-checkbox-fill-solid form-field-checkbox-color-inverted form-field-checkbox-shape-square form-field-checkbox-layout-stack form-field-radio-type-icon form-field-radio-fill-solid form-field-radio-color-normal form-field-radio-shape-pill form-field-radio-layout-stack form-field-survey-fill-solid form-field-survey-color-normal form-field-survey-shape-pill form-field-hover-focus-outline form-submit-button-style-label header-overlay-alignment-left header-width-full   tweak-fixed-header-style-basic tweak-blog-alternating-side-by-side-width-inset tweak-blog-alternating-side-by-side-image-aspect-ratio-32-standard tweak-blog-alternating-side-by-side-text-alignment-left tweak-blog-alternating-side-by-side-read-more-style-show tweak-blog-alternating-side-by-side-image-text-alignment-middle tweak-blog-alternating-side-by-side-delimiter-bullet tweak-blog-alternating-side-by-side-meta-position-below-excerpt tweak-blog-alternating-side-by-side-primary-meta-categories tweak-blog-alternating-side-by-side-secondary-meta-date tweak-blog-alternating-side-by-side-excerpt-show tweak-blog-basic-grid-width-inset tweak-blog-basic-grid-image-aspect-ratio-32-standard tweak-blog-basic-grid-text-alignment-left tweak-blog-basic-grid-delimiter-pipe tweak-blog-basic-grid-image-placement-above tweak-blog-basic-grid-read-more-style-show tweak-blog-basic-grid-primary-meta-date tweak-blog-basic-grid-secondary-meta-author tweak-blog-basic-grid-excerpt-show tweak-blog-item-width-medium tweak-blog-item-text-alignment-left tweak-blog-item-meta-position-above-title tweak-blog-item-show-categories tweak-blog-item-show-date   tweak-blog-item-delimiter-bullet tweak-blog-masonry-width-full tweak-blog-masonry-text-alignment-left tweak-blog-masonry-primary-meta-categories tweak-blog-masonry-secondary-meta-date tweak-blog-masonry-meta-position-top tweak-blog-masonry-read-more-style-show tweak-blog-masonry-delimiter-space tweak-blog-masonry-image-placement-above tweak-blog-masonry-excerpt-show tweak-blog-side-by-side-width-inset tweak-blog-side-by-side-image-placement-left tweak-blog-side-by-side-image-aspect-ratio-169-widescreen tweak-blog-side-by-side-primary-meta-categories tweak-blog-side-by-side-secondary-meta-date tweak-blog-side-by-side-meta-position-top tweak-blog-side-by-side-text-alignment-left tweak-blog-side-by-side-image-text-alignment-middle tweak-blog-side-by-side-read-more-style-show tweak-blog-side-by-side-delimiter-bullet tweak-blog-side-by-side-excerpt-show tweak-blog-single-column-width-full tweak-blog-single-column-text-alignment-center tweak-blog-single-column-image-placement-above tweak-blog-single-column-delimiter-bullet tweak-blog-single-column-read-more-style-show tweak-blog-single-column-primary-meta-categories tweak-blog-single-column-secondary-meta-date tweak-blog-single-column-meta-position-top tweak-blog-single-column-content-full-post tweak-events-stacked-width-inset tweak-events-stacked-height-medium tweak-events-stacked-show-past-events tweak-events-stacked-show-thumbnails tweak-events-stacked-thumbnail-size-11-square tweak-events-stacked-date-style-with-text tweak-events-stacked-show-time tweak-events-stacked-show-location tweak-events-stacked-ical-gcal-links    tweak-global-animations-complexity-level-detailed tweak-global-animations-animation-style-fade tweak-global-animations-animation-type-none tweak-global-animations-animation-curve-ease tweak-portfolio-grid-basic-width-full tweak-portfolio-grid-basic-height-large tweak-portfolio-grid-basic-image-aspect-ratio-11-square tweak-portfolio-grid-basic-text-alignment-left tweak-portfolio-grid-basic-hover-effect-fade tweak-portfolio-grid-overlay-width-full tweak-portfolio-grid-overlay-height-large tweak-portfolio-grid-overlay-image-aspect-ratio-11-square tweak-portfolio-grid-overlay-text-placement-center tweak-portfolio-grid-overlay-show-text-after-hover tweak-portfolio-index-background-link-format-stacked tweak-portfolio-index-background-width-full tweak-portfolio-index-background-height-large  tweak-portfolio-index-background-vertical-alignment-middle tweak-portfolio-index-background-horizontal-alignment-center tweak-portfolio-index-background-delimiter-none tweak-portfolio-index-background-animation-type-fade tweak-portfolio-index-background-animation-duration-medium tweak-portfolio-hover-follow-layout-inline  tweak-portfolio-hover-follow-delimiter-forward-slash tweak-portfolio-hover-follow-animation-type-fade tweak-portfolio-hover-follow-animation-duration-medium tweak-portfolio-hover-static-layout-stacked  tweak-portfolio-hover-static-delimiter-forward-slash tweak-portfolio-hover-static-animation-type-scale-up tweak-portfolio-hover-static-animation-duration-medium image-block-poster-text-alignment-center image-block-card-content-position-center image-block-card-text-alignment-left image-block-overlap-content-position-center image-block-overlap-text-alignment-left image-block-collage-content-position-center image-block-collage-text-alignment-left image-block-stack-text-alignment-left hide-opentable-icons opentable-style-dark tweak-product-quick-view-button-style-floating tweak-product-quick-view-button-position-bottom tweak-product-quick-view-lightbox-excerpt-display-truncate tweak-product-quick-view-lightbox-show-arrows tweak-product-quick-view-lightbox-show-close-button tweak-product-quick-view-lightbox-controls-weight-light native-currency-code-eur collection-type-page collection-layout-default collection-67fcb8c434e8564dcabc023a mobile-style-available sqs-seven-one
    "
    tabindex="-1"
  >
    <div
      id="siteWrapper"
      class="clearfix site-wrapper"
    >
        <div id="floatingCart" class="floating-cart hidden">
          <a href="/cart" class="icon icon--stroke icon--fill icon--cart sqs-custom-cart">
            <span class="Cart-inner">
  <svg class="icon icon--cart" viewBox="0 0 31 24">
  <g class="svg-icon cart-icon--odd">
    <circle fill="none" stroke-miterlimit="10" cx="22.5" cy="21.5" r="1"/>
    <circle fill="none" stroke-miterlimit="10" cx="9.5" cy="21.5" r="1"/>
    <path fill="none" stroke-miterlimit="10" d="M0,1.5h5c0.6,0,1.1,0.4,1.1,1l1.7,13
      c0.1,0.5,0.6,1,1.1,1h15c0.5,0,1.2-0.4,1.4-0.9l3.3-8.1c0.2-0.5-0.1-0.9-0.6-0.9H12"/>
  </g>
</svg>
              <div class="legacy-cart icon-cart-quantity">
                <span class="sqs-cart-quantity">0</span>
              </div>
            </span>
          </a>
        </div>
      <main id="page" class="container" role="main">
<article class="sections" id="sections" data-page-sections="67fcb8c434e8564dcabc023b">
<section
  data-test="page-section"
  data-section-theme="white"
  class='page-section 
      full-bleed-section
      layout-engine-section
    background-width--full-bleed
      section-height--medium
      content-width--wide
    horizontal-alignment--center
    vertical-alignment--middle
       has-background
    white'
  data-section-id="67fcbcf7d4151546f5e868c9"
  data-controller="SectionWrapperController"
  data-current-styles="{
        &quot;backgroundImage&quot;: {
          &quot;id&quot;: &quot;67fccb5534e8564dcabdd398&quot;,
          &quot;recordType&quot;: 2,
          &quot;addedOn&quot;: 1744620373364,
          &quot;updatedOn&quot;: 1745828803891,
          &quot;workflowState&quot;: 1,
          &quot;publishOn&quot;: 1744620373364,
          &quot;authorId&quot;: &quot;5963fc0e414fb5c08934f4ef&quot;,
          &quot;systemDataId&quot;: &quot;b86ac4c1-8744-4ff4-b986-214193b17536&quot;,
          &quot;systemDataVariants&quot;: &quot;3472x2315,100w,300w,500w,750w,1000w,1500w,2500w&quot;,
          &quot;systemDataSourceType&quot;: &quot;JPG&quot;,
          &quot;filename&quot;: &quot;roman_fontana_slider_home.jpg&quot;,
          &quot;mediaFocalPoint&quot;: {
            &quot;x&quot;: 0.7752171008684035,
            &quot;y&quot;: 0.16802100262532815,
            &quot;source&quot;: 3
          },
          &quot;colorData&quot;: {
            &quot;topLeftAverage&quot;: &quot;918778&quot;,
            &quot;topRightAverage&quot;: &quot;c1b39c&quot;,
            &quot;bottomLeftAverage&quot;: &quot;833b33&quot;,
            &quot;bottomRightAverage&quot;: &quot;54524d&quot;,
            &quot;centerAverage&quot;: &quot;7b6d66&quot;,
            &quot;suggestedBgColor&quot;: &quot;978d86&quot;
          },
          &quot;urlId&quot;: &quot;4rd1rhdwf46s382w40asy2i8utro5o&quot;,
          &quot;title&quot;: &quot;&quot;,
          &quot;body&quot;: null,
          &quot;likeCount&quot;: 0,
          &quot;commentCount&quot;: 0,
          &quot;publicCommentCount&quot;: 0,
          &quot;commentState&quot;: 2,
          &quot;unsaved&quot;: false,
          &quot;author&quot;: {
            &quot;id&quot;: &quot;5963fc0e414fb5c08934f4ef&quot;,
            &quot;displayName&quot;: &quot;Roman Fontana&quot;,
            &quot;firstName&quot;: &quot;Fabio&quot;,
            &quot;lastName&quot;: &quot;Bertozzi&quot;
          },
          &quot;assetUrl&quot;: &quot;assets/images/roman_fontana_slider_home.jpg&quot;,
          &quot;contentType&quot;: &quot;image/jpeg&quot;,
          &quot;items&quot;: [ ],
          &quot;pushedServices&quot;: { },
          &quot;pendingPushedServices&quot;: { },
          &quot;originalSize&quot;: &quot;3472x2315&quot;,
          &quot;recordTypeLabel&quot;: &quot;image&quot;
        },
        &quot;imageOverlayOpacity&quot;: 0.1,
        &quot;backgroundWidth&quot;: &quot;background-width--full-bleed&quot;,
        &quot;sectionHeight&quot;: &quot;section-height--medium&quot;,
        &quot;customSectionHeight&quot;: 1,
        &quot;horizontalAlignment&quot;: &quot;horizontal-alignment--center&quot;,
        &quot;verticalAlignment&quot;: &quot;vertical-alignment--middle&quot;,
        &quot;contentWidth&quot;: &quot;content-width--wide&quot;,
        &quot;customContentWidth&quot;: 50,
        &quot;backgroundColor&quot;: &quot;&quot;,
        &quot;sectionTheme&quot;: &quot;white&quot;,
        &quot;sectionAnimation&quot;: &quot;none&quot;,
        &quot;backgroundMode&quot;: &quot;image&quot;,
        &quot;imageEffect&quot;: &quot;none&quot;
      }"
  data-current-context="{
        &quot;video&quot;: {
          &quot;playbackSpeed&quot;: 1,
          &quot;filter&quot;: 1,
          &quot;filterStrength&quot;: 0,
          &quot;zoom&quot;: 0,
          &quot;videoSourceProvider&quot;: &quot;none&quot;
        },
        &quot;backgroundImageId&quot;: null,
        &quot;backgroundMediaEffect&quot;: {
          &quot;type&quot;: &quot;none&quot;
        },
        &quot;divider&quot;: {
          &quot;enabled&quot;: false
        },
        &quot;typeName&quot;: &quot;page&quot;
      }"
  data-animation="none"
  data-fluid-engine-section
>
  <div
    class="section-border"
  >
    <div class="section-background">
<img alt="" data-src="assets/images/roman_fontana_slider_home.jpg" data-image="assets/images/roman_fontana_slider_home.jpg" data-image-dimensions="3472x2315" data-image-focal-point="0.7752171008684035,0.16802100262532815" alt="roman_fontana_slider_home.jpg" data-load="false" elementtiming="nbf-background" src="assets/images/roman_fontana_slider_home.jpg" width="3472" height="2315" alt="" sizes="(max-width: 799px) 200vw, 100vw" style="display:block;object-position: 77.52171008684034% 16.802100262532814%" srcset="assets/images/roman_fontana_slider_home.jpg?format=100w 100w, assets/images/roman_fontana_slider_home.jpg?format=300w 300w, assets/images/roman_fontana_slider_home.jpg?format=500w 500w, assets/images/roman_fontana_slider_home.jpg?format=750w 750w, assets/images/roman_fontana_slider_home.jpg?format=1000w 1000w, assets/images/roman_fontana_slider_home.jpg?format=1500w 1500w, assets/images/roman_fontana_slider_home.jpg?format=2500w 2500w" fetchpriority="high" loading="eager" decoding="async" data-loader="sqs">
        <div class="section-background-overlay" style="opacity: 0.1;"></div>
    </div>
  </div>
  <div
    class='content-wrapper'
    style='
    '
  >
    <div
      class="content"
    >
      <div data-fluid-engine="true"><style>
.fe-67fcbcf711a9061ae8e0aa8f {
  --grid-gutter: calc(var(--sqs-mobile-site-gutter, 6vw) - 11.0px);
  --cell-max-width: calc( ( var(--sqs-site-max-width, 1500px) - (11.0px * (8 - 1)) ) / 8 );
  display: grid;
  position: relative;
  grid-area: 1/1/-1/-1;
  grid-template-rows: repeat(31,minmax(24px, auto));
  grid-template-columns:
    minmax(var(--grid-gutter), 1fr)
    repeat(8, minmax(0, var(--cell-max-width)))
    minmax(var(--grid-gutter), 1fr);
  row-gap: 11.0px;
  column-gap: 11.0px;
}
@media (min-width: 768px) {
  .background-width--inset .fe-67fcbcf711a9061ae8e0aa8f {
    --inset-padding: calc(var(--sqs-site-gutter) * 2);
  }
  .fe-67fcbcf711a9061ae8e0aa8f {
    --grid-gutter: calc(var(--sqs-site-gutter, 4vw) - 11.0px);
    --cell-max-width: calc( ( var(--sqs-site-max-width, 1500px) - (11.0px * (24 - 1)) ) / 24 );
    --inset-padding: 0vw;
    --row-height-scaling-factor: 0.0215;
    --container-width: min(var(--sqs-site-max-width, 1500px), calc(100vw - var(--sqs-site-gutter, 4vw) * 2 - var(--inset-padding) ));
    grid-template-rows: repeat(16,minmax(calc(var(--container-width) * var(--row-height-scaling-factor)), auto));
    grid-template-columns:
      minmax(var(--grid-gutter), 1fr)
      repeat(24, minmax(0, var(--cell-max-width)))
      minmax(var(--grid-gutter), 1fr);
  }
}
  .fe-block-yui_3_17_2_1_1744620168284_5199 {
    grid-area: 12/1/32/11;
    z-index: 1;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744620168284_5199 .sqs-block {
    justify-content: center;
  }
  .fe-block-yui_3_17_2_1_1744620168284_5199 .sqs-block-alignment-wrapper {
    align-items: center;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744620168284_5199 {
      grid-area: 1/2/17/17;
      z-index: 1;
    }
    .fe-block-yui_3_17_2_1_1744620168284_5199 .sqs-block {
      justify-content: center;
    }
    .fe-block-yui_3_17_2_1_1744620168284_5199 .sqs-block-alignment-wrapper {
      align-items: center;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_4118 {
    grid-area: 13/2/19/10;
    z-index: 2;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_4118 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_4118 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_4118 {
      grid-area: 3/3/8/16;
      z-index: 4;
    }
    .fe-block-yui_3_17_2_1_1744615607231_4118 .sqs-block {
      justify-content: center;
    }
    .fe-block-yui_3_17_2_1_1744615607231_4118 .sqs-block-alignment-wrapper {
      align-items: center;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_4591 {
    grid-area: 20/2/29/10;
    z-index: 3;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_4591 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_4591 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_4591 {
      grid-area: 8/3/13/15;
      z-index: 2;
    }
    .fe-block-yui_3_17_2_1_1744615607231_4591 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_4591 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_5495 {
    grid-area: 29/2/31/10;
    z-index: 4;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_5495 .sqs-block {
    justify-content: center;
  }
  .fe-block-yui_3_17_2_1_1744615607231_5495 .sqs-block-alignment-wrapper {
    align-items: center;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_5495 {
      grid-area: 14/3/16/9;
      z-index: 3;
    }
    .fe-block-yui_3_17_2_1_1744615607231_5495 .sqs-block {
      justify-content: center;
    }
    .fe-block-yui_3_17_2_1_1744615607231_5495 .sqs-block-alignment-wrapper {
      align-items: center;
    }
  }
</style><div class="fluid-engine fe-67fcbcf711a9061ae8e0aa8f"><div class="fe-block fe-block-yui_3_17_2_1_1744620168284_5199"><div class="sqs-block website-component-block sqs-block-website-component" data-block-css="[&quot;assets/css/shape-styles.css&quot;]" data-block-scripts="[&quot;assets/js/shape-visitor.js&quot;]" data-block-type="1337" data-definition-name="website.components.shape" id="block-yui_3_17_2_1_1744620168284_5199"><div class="sqs-block-content">
<div
  class="sqs-shape-block-container sqs-block-alignment-wrapper hidden-stretch-block"
  data-stretched-to-fill
  data-shape-name="rectangle"
      data-blend-mode='normal'
>
    <div class="sqs-shape sqs-shape-rectangle"
    style="
          border-top-left-radius: 50px;
          border-top-right-radius: 50px;
          border-bottom-left-radius: 50px;
          border-bottom-right-radius: 50px;
    "
    ></div>
  <style>
    #block-yui_3_17_2_1_1744620168284_5199 .sqs-block-alignment-wrapper {
      justify-content: center;
    }
      #block-yui_3_17_2_1_1744620168284_5199 .sqs-shape > * {
          stroke: none;
      }
        #block-yui_3_17_2_1_1744620168284_5199 {
          --shape-block-background-color: 
    hsla(36, 50%, 96%, 1)
;
        }
  </style>
</div>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_4118"><div class="sqs-block code-block sqs-block-code" data-block-type="23" id="block-yui_3_17_2_1_1744615607231_4118"><div class="sqs-block-content"><h1 class="responsive-title-sezione1">
  Ritrova il tuo equilibrio fisico e mentale:
</h1>
<style>
  .responsive-title-sezione1 {
    font-family: 'EB Garamond', serif;
    font-size: 72px;
    font-weight: 600;
    font-style: normal;
    margin: 0;
    color: #33322e;
    text-align: left !important;
  }
  @media screen and (max-width: 600px) {
    .responsive-title-sezione1 {
      font-size: 52px;
    }
  }
</style>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_4591"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-yui_3_17_2_1_1744615607231_4591"><div class="sqs-block-content">
  <script class="TextAttributes-props" type="application/json">[ {
        "type": "highlight",
        "id": "3f1ac7e3-1a9b-4563-9e3e-ee833a574038",
        "shape": "underlineCurve",
        "isFront": false,
        "isAnimationEnabled": false,
        "animation": "draw",
        "duration": 0.5,
        "direction": "right",
        "color": {
          "type": "CUSTOM_COLOR",
          "customColor": {
            "hslaValue": {
              "hue": 21.13,
              "saturation": 0.56,
              "lightness": 0.5,
              "alpha": 1.0
            },
            "userFormat": "hex"
          }
        },
        "thickness": {
          "unit": "em",
          "value": 0.1
        },
        "linecap": "square"
      } ]</script>
<div class="sqs-html-content">
  <h3 style="white-space:pre-wrap;"><span class="sqsrte-text-highlight" data-text-attribute-id="3f1ac7e3-1a9b-4563-9e3e-ee833a574038"><span class="sqsrte-text-color--custom" style="color: #33322E">Inizia il percorso studiato per te:</span></span></h3><p class="sqsrte-large" style="white-space:pre-wrap;">In qualità di Biologo specializzato in <strong>nutrizione, allenamento e coaching del cambiamento</strong>, ti guiderò nel tuo percorso di trasformazione per garantirti <strong>risultati concreti e definitivi.</strong></p>
</div>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_5495"><div class="sqs-block button-block sqs-block-button sqs-stretched" data-block-type="53" id="block-yui_3_17_2_1_1744615607231_5495"><div class="sqs-block-content">
<div
  class="sqs-block-button-container sqs-block-button-container--center"
  data-animation-role="button"
  data-alignment="center"
  data-button-size="large"
  data-button-type="secondary"
>
  <a
    href=""
    class="sqs-block-button-element--large sqs-button-element--secondary sqs-block-button-element"
  >
    Prenota un appuntamento
  </a>
</div>
</div></div></div></div></div>
    </div>
  </div>
</section>
<section
  data-test="page-section"
  data-section-theme=""
  class='page-section 
      full-bleed-section
      layout-engine-section
    background-width--full-bleed
      section-height--small
      content-width--wide
    horizontal-alignment--center
    vertical-alignment--middle
    '
  data-section-id="67fcbff014018d7923d20640"
  data-controller="SectionWrapperController"
  data-current-styles="{
        &quot;imageOverlayOpacity&quot;: 0.15,
        &quot;backgroundWidth&quot;: &quot;background-width--full-bleed&quot;,
        &quot;sectionHeight&quot;: &quot;section-height--small&quot;,
        &quot;customSectionHeight&quot;: 1,
        &quot;horizontalAlignment&quot;: &quot;horizontal-alignment--center&quot;,
        &quot;verticalAlignment&quot;: &quot;vertical-alignment--middle&quot;,
        &quot;contentWidth&quot;: &quot;content-width--wide&quot;,
        &quot;customContentWidth&quot;: 50,
        &quot;backgroundColor&quot;: &quot;&quot;,
        &quot;sectionTheme&quot;: &quot;&quot;,
        &quot;sectionAnimation&quot;: &quot;none&quot;,
        &quot;backgroundMode&quot;: &quot;image&quot;
      }"
  data-current-context="{
        &quot;video&quot;: {
          &quot;playbackSpeed&quot;: 1,
          &quot;filter&quot;: 1,
          &quot;filterStrength&quot;: 0,
          &quot;zoom&quot;: 0,
          &quot;videoSourceProvider&quot;: &quot;none&quot;
        },
        &quot;backgroundImageId&quot;: null,
        &quot;backgroundMediaEffect&quot;: {
          &quot;type&quot;: &quot;none&quot;
        },
        &quot;divider&quot;: {
          &quot;enabled&quot;: false
        },
        &quot;typeName&quot;: &quot;page&quot;
      }"
  data-animation="none"
  data-fluid-engine-section
>
  <div
    class="section-border"
  >
    <div class="section-background">
    </div>
  </div>
  <div
    class='content-wrapper'
    style='
    '
  >
    <div
      class="content"
    >
      <div data-fluid-engine="true"><style>
.fe-67fcbff00185f366ca183494 {
  --grid-gutter: calc(var(--sqs-mobile-site-gutter, 6vw) - 11.0px);
  --cell-max-width: calc( ( var(--sqs-site-max-width, 1500px) - (11.0px * (8 - 1)) ) / 8 );
  display: grid;
  position: relative;
  grid-area: 1/1/-1/-1;
  grid-template-rows: repeat(48,minmax(24px, auto));
  grid-template-columns:
    minmax(var(--grid-gutter), 1fr)
    repeat(8, minmax(0, var(--cell-max-width)))
    minmax(var(--grid-gutter), 1fr);
  row-gap: 12.0px;
  column-gap: 11.0px;
}
@media (min-width: 768px) {
  .background-width--inset .fe-67fcbff00185f366ca183494 {
    --inset-padding: calc(var(--sqs-site-gutter) * 2);
  }
  .fe-67fcbff00185f366ca183494 {
    --grid-gutter: calc(var(--sqs-site-gutter, 4vw) - 11.0px);
    --cell-max-width: calc( ( var(--sqs-site-max-width, 1500px) - (11.0px * (24 - 1)) ) / 24 );
    --inset-padding: 0vw;
    --row-height-scaling-factor: 0.0215;
    --container-width: min(var(--sqs-site-max-width, 1500px), calc(100vw - var(--sqs-site-gutter, 4vw) * 2 - var(--inset-padding) ));
    grid-template-rows: repeat(31,minmax(calc(var(--container-width) * var(--row-height-scaling-factor)), auto));
    grid-template-columns:
      minmax(var(--grid-gutter), 1fr)
      repeat(24, minmax(0, var(--cell-max-width)))
      minmax(var(--grid-gutter), 1fr);
  }
}
  .fe-block-yui_3_17_2_1_1744615607231_10094 {
    grid-area: 1/2/8/10;
    z-index: 2;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_10094 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_10094 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_10094 {
      grid-area: 1/3/7/25;
      z-index: 2;
    }
    .fe-block-yui_3_17_2_1_1744615607231_10094 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_10094 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_12753 {
    grid-area: 8/2/20/10;
    z-index: 3;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_12753 .sqs-block {
    justify-content: center;
  }
  .fe-block-yui_3_17_2_1_1744615607231_12753 .sqs-block-alignment-wrapper {
    align-items: center;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_12753 {
      grid-area: 9/2/32/12;
      z-index: 3;
    }
    .fe-block-yui_3_17_2_1_1744615607231_12753 .sqs-block {
      justify-content: center;
    }
    .fe-block-yui_3_17_2_1_1744615607231_12753 .sqs-block-alignment-wrapper {
      align-items: center;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_14336 {
    grid-area: 20/2/28/10;
    z-index: 6;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_14336 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_14336 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_14336 {
      grid-area: 10/14/17/26;
      z-index: 6;
    }
    .fe-block-yui_3_17_2_1_1744615607231_14336 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_14336 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_13325 {
    grid-area: 28/2/38/10;
    z-index: 4;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_13325 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_13325 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_13325 {
      grid-area: 17/14/24/26;
      z-index: 4;
    }
    .fe-block-yui_3_17_2_1_1744615607231_13325 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_13325 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_189929 {
    grid-area: 38/2/47/10;
    z-index: 9;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_189929 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_189929 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_189929 {
      grid-area: 23/14/30/24;
      z-index: 9;
    }
    .fe-block-yui_3_17_2_1_1744615607231_189929 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_189929 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_14886 {
    grid-area: 47/2/49/10;
    z-index: 7;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_14886 .sqs-block {
    justify-content: center;
  }
  .fe-block-yui_3_17_2_1_1744615607231_14886 .sqs-block-alignment-wrapper {
    align-items: center;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_14886 {
      grid-area: 30/14/32/21;
      z-index: 7;
    }
    .fe-block-yui_3_17_2_1_1744615607231_14886 .sqs-block {
      justify-content: center;
    }
    .fe-block-yui_3_17_2_1_1744615607231_14886 .sqs-block-alignment-wrapper {
      align-items: center;
    }
  }
</style><div class="fluid-engine fe-67fcbff00185f366ca183494"><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_10094"><div class="sqs-block code-block sqs-block-code" data-block-type="23" id="block-yui_3_17_2_1_1744615607231_10094"><div class="sqs-block-content"><h1 class="responsive-title-sezione2">
  "il momento ideale per cambiare era ieri ma adesso è il momento migliore per iniziare la tua trasformazione."
</h1>
<style>
  .responsive-title-sezione2 {
    font-family: 'EB Garamond', serif;
    font-size: 58px;
    font-weight: 600;
    font-style: italic;
    margin: 0;
    text-align:center;
    color:#5C3D2E;
  }
  @media screen and (max-width: 600px) {
    .responsive-title-sezione2 {
      font-size: 36px;
    }
  }
</style>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_12753"><div class="sqs-block image-block sqs-block-image sqs-stretched" data-aspect-ratio="121.86348862405201" data-block-type="5" id="block-yui_3_17_2_1_1744615607231_12753"><div class="sqs-block-content">
    <div
      class="
        image-block-outer-wrapper
        layout-caption-below
        design-layout-fluid
        image-position-center
        combination-animation-site-default
        individual-animation-site-default
      "
      data-test="image-block-fluid-outer-wrapper"
    >
      <div
        class="fluid-image-animation-wrapper sqs-image sqs-block-alignment-wrapper"
        data-animation-role="image"
      >
        <div
          class="fluid-image-container sqs-image-content"
          style="overflow: hidden;-webkit-mask-image: -webkit-radial-gradient(white, black);border-top-left-radius: 250px;border-top-right-radius: 250px;border-bottom-right-radius: 250px;position: relative;width: 100%;height: 100%;"
        >
              <div class="content-fill">
            <img data-stretch="true" data-src="assets/images/roman_fontana_nutrizionista_felice.jpg" data-image="assets/images/roman_fontana_nutrizionista_felice.jpg" data-image-dimensions="1280x1920" data-image-focal-point="0.2791773164584778,0.4320431937714476" alt="Uomo sorridente indossa una camicia bianca e jeans, in piedi davanti ad un albero di palma in giardino soleggiato" data-load="false" elementtiming="system-image-block" src="assets/images/roman_fontana_nutrizionista_felice.jpg" width="1280" height="1920" alt="" sizes="100vw" style="display:block;object-fit: cover; object-position: 27.91773164584778% 43.20431937714476%" srcset="assets/images/roman_fontana_nutrizionista_felice.jpg?format=100w 100w, assets/images/roman_fontana_nutrizionista_felice.jpg?format=300w 300w, assets/images/roman_fontana_nutrizionista_felice.jpg?format=500w 500w, assets/images/roman_fontana_nutrizionista_felice.jpg?format=750w 750w, assets/images/roman_fontana_nutrizionista_felice.jpg?format=1000w 1000w, assets/images/roman_fontana_nutrizionista_felice.jpg?format=1500w 1500w, assets/images/roman_fontana_nutrizionista_felice.jpg?format=2500w 2500w" loading="lazy" decoding="async" data-loader="sqs">
            <div class="fluidImageOverlay"></div>
              </div>
        </div>
      </div>
    </div>
    <style>
      .sqs-block-image .sqs-block-content {
        height: 100%;
        width: 100%;
      }
        .fe-block-yui_3_17_2_1_1744615607231_12753 .fluidImageOverlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          mix-blend-mode: normal;
            opacity: 0;
        }
    </style>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_14336"><div class="sqs-block code-block sqs-block-code" data-block-type="23" id="block-yui_3_17_2_1_1744615607231_14336"><div class="sqs-block-content"><h1 class="responsive-title-sezione2b">
  Soluzioni personalizzate per risultati duraturi
</h1>
<style>
  .responsive-title-sezione2b {
    font-family: 'EB Garamond', serif;
    font-size: 72px;
    font-weight: 600;
    font-style: normal;
    margin: 0;
    text-align:left;
    color:#6E7D58;
  }
  @media screen and (max-width: 600px) {
    .responsive-title-sezione2b {
      font-size: 52px;
    }
  }
</style>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_13325"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-yui_3_17_2_1_1744615607231_13325"><div class="sqs-block-content">
<div class="sqs-html-content">
  <p class="sqsrte-large" style="white-space:pre-wrap;"><strong>Mi chiamo Roman Fontana</strong>, sono un esperto in Biologia, Nutrizione e Scienze del Fitness, con una formazione accademica in questi ambiti e <strong>oltre dieci anni di esperienza.</strong> </p><p class="sqsrte-large" style="white-space:pre-wrap;">Integro alimentazione, movimento e crescita personale per offrire <strong>soluzioni personalizzate e sostenibili, pensate per migliorare il tuo benessere</strong> con un approccio su misura.</p>
</div>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_189929"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-yui_3_17_2_1_1744615607231_189929"><div class="sqs-block-content">
<div class="sqs-html-content">
  <p class="sqsrte-large" style="white-space:pre-wrap;">✓ Laurea in Scienze biologiche<br>✓ Laurea in Scienze del Fitness<br>✓ Diploma da Personal Trainer<br>✓ Master in Nutrizione Sportiva<br>✓ Master in Medicina Biointegrata<br>✓ Specializzazione in Coaching Trasformazionale<br>✓ Membro dell’Ordine Nazionale dei Biologi </p>
</div>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_14886"><div class="sqs-block button-block sqs-block-button sqs-stretched" data-block-type="53" id="block-yui_3_17_2_1_1744615607231_14886"><div class="sqs-block-content">
<div
  class="sqs-block-button-container sqs-block-button-container--center"
  data-animation-role="button"
  data-alignment="center"
  data-button-size="large"
  data-button-type="secondary"
>
  <a
    href=""
    class="sqs-block-button-element--large sqs-button-element--secondary sqs-block-button-element"
  >
    Prenota un appuntamento
  </a>
</div>
</div></div></div></div></div>
    </div>
  </div>
</section>
<section
  data-test="page-section"
  data-section-theme="dark"
  class='page-section 
      full-bleed-section
      layout-engine-section
    background-width--full-bleed
      section-height--small
      content-width--wide
    horizontal-alignment--center
    vertical-alignment--middle
    dark'
  data-section-id="67fcc4e4374c5c348132d9c6"
  data-controller="SectionWrapperController"
  data-current-styles="{
        &quot;imageOverlayOpacity&quot;: 0.15,
        &quot;backgroundWidth&quot;: &quot;background-width--full-bleed&quot;,
        &quot;sectionHeight&quot;: &quot;section-height--small&quot;,
        &quot;customSectionHeight&quot;: 1,
        &quot;horizontalAlignment&quot;: &quot;horizontal-alignment--center&quot;,
        &quot;verticalAlignment&quot;: &quot;vertical-alignment--middle&quot;,
        &quot;contentWidth&quot;: &quot;content-width--wide&quot;,
        &quot;customContentWidth&quot;: 50,
        &quot;backgroundColor&quot;: &quot;&quot;,
        &quot;sectionTheme&quot;: &quot;dark&quot;,
        &quot;sectionAnimation&quot;: &quot;none&quot;,
        &quot;backgroundMode&quot;: &quot;image&quot;
      }"
  data-current-context="{
        &quot;video&quot;: {
          &quot;playbackSpeed&quot;: 1,
          &quot;filter&quot;: 1,
          &quot;filterStrength&quot;: 0,
          &quot;zoom&quot;: 0,
          &quot;videoSourceProvider&quot;: &quot;none&quot;
        },
        &quot;backgroundImageId&quot;: null,
        &quot;backgroundMediaEffect&quot;: {
          &quot;type&quot;: &quot;none&quot;
        },
        &quot;divider&quot;: {
          &quot;enabled&quot;: false
        },
        &quot;typeName&quot;: &quot;page&quot;
      }"
  data-animation="none"
  data-fluid-engine-section
>
  <div
    class="section-border"
  >
    <div class="section-background">
    </div>
  </div>
  <div
    class='content-wrapper'
    style='
    '
  >
    <div
      class="content"
    >
      <div data-fluid-engine="true"><style>
.fe-67fcc4e45b3bbb748df6dbfa {
  --grid-gutter: calc(var(--sqs-mobile-site-gutter, 6vw) - 11.0px);
  --cell-max-width: calc( ( var(--sqs-site-max-width, 1500px) - (11.0px * (8 - 1)) ) / 8 );
  display: grid;
  position: relative;
  grid-area: 1/1/-1/-1;
  grid-template-rows: repeat(70,minmax(24px, auto));
  grid-template-columns:
    minmax(var(--grid-gutter), 1fr)
    repeat(8, minmax(0, var(--cell-max-width)))
    minmax(var(--grid-gutter), 1fr);
  row-gap: 11.0px;
  column-gap: 11.0px;
}
@media (min-width: 768px) {
  .background-width--inset .fe-67fcc4e45b3bbb748df6dbfa {
    --inset-padding: calc(var(--sqs-site-gutter) * 2);
  }
  .fe-67fcc4e45b3bbb748df6dbfa {
    --grid-gutter: calc(var(--sqs-site-gutter, 4vw) - 11.0px);
    --cell-max-width: calc( ( var(--sqs-site-max-width, 1500px) - (11.0px * (24 - 1)) ) / 24 );
    --inset-padding: 0vw;
    --row-height-scaling-factor: 0.0215;
    --container-width: min(var(--sqs-site-max-width, 1500px), calc(100vw - var(--sqs-site-gutter, 4vw) * 2 - var(--inset-padding) ));
    grid-template-rows: repeat(37,minmax(calc(var(--container-width) * var(--row-height-scaling-factor)), auto));
    grid-template-columns:
      minmax(var(--grid-gutter), 1fr)
      repeat(24, minmax(0, var(--cell-max-width)))
      minmax(var(--grid-gutter), 1fr);
  }
}
  .fe-block-yui_3_17_2_1_1744615607231_16009 {
    grid-area: 1/2/5/10;
    z-index: 1;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_16009 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_16009 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_16009 {
      grid-area: 1/6/4/22;
      z-index: 1;
    }
    .fe-block-yui_3_17_2_1_1744615607231_16009 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_16009 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_16402 {
    grid-area: 10/2/12/10;
    z-index: 2;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_16402 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_16402 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_16402 {
      grid-area: 10/2/12/8;
      z-index: 2;
    }
    .fe-block-yui_3_17_2_1_1744615607231_16402 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_16402 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_17128 {
    grid-area: 6/5/11/7;
    z-index: 3;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_17128 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_17128 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_17128 {
      grid-area: 6/4/10/6;
      z-index: 3;
    }
    .fe-block-yui_3_17_2_1_1744615607231_17128 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_17128 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_184017 {
    grid-area: 12/2/19/10;
    z-index: 8;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_184017 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_184017 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_184017 {
      grid-area: 8/8/11/26;
      z-index: 8;
    }
    .fe-block-yui_3_17_2_1_1744615607231_184017 .sqs-block {
      justify-content: flex-end;
    }
    .fe-block-yui_3_17_2_1_1744615607231_184017 .sqs-block-alignment-wrapper {
      align-items: flex-end;
    }
  }
  .fe-block-2d690fb8ed1145f3ebf6 {
    grid-area: 22/2/24/10;
    z-index: 3;
    @media (max-width: 767px) {
    }
  }
  .fe-block-2d690fb8ed1145f3ebf6 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-2d690fb8ed1145f3ebf6 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-2d690fb8ed1145f3ebf6 {
      grid-area: 16/2/18/8;
      z-index: 3;
    }
    .fe-block-2d690fb8ed1145f3ebf6 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-2d690fb8ed1145f3ebf6 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-b11a1073b3de64e4662e {
    grid-area: 19/5/24/7;
    z-index: 4;
    @media (max-width: 767px) {
    }
  }
  .fe-block-b11a1073b3de64e4662e .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-b11a1073b3de64e4662e .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-b11a1073b3de64e4662e {
      grid-area: 12/4/17/6;
      z-index: 4;
    }
    .fe-block-b11a1073b3de64e4662e .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-b11a1073b3de64e4662e .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-799230495499d67dccac {
    grid-area: 24/2/36/10;
    z-index: 9;
    @media (max-width: 767px) {
    }
  }
  .fe-block-799230495499d67dccac .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-799230495499d67dccac .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-799230495499d67dccac {
      grid-area: 14/8/17/26;
      z-index: 9;
    }
    .fe-block-799230495499d67dccac .sqs-block {
      justify-content: flex-end;
    }
    .fe-block-799230495499d67dccac .sqs-block-alignment-wrapper {
      align-items: flex-end;
    }
  }
  .fe-block-yui_3_17_2_1_1745844026291_11922 {
    grid-area: 33/5/38/7;
    z-index: 14;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1745844026291_11922 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1745844026291_11922 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1745844026291_11922 {
      grid-area: 18/4/22/6;
      z-index: 14;
    }
    .fe-block-yui_3_17_2_1_1745844026291_11922 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1745844026291_11922 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-60caf8c10dc92eb73034 {
    grid-area: 36/2/38/10;
    z-index: 4;
    @media (max-width: 767px) {
    }
  }
  .fe-block-60caf8c10dc92eb73034 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-60caf8c10dc92eb73034 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-60caf8c10dc92eb73034 {
      grid-area: 22/2/24/8;
      z-index: 4;
    }
    .fe-block-60caf8c10dc92eb73034 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-60caf8c10dc92eb73034 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-609fa61a8f572f9c9027 {
    grid-area: 38/2/48/10;
    z-index: 10;
    @media (max-width: 767px) {
    }
  }
  .fe-block-609fa61a8f572f9c9027 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-609fa61a8f572f9c9027 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-609fa61a8f572f9c9027 {
      grid-area: 19/8/23/26;
      z-index: 10;
    }
    .fe-block-609fa61a8f572f9c9027 .sqs-block {
      justify-content: flex-end;
    }
    .fe-block-609fa61a8f572f9c9027 .sqs-block-alignment-wrapper {
      align-items: flex-end;
    }
  }
  .fe-block-a4bcdc2e879239d514d5 {
    grid-area: 48/5/51/7;
    z-index: 6;
    @media (max-width: 767px) {
    }
  }
  .fe-block-a4bcdc2e879239d514d5 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-a4bcdc2e879239d514d5 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-a4bcdc2e879239d514d5 {
      grid-area: 24/4/28/6;
      z-index: 6;
    }
    .fe-block-a4bcdc2e879239d514d5 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-a4bcdc2e879239d514d5 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-b403193d02e89905ffc8 {
    grid-area: 51/2/53/10;
    z-index: 5;
    @media (max-width: 767px) {
    }
  }
  .fe-block-b403193d02e89905ffc8 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-b403193d02e89905ffc8 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-b403193d02e89905ffc8 {
      grid-area: 28/2/30/8;
      z-index: 5;
    }
    .fe-block-b403193d02e89905ffc8 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-b403193d02e89905ffc8 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-e04aa4dd4c4607ed955a {
    grid-area: 53/2/60/10;
    z-index: 11;
    @media (max-width: 767px) {
    }
  }
  .fe-block-e04aa4dd4c4607ed955a .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-e04aa4dd4c4607ed955a .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-e04aa4dd4c4607ed955a {
      grid-area: 26/8/29/26;
      z-index: 11;
    }
    .fe-block-e04aa4dd4c4607ed955a .sqs-block {
      justify-content: flex-end;
    }
    .fe-block-e04aa4dd4c4607ed955a .sqs-block-alignment-wrapper {
      align-items: flex-end;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_185516 {
    grid-area: 61/2/68/10;
    z-index: 13;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_185516 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_185516 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_185516 {
      grid-area: 32/2/35/26;
      z-index: 13;
    }
    .fe-block-yui_3_17_2_1_1744615607231_185516 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_185516 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_185127 {
    grid-area: 69/2/71/10;
    z-index: 12;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_185127 .sqs-block {
    justify-content: center;
  }
  .fe-block-yui_3_17_2_1_1744615607231_185127 .sqs-block-alignment-wrapper {
    align-items: center;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_185127 {
      grid-area: 36/11/38/17;
      z-index: 12;
    }
    .fe-block-yui_3_17_2_1_1744615607231_185127 .sqs-block {
      justify-content: center;
    }
    .fe-block-yui_3_17_2_1_1744615607231_185127 .sqs-block-alignment-wrapper {
      align-items: center;
    }
  }
</style><div class="fluid-engine fe-67fcc4e45b3bbb748df6dbfa"><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_16009"><div class="sqs-block code-block sqs-block-code" data-block-type="23" id="block-yui_3_17_2_1_1744615607231_16009"><div class="sqs-block-content"><h1 class="responsive-title-sezione3">
 Come funziona il percorso?
</h1>
<style>
  .responsive-title-sezione3 {
    font-family: 'EB Garamond', serif;
    font-size: 72px;
    font-weight: 600;
    font-style: normal;
    margin: 0;
    text-align:center;
    color: #a5422c;
  }
  @media screen and (max-width: 600px) {
    .responsive-title-sezione3 {
      font-size: 52px;
    }
  }
</style>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_16402"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-yui_3_17_2_1_1744615607231_16402"><div class="sqs-block-content">
<div class="sqs-html-content">
  <h4 style="text-align:center;white-space:pre-wrap;"><span class="sqsrte-text-color--custom" style="color: #A5422C">Consulenza Iniziale</span></h4>
</div>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_17128"><div class="sqs-block code-block sqs-block-code" data-block-type="23" id="block-yui_3_17_2_1_1744615607231_17128"><div class="sqs-block-content"><?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 27.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 800 800" style="enable-background:new 0 0 800 800;" xml:space="preserve">
<style type="text/css">
	.st0{fill:none;stroke:#a5422c;stroke-width:66.6667;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:133.3333;}
</style>
<path class="st0" d="M400,200l-33.1,100 M550,200c46.6,0,69.9,0,88.3,7.6c24.5,10.1,44,29.6,54.1,54.1c7.6,18.4,7.6,41.7,7.6,88.3
	v190c0,56,0,84-10.9,105.4c-9.6,18.8-24.9,34.1-43.7,43.7C624,700,596,700,540,700H260c-56,0-84,0-105.4-10.9
	c-18.8-9.6-34.1-24.9-43.7-43.7C100,624,100,596,100,540V350c0-46.6,0-69.9,7.6-88.3c10.1-24.5,29.6-44,54.1-54.1
	c18.4-7.6,41.7-7.6,88.3-7.6 M333.3,566.7h133.3 M351.4,100h97.2c36,0,54,0,67.4,6.2c17.8,8.2,31,23.9,36.2,42.8
	c3.9,14.2,0.9,31.9-5,67.5c-4.4,26.4-6.6,39.5-12.4,49.8c-7.8,13.7-20.2,24.2-35,29.7c-11.1,4.1-24.5,4.1-51.2,4.1h-97.2
	c-26.7,0-40.1,0-51.2-4.1c-14.8-5.4-27.2-15.9-35-29.7c-5.8-10.3-8-23.5-12.4-49.8c-5.9-35.5-8.9-53.3-5-67.5
	c5.2-18.9,18.5-34.5,36.2-42.8C297.3,100,315.4,100,351.4,100z"/>
</svg>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_184017"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-yui_3_17_2_1_1744615607231_184017"><div class="sqs-block-content">
<div class="sqs-html-content">
  <p class="sqsrte-large" style="white-space:pre-wrap;"><span class="sqsrte-text-color--black">Un colloquio approfondito per valutare insieme i tuoi obiettivi, il tuo punto di partenza e le tue abitudini. Analizzeremo il tuo stile di vita, le tue preferenze alimentari e il tuo livello di attività fisica per definire la strategia migliore per te.</span></p>
</div>
</div></div></div><div class="fe-block fe-block-2d690fb8ed1145f3ebf6"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-2d690fb8ed1145f3ebf6"><div class="sqs-block-content">
<div class="sqs-html-content">
  <h4 style="text-align:center;white-space:pre-wrap;"><span class="sqsrte-text-color--custom" style="color: #A5422C">Programma personale</span></h4>
</div>
</div></div></div><div class="fe-block fe-block-b11a1073b3de64e4662e"><div class="sqs-block code-block sqs-block-code" data-block-type="23" id="block-b11a1073b3de64e4662e"><div class="sqs-block-content"><?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 53.42 55.42">
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <polyline points="2 34.32 7.05 34.32 8.9 34.32" style="fill: none; stroke: #a5422c; stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 4px;"/>
      <path d="M13.07,34.32H7.05c-.24-1.24-.37-2.52-.37-3.83,0-11.07,8.97-20.03,20.03-20.03s20.03,8.96,20.03,20.03c0,1.31-.13,2.59-.37,3.83" style="fill: none; stroke: #a5422c; stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 4px;"/>
      <polyline points="46.37 34.32 46.37 34.32 51.42 34.32" style="fill: none; stroke: #a5422c; stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 4px;"/>
      <circle cx="26.39" cy="6.05" r="4.05" style="fill: none; stroke: #a5422c; stroke-miterlimit: 10; stroke-width: 4px;"/>
      <circle cx="29.98" cy="40.27" r="13.15" style="fill: none; stroke: #a5422c; stroke-miterlimit: 10; stroke-width: 4px;"/>
      <polyline points="26 40.13 29.21 43.74 36.51 36.79" style="fill: none; stroke: #a5422c; stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 3px;"/>
    </g>
  </g>
</svg></div></div></div><div class="fe-block fe-block-799230495499d67dccac"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-799230495499d67dccac"><div class="sqs-block-content">
<div class="sqs-html-content">
  <p class="sqsrte-large" style="white-space:pre-wrap;"><span class="sqsrte-text-color--black">Progetteremo un piano alimentare e di allenamento su misura per te, che si adatti alle tue esigenze e ai tuoi progressi. Privilegeremo alimenti gustosi e facili da preparare, e esercizi che puoi svolgere dove e quando vuoi, con un'attenzione particolare alla facilità di applicazione e al piacere di esecuzione.</span></p>
</div>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1745844026291_11922"><div class="sqs-block code-block sqs-block-code" data-block-type="23" id="block-yui_3_17_2_1_1745844026291_11922"><div class="sqs-block-content"><?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 800">
  <path d="M483.3,150c0,46-37.3,83.3-83.3,83.3s-83.3-37.3-83.3-83.3,37.3-83.3,83.3-83.3,83.3,37.3,83.3,83.3Z" style="fill: none; stroke: #a5422c; stroke-miterlimit: 133.33; stroke-width: 50px;"/>
  <path d="M100,566.7l86.3-43.6c8.4-4.2,13.7-12.9,13.7-22.4,0-98,71.3-181.8,166.9-197.9,21.8-3.7,44.5-3.7,66.2,0,95.6,16.1,166.9,99.9,166.9,197.9,0,9.5,5.3,18.2,13.7,22.4l86.3,43.6" style="fill: none; stroke: #a5422c; stroke-linecap: round; stroke-linejoin: round; stroke-width: 50px;"/>
  <path d="M316.7,533.3l-34.7,46.3c-1.1,1.4-1.6,2.1-2.1,2.8-8.8,11.1-20.8,19.1-34.5,22.9-.8,.2-1.7,.4-3.4,.9l-49.5,12.4c-34.8,8.7-59.1,39.9-59.1,75.7,0,21.6,17.5,39,39,39h51.9c20,0,30,0,39.7-1.1,22.7-2.7,44.6-10,64.3-21.4,8.4-4.9,16.4-10.9,32.4-22.9l6.1-4.5m-.1-.1l66.7-50m-66.7,50l84.7,31.7c20.3,7.6,30.5,11.4,41,13.9,5.4,1.3,10.9,2.3,16.5,3,10.7,1.4,21.5,1.4,43.2,1.4h75.6c21.6,0,39-17.5,39-39,0-35.8-24.4-67.1-59.1-75.7l-49.6-12.4c-1.7-.4-2.6-.6-3.4-.9-13.6-3.8-25.7-11.8-34.5-22.9-.5-.7-1.1-1.4-2.1-2.8l-34.7-46.3" style="fill: none; stroke: #a5422c; stroke-linecap: round; stroke-linejoin: round; stroke-width: 50px;"/>
</svg></div></div></div><div class="fe-block fe-block-60caf8c10dc92eb73034"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-60caf8c10dc92eb73034"><div class="sqs-block-content">
<div class="sqs-html-content">
  <h4 style="text-align:center;white-space:pre-wrap;"><span class="sqsrte-text-color--custom" style="color: #A5422C">Lavoro introspettivo</span></h4>
</div>
</div></div></div><div class="fe-block fe-block-609fa61a8f572f9c9027"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-609fa61a8f572f9c9027"><div class="sqs-block-content">
<div class="sqs-html-content">
  <p class="sqsrte-large" style="white-space:pre-wrap;"><span class="sqsrte-text-color--black">Ti guiderò in un viaggio alla scoperta di te stesso, per farti comprendere l'origine delle tue abitudini e trasformarle in comportamenti potenzianti che ti permetteranno di vivere una vita più autentica e appagante. Attraverso il coaching, potrai superare le tue convinzioni limitanti e sbloccare il tuo vero potenziale.</span></p>
</div>
</div></div></div><div class="fe-block fe-block-a4bcdc2e879239d514d5"><div class="sqs-block code-block sqs-block-code" data-block-type="23" id="block-a4bcdc2e879239d514d5"><div class="sqs-block-content"><?xml version="1.0" encoding="UTF-8"?>
<svg id="goal" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 666.67 666.67">
  <defs>
    <style>
      .cls-1 {
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 66.67px;
      }
      .cls-1, .cls-2 {
        fill: none;
        stroke: #a5422c;
      }
      .cls-2 {
        stroke-miterlimit: 10;
      }
    </style>
  </defs>
  <polygon id="secondary" class="cls-2" points="433.33 133.33 433.33 233.33 533.33 233.33 633.33 133.33 533.33 133.33 533.33 33.33 433.33 133.33"/>
  <path id="primary" class="cls-1" d="M433.33,233.33l-83.3,83.3m83.3-183.3v100h100l100-100h-100V33.33l-100,100Z"/>
  <path id="primary-2" class="cls-1" d="M344.33,33.33h-11C167.63,33.33,33.33,167.63,33.33,333.33s134.3,300,300,300,300-134.3,300-300v-11"/>
  <path id="primary-3" class="cls-1" d="M496.63,366.63c-18.4,90.2-106.5,148.3-196.7,129.9s-148.3-106.4-129.8-196.6c13.4-65.4,64.5-116.5,129.9-129.9"/>
</svg></div></div></div><div class="fe-block fe-block-b403193d02e89905ffc8"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-b403193d02e89905ffc8"><div class="sqs-block-content">
<div class="sqs-html-content">
  <h4 style="text-align:center;white-space:pre-wrap;"><span class="sqsrte-text-color--custom" style="color: #A5422C">Monitoraggio costante</span></h4>
</div>
</div></div></div><div class="fe-block fe-block-e04aa4dd4c4607ed955a"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-e04aa4dd4c4607ed955a"><div class="sqs-block-content">
<div class="sqs-html-content">
  <p class="sqsrte-large" style="white-space:pre-wrap;"><span class="sqsrte-text-color--black">Seguiremo con costanza i tuoi progressi attraverso incontri periodici (in studio o online) e un supporto continuo via WhatsApp. In questo modo, potremo adattare il percorso alle tue esigenze e garantirti risultati duraturi e sostenibili</span></p>
</div>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_185516"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-yui_3_17_2_1_1744615607231_185516"><div class="sqs-block-content">
<div class="sqs-html-content">
  <h3 style="text-align:center;white-space:pre-wrap;"><span class="sqsrte-text-color--custom" style="color: #A5422C"><strong>Contattami su WhatsApp per programmare un appuntamento in presenza oppure online.</strong></span></h3>
</div>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_185127"><div class="sqs-block button-block sqs-block-button sqs-stretched" data-block-type="53" id="block-yui_3_17_2_1_1744615607231_185127"><div class="sqs-block-content">
<div
  class="sqs-block-button-container sqs-block-button-container--center"
  data-animation-role="button"
  data-alignment="center"
  data-button-size="small"
  data-button-type="tertiary"
>
  <a
    href=""
    class="sqs-block-button-element--small sqs-button-element--tertiary sqs-block-button-element"
  >
    Prenota un appuntamento
  </a>
</div>
</div></div></div></div></div>
    </div>
  </div>
</section>
<section
  data-test="page-section"
  data-section-theme=""
  class='page-section 
      full-bleed-section
      layout-engine-section
    background-width--full-bleed
      section-height--small
      content-width--wide
    horizontal-alignment--center
    vertical-alignment--middle
    '
  data-section-id="67fcd7842f4717014f3e7d24"
  data-controller="SectionWrapperController"
  data-current-styles="{
        &quot;imageOverlayOpacity&quot;: 0.15,
        &quot;backgroundWidth&quot;: &quot;background-width--full-bleed&quot;,
        &quot;sectionHeight&quot;: &quot;section-height--small&quot;,
        &quot;customSectionHeight&quot;: 1,
        &quot;horizontalAlignment&quot;: &quot;horizontal-alignment--center&quot;,
        &quot;verticalAlignment&quot;: &quot;vertical-alignment--middle&quot;,
        &quot;contentWidth&quot;: &quot;content-width--wide&quot;,
        &quot;customContentWidth&quot;: 50,
        &quot;backgroundColor&quot;: &quot;&quot;,
        &quot;sectionTheme&quot;: &quot;&quot;,
        &quot;sectionAnimation&quot;: &quot;none&quot;,
        &quot;backgroundMode&quot;: &quot;image&quot;
      }"
  data-current-context="{
              &quot;video&quot;: {
                &quot;playbackSpeed&quot;: 1,
                &quot;filter&quot;: 1,
                &quot;filterStrength&quot;: 0,
              &quot;zoom&quot;: 0,
              &quot;videoSourceProvider&quot;: &quot;none&quot;
              },
              &quot;backgroundImageId&quot;: null,
              &quot;backgroundMediaEffect&quot;: {
                &quot;type&quot;: &quot;none&quot;
              },
              &quot;divider&quot;: {
                &quot;enabled&quot;: false
              },
              &quot;typeName&quot;: &quot;page&quot;
            }"
  data-animation="none"
  data-fluid-engine-section
>
  <div
    class="section-border"
  >
    <div class="section-background">
    </div>
  </div>
  <div
    class='content-wrapper'
    style='
    '
  >
    <div
      class="content"
    >
      <div data-fluid-engine="true"><style>
.fe-67fcd784319b323b26b285eb {
  --grid-gutter: calc(var(--sqs-mobile-site-gutter, 6vw) - 11.0px);
  --cell-max-width: calc( ( var(--sqs-site-max-width, 1500px) - (11.0px * (8 - 1)) ) / 8 );
  display: grid;
  position: relative;
  grid-area: 1/1/-1/-1;
  grid-template-rows: repeat(40,minmax(24px, auto));
  grid-template-columns:
    minmax(var(--grid-gutter), 1fr)
    repeat(8, minmax(0, var(--cell-max-width)))
    minmax(var(--grid-gutter), 1fr);
  row-gap: 11.0px;
  column-gap: 11.0px;
}
@media (min-width: 768px) {
  .background-width--inset .fe-67fcd784319b323b26b285eb {
    --inset-padding: calc(var(--sqs-site-gutter) * 2);
  }
  .fe-67fcd784319b323b26b285eb {
    --grid-gutter: calc(var(--sqs-site-gutter, 4vw) - 11.0px);
    --cell-max-width: calc( ( var(--sqs-site-max-width, 1500px) - (11.0px * (24 - 1)) ) / 24 );
    --inset-padding: 0vw;
    --row-height-scaling-factor: 0.0215;
    --container-width: min(var(--sqs-site-max-width, 1500px), calc(100vw - var(--sqs-site-gutter, 4vw) * 2 - var(--inset-padding) ));
    grid-template-rows: repeat(22,minmax(calc(var(--container-width) * var(--row-height-scaling-factor)), auto));
    grid-template-columns:
      minmax(var(--grid-gutter), 1fr)
      repeat(24, minmax(0, var(--cell-max-width)))
      minmax(var(--grid-gutter), 1fr);
  }
}
  .fe-block-yui_3_17_2_1_1744615607231_189027 {
    grid-area: 1/2/5/10;
    z-index: 1;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_189027 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_189027 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_189027 {
      grid-area: 1/6/4/20;
      z-index: 1;
    }
    .fe-block-yui_3_17_2_1_1744615607231_189027 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_189027 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_191429 {
    grid-area: 5/2/9/10;
    z-index: 4;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_191429 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_191429 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_191429 {
      grid-area: 7/13/9/26;
      z-index: 4;
    }
    .fe-block-yui_3_17_2_1_1744615607231_191429 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_191429 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_190812 {
    grid-area: 9/2/24/10;
    z-index: 3;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_190812 .sqs-block {
    justify-content: center;
  }
  .fe-block-yui_3_17_2_1_1744615607231_190812 .sqs-block-alignment-wrapper {
    align-items: center;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_190812 {
      grid-area: 7/2/23/12;
      z-index: 3;
    }
    .fe-block-yui_3_17_2_1_1744615607231_190812 .sqs-block {
      justify-content: center;
    }
    .fe-block-yui_3_17_2_1_1744615607231_190812 .sqs-block-alignment-wrapper {
      align-items: center;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_190437 {
    grid-area: 25/2/32/10;
    z-index: 2;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_190437 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_190437 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_190437 {
      grid-area: 4/2/7/26;
      z-index: 2;
    }
    .fe-block-yui_3_17_2_1_1744615607231_190437 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_190437 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_191780 {
    grid-area: 32/2/39/10;
    z-index: 5;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_191780 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_191780 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_191780 {
      grid-area: 10/13/15/26;
      z-index: 5;
    }
    .fe-block-yui_3_17_2_1_1744615607231_191780 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_191780 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_192607 {
    grid-area: 39/2/41/10;
    z-index: 6;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_192607 .sqs-block {
    justify-content: center;
  }
  .fe-block-yui_3_17_2_1_1744615607231_192607 .sqs-block-alignment-wrapper {
    align-items: center;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_192607 {
      grid-area: 17/13/19/19;
      z-index: 6;
    }
    .fe-block-yui_3_17_2_1_1744615607231_192607 .sqs-block {
      justify-content: center;
    }
    .fe-block-yui_3_17_2_1_1744615607231_192607 .sqs-block-alignment-wrapper {
      align-items: center;
    }
  }
</style><div class="fluid-engine fe-67fcd784319b323b26b285eb"><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_189027"><div class="sqs-block code-block sqs-block-code" data-block-type="23" id="block-yui_3_17_2_1_1744615607231_189027"><div class="sqs-block-content"><h1 class="responsive-title-sezione4">
 Come posso aiutarti?
</h1>
<style>
  .responsive-title-sezione4 {
    font-family: 'EB Garamond', serif;
    font-size: 72px;
    font-weight: 600;
    font-style: normal;
    margin: 0;
    text-align: center;
    color: black;
  }
  @media screen and (max-width: 600px) {
    .responsive-title-sezione4 {
      font-size: 52px;
    }
  }
</style>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_191429"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-yui_3_17_2_1_1744615607231_191429"><div class="sqs-block-content">
<div class="sqs-html-content">
  <p class="sqsrte-large" style="white-space:pre-wrap;">Ti offro un percorso completo e personalizzato per raggiungere il tuo equilibrio fisico e mentale.</p>
</div>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_190812"><div class="sqs-block image-block sqs-block-image sqs-stretched" data-aspect-ratio="68.76874557105535" data-block-type="5" id="block-yui_3_17_2_1_1744615607231_190812"><div class="sqs-block-content">
    <div
      class="
        image-block-outer-wrapper
        layout-caption-below
        design-layout-fluid
        image-position-center
        combination-animation-site-default
        individual-animation-site-default
      "
      data-test="image-block-fluid-outer-wrapper"
    >
      <div
        class="fluid-image-animation-wrapper sqs-image sqs-block-alignment-wrapper"
        data-animation-role="image"
      >
        <div
          class="fluid-image-container sqs-image-content"
          style="overflow: hidden;-webkit-mask-image: -webkit-radial-gradient(white, black);border-top-left-radius: 50px;border-top-right-radius: 50px;border-bottom-left-radius: 50px;border-bottom-right-radius: 50px;position: relative;width: 100%;height: 100%;"
        >
              <div class="content-fill">
            <img data-stretch="true" data-src="assets/images/roman_che_guarda_il_futuro.jpg" data-image="assets/images/roman_che_guarda_il_futuro.jpg" data-image-dimensions="1442x1920" data-image-focal-point="0.5277743291194116,0.3200319953862575" alt="null" data-load="false" elementtiming="system-image-block" src="assets/images/roman_che_guarda_il_futuro.jpg" width="1442" height="1920" alt="" sizes="100vw" style="display:block;object-fit: cover; object-position: 52.777432911941155% 32.00319953862575%" srcset="assets/images/roman_che_guarda_il_futuro.jpg?format=100w 100w, assets/images/roman_che_guarda_il_futuro.jpg?format=300w 300w, assets/images/roman_che_guarda_il_futuro.jpg?format=500w 500w, assets/images/roman_che_guarda_il_futuro.jpg?format=750w 750w, assets/images/roman_che_guarda_il_futuro.jpg?format=1000w 1000w, assets/images/roman_che_guarda_il_futuro.jpg?format=1500w 1500w, assets/images/roman_che_guarda_il_futuro.jpg?format=2500w 2500w" loading="lazy" decoding="async" data-loader="sqs">
            <div class="fluidImageOverlay"></div>
              </div>
        </div>
      </div>
    </div>
    <style>
      .sqs-block-image .sqs-block-content {
        height: 100%;
        width: 100%;
      }
        .fe-block-yui_3_17_2_1_1744615607231_190812 .fluidImageOverlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          mix-blend-mode: normal;
            opacity: 0;
        }
    </style>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_190437"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-yui_3_17_2_1_1744615607231_190437"><div class="sqs-block-content">
<div class="sqs-html-content">
  <h4 style="text-align:center;white-space:pre-wrap;">Il mio obiettivo è guidarti verso il tuo pieno potenziale, ispirandoti a vivere una vita equilibrata e felice. Credo che ogni scelta fatta in linea con il proprio scopo conduca a una felicità autentica e duratura.</h4>
</div>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_191780"><div class="sqs-block accordion-block sqs-block-accordion" data-blend-mode="NORMAL" data-block-type="69" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-yui_3_17_2_1_1744615607231_191780"><div class="sqs-block-content">
<ul
  class="accordion-items-container"
  data-should-allow-multiple-open-items=""
  data-is-divider-enabled="true"
  data-is-first-divider-visible="true"
  data-is-last-divider-visible="true"
  data-is-expanded-first-item=""
  data-accordion-title-alignment="left"
  data-accordion-description-alignment="left"
  data-accordion-description-placement="left"
  data-accordion-icon-placement="right"
>
    <li class="accordion-item">
          <div
            class="accordion-divider accordion-divider--top"
            aria-hidden="true"
            style="
              height: 1px;
              opacity: 1;
            "
          ></div>
      <h4
        class="
          accordion-item__title-wrapper
        "
        role="heading"
        aria-level="3"
      >
        <button
          class="accordion-item__click-target"
          aria-expanded="false"
          style="
            padding-top: 15px;
            padding-bottom: 15px;
            padding-left: 0px;
            padding-right: 0px;
          "
        >
          <span
            class="accordion-item__title"
            style="
                padding-right: 24px;
            "
          >
            Ascoltarti con attenzione
          </span>
          <div
            class="accordion-icon-container"
            data-is-open="false"
            aria-hidden="true"
            style="
              height: 24px;
              width: 24px;
            "
          >
              <div class="arrow-container">
                <div class="arrow"
                  style="
                    border-width: 3px;
                  "
                ></div>
              </div>
          </div>
        </button>
      </h4>
      <div class="accordion-item__dropdown" role="region">
        <div
          class="
            accordion-item__description
            sqsrte-large
          "
          style="
            padding-top: 0px;
            padding-bottom: 30px;
            padding-left: 0px;
            padding-right: 0px;
            min-width: 70%;
            max-width: 300px;
          "
        >
          <p data-rte-preserve-empty="true" style="white-space:pre-wrap;">Capire le tue esigenze, i tuoi obiettivi e le tue sfide</p>
        </div>
      </div>
        <div
          class="accordion-divider"
          aria-hidden="true"
          style="
            height: 1px;
            opacity: 1;
          "
        ></div>
    </li>
    <li class="accordion-item">
      <h4
        class="
          accordion-item__title-wrapper
        "
        role="heading"
        aria-level="3"
      >
        <button
          class="accordion-item__click-target"
          aria-expanded="false"
          style="
            padding-top: 15px;
            padding-bottom: 15px;
            padding-left: 0px;
            padding-right: 0px;
          "
        >
          <span
            class="accordion-item__title"
            style="
                padding-right: 24px;
            "
          >
            Creare un piano d'azione
          </span>
          <div
            class="accordion-icon-container"
            data-is-open="false"
            aria-hidden="true"
            style="
              height: 24px;
              width: 24px;
            "
          >
              <div class="arrow-container">
                <div class="arrow"
                  style="
                    border-width: 3px;
                  "
                ></div>
              </div>
          </div>
        </button>
      </h4>
      <div class="accordion-item__dropdown" role="region">
        <div
          class="
            accordion-item__description
            sqsrte-large
          "
          style="
            padding-top: 0px;
            padding-bottom: 30px;
            padding-left: 0px;
            padding-right: 0px;
            min-width: 70%;
            max-width: 300px;
          "
        >
          <p data-rte-preserve-empty="true" style="white-space:pre-wrap;">Un percorso che si adatti al tuo stile di vita e alle tue preferenze, con un'attenzione alla <strong>facilità di applicazione e alla gratificazione</strong></p>
        </div>
      </div>
        <div
          class="accordion-divider"
          aria-hidden="true"
          style="
            height: 1px;
            opacity: 1;
          "
        ></div>
    </li>
    <li class="accordion-item">
      <h4
        class="
          accordion-item__title-wrapper
        "
        role="heading"
        aria-level="3"
      >
        <button
          class="accordion-item__click-target"
          aria-expanded="false"
          style="
            padding-top: 15px;
            padding-bottom: 15px;
            padding-left: 0px;
            padding-right: 0px;
          "
        >
          <span
            class="accordion-item__title"
            style="
                padding-right: 24px;
            "
          >
            Insegnarti a fare scelte consapevoli
          </span>
          <div
            class="accordion-icon-container"
            data-is-open="false"
            aria-hidden="true"
            style="
              height: 24px;
              width: 24px;
            "
          >
              <div class="arrow-container">
                <div class="arrow"
                  style="
                    border-width: 3px;
                  "
                ></div>
              </div>
          </div>
        </button>
      </h4>
      <div class="accordion-item__dropdown" role="region">
        <div
          class="
            accordion-item__description
            sqsrte-large
          "
          style="
            padding-top: 0px;
            padding-bottom: 30px;
            padding-left: 0px;
            padding-right: 0px;
            min-width: 70%;
            max-width: 300px;
          "
        >
          <p data-rte-preserve-empty="true" style="white-space:pre-wrap;">Non solo durante il percorso ma anche dopo, per garantirti <strong>risultati duraturi e sostenibili</strong>.</p>
        </div>
      </div>
        <div
          class="accordion-divider"
          aria-hidden="true"
          style="
            height: 1px;
            opacity: 1;
          "
        ></div>
    </li>
</ul>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_192607"><div class="sqs-block button-block sqs-block-button sqs-stretched" data-block-type="53" id="block-yui_3_17_2_1_1744615607231_192607"><div class="sqs-block-content">
<div
  class="sqs-block-button-container sqs-block-button-container--center"
  data-animation-role="button"
  data-alignment="center"
  data-button-size="small"
  data-button-type="tertiary"
>
  <a
    href=""
    class="sqs-block-button-element--small sqs-button-element--tertiary sqs-block-button-element"
  >
    Prenota un appuntamento
  </a>
</div>
</div></div></div></div></div>
    </div>
  </div>
</section>
<section
  data-test="page-section"
  data-section-theme="light"
  class='page-section 
      full-bleed-section
      layout-engine-section
    background-width--full-bleed
      section-height--small
      content-width--wide
    horizontal-alignment--center
    vertical-alignment--middle
    light'
  data-section-id="67fcdfd19a265e0dfe26dcf2"
  data-controller="SectionWrapperController"
  data-current-styles="{
          &quot;imageOverlayOpacity&quot;: 0.15,
          &quot;backgroundWidth&quot;: &quot;background-width--full-bleed&quot;,
          &quot;sectionHeight&quot;: &quot;section-height--small&quot;,
          &quot;customSectionHeight&quot;: 1,
          &quot;horizontalAlignment&quot;: &quot;horizontal-alignment--center&quot;,
          &quot;verticalAlignment&quot;: &quot;vertical-alignment--middle&quot;,
          &quot;contentWidth&quot;: &quot;content-width--wide&quot;,
          &quot;customContentWidth&quot;: 50,
          &quot;sectionTheme&quot;: &quot;light&quot;,
          &quot;sectionAnimation&quot;: &quot;none&quot;,
          &quot;backgroundMode&quot;: &quot;image&quot;
        }"
  data-current-context="{
          &quot;video&quot;: {
            &quot;playbackSpeed&quot;: 0.5,
            &quot;filter&quot;: 1,
            &quot;filterStrength&quot;: 0,
            &quot;zoom&quot;: 0,
            &quot;videoSourceProvider&quot;: &quot;none&quot;
          },
          &quot;backgroundImageId&quot;: null,
          &quot;backgroundMediaEffect&quot;: {
            &quot;type&quot;: &quot;none&quot;
          },
          &quot;divider&quot;: {
            &quot;enabled&quot;: false
          },
          &quot;typeName&quot;: &quot;page&quot;
        }"
  data-animation="none"
  data-fluid-engine-section
>
  <div
    class="section-border"
  >
    <div class="section-background">
    </div>
  </div>
  <div
    class='content-wrapper'
    style='
    '
  >
    <div
      class="content"
    >
      <div data-fluid-engine="true"><style>
.fe-67fcdfd19a265e0dfe26dcf1 {
  --grid-gutter: calc(var(--sqs-mobile-site-gutter, 6vw) - 11.0px);
  --cell-max-width: calc( ( var(--sqs-site-max-width, 1500px) - (11.0px * (8 - 1)) ) / 8 );
  display: grid;
  position: relative;
  grid-area: 1/1/-1/-1;
  grid-template-rows: repeat(35,minmax(24px, auto));
  grid-template-columns:
    minmax(var(--grid-gutter), 1fr)
    repeat(8, minmax(0, var(--cell-max-width)))
    minmax(var(--grid-gutter), 1fr);
  row-gap: 11.0px;
  column-gap: 11.0px;
}
@media (min-width: 768px) {
  .background-width--inset .fe-67fcdfd19a265e0dfe26dcf1 {
    --inset-padding: calc(var(--sqs-site-gutter) * 2);
  }
  .fe-67fcdfd19a265e0dfe26dcf1 {
    --grid-gutter: calc(var(--sqs-site-gutter, 4vw) - 11.0px);
    --cell-max-width: calc( ( var(--sqs-site-max-width, 1500px) - (11.0px * (24 - 1)) ) / 24 );
    --inset-padding: 0vw;
    --row-height-scaling-factor: 0.0215;
    --container-width: min(var(--sqs-site-max-width, 1500px), calc(100vw - var(--sqs-site-gutter, 4vw) * 2 - var(--inset-padding) ));
    grid-template-rows: repeat(19,minmax(calc(var(--container-width) * var(--row-height-scaling-factor)), auto));
    grid-template-columns:
      minmax(var(--grid-gutter), 1fr)
      repeat(24, minmax(0, var(--cell-max-width)))
      minmax(var(--grid-gutter), 1fr);
  }
}
  .fe-block-c4ed36b5b8cc7e66329f {
    grid-area: 1/1/10/11;
    z-index: 0;
    @media (max-width: 767px) {
    }
  }
  .fe-block-c4ed36b5b8cc7e66329f .sqs-block {
    justify-content: center;
  }
  .fe-block-c4ed36b5b8cc7e66329f .sqs-block-alignment-wrapper {
    align-items: center;
  }
  @media (min-width: 768px) {
    .fe-block-c4ed36b5b8cc7e66329f {
      grid-area: 1/1/20/9;
      z-index: 1;
    }
    .fe-block-c4ed36b5b8cc7e66329f .sqs-block {
      justify-content: center;
    }
    .fe-block-c4ed36b5b8cc7e66329f .sqs-block-alignment-wrapper {
      align-items: center;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_194019 {
    grid-area: 10/2/18/10;
    z-index: 5;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_194019 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_194019 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_194019 {
      grid-area: 1/10/6/26;
      z-index: 5;
    }
    .fe-block-yui_3_17_2_1_1744615607231_194019 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_194019 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-f4c5167726970fc32a8d {
    grid-area: 18/2/23/10;
    z-index: 3;
    @media (max-width: 767px) {
    }
  }
  .fe-block-f4c5167726970fc32a8d .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-f4c5167726970fc32a8d .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-f4c5167726970fc32a8d {
      grid-area: 6/10/8/26;
      z-index: 3;
    }
    .fe-block-f4c5167726970fc32a8d .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-f4c5167726970fc32a8d .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_194430 {
    grid-area: 23/2/36/10;
    z-index: 6;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_194430 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_194430 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_194430 {
      grid-area: 9/10/18/26;
      z-index: 6;
    }
    .fe-block-yui_3_17_2_1_1744615607231_194430 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_194430 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
</style><div class="fluid-engine fe-67fcdfd19a265e0dfe26dcf1"><div class="fe-block fe-block-c4ed36b5b8cc7e66329f"><div class="sqs-block image-block sqs-block-image sqs-stretched" data-aspect-ratio="107.63790664780764" data-block-type="5" id="block-c4ed36b5b8cc7e66329f"><div class="sqs-block-content">
    <div
      class="
        image-block-outer-wrapper
        layout-caption-below
        design-layout-fluid
        image-position-center
        combination-animation-site-default
        individual-animation-site-default
      "
      data-test="image-block-fluid-outer-wrapper"
    >
      <div
        class="fluid-image-animation-wrapper sqs-image sqs-block-alignment-wrapper"
        data-animation-role="image"
      >
        <div
          class="fluid-image-container sqs-image-content"
          style="overflow: hidden;-webkit-mask-image: -webkit-radial-gradient(white, black);position: relative;width: 100%;height: 100%;"
        >
              <div class="content-fill">
            <img data-stretch="true" data-src="assets/images/whatsapp_image.jpg" data-image="assets/images/whatsapp_image.jpg" data-image-dimensions="1537x2048" data-image-focal-point="0.5,0.5" alt="Uomo che cucina tranci di salmone in una padella in cucina" data-load="false" elementtiming="system-image-block" src="assets/images/whatsapp_image.jpg" width="1537" height="2048" alt="" sizes="100vw" style="display:block;object-fit: cover; object-position: 50% 50%" srcset="assets/images/whatsapp_image.jpg?format=100w 100w, assets/images/whatsapp_image.jpg?format=300w 300w, assets/images/whatsapp_image.jpg?format=500w 500w, assets/images/whatsapp_image.jpg?format=750w 750w, assets/images/whatsapp_image.jpg?format=1000w 1000w, assets/images/whatsapp_image.jpg?format=1500w 1500w, assets/images/whatsapp_image.jpg?format=2500w 2500w" loading="lazy" decoding="async" data-loader="sqs">
            <div class="fluidImageOverlay"></div>
              </div>
        </div>
      </div>
    </div>
    <style>
      .sqs-block-image .sqs-block-content {
        height: 100%;
        width: 100%;
      }
        .fe-block-c4ed36b5b8cc7e66329f .fluidImageOverlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          mix-blend-mode: normal;
            opacity: 0;
        }
    </style>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_194019"><div class="sqs-block code-block sqs-block-code" data-block-type="23" id="block-yui_3_17_2_1_1744615607231_194019"><div class="sqs-block-content"><h1 class="responsive-title-sezione-nutrizione-1">
Nutrizione: Mangiare sano non è mai stato così facile
</h1>
<style>
  .responsive-title-sezione-nutrizione-1 {
    font-family: 'EB Garamond', serif;
    font-size: 72px;
    font-weight: 600;
    font-style: normal;
    margin: 0;
    text-align:left;
    color: #6E7D58;
  }
  @media screen and (max-width: 600px) {
    .responsive-title-sezione-nutrizione-1 {
      font-size: 52px;
    }
  }
</style>
</div></div></div><div class="fe-block fe-block-f4c5167726970fc32a8d"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-f4c5167726970fc32a8d"><div class="sqs-block-content">
<div class="sqs-html-content">
  <p class="sqsrte-large" style="white-space:pre-wrap;">Dimentica la dieta. Il mio metodo si basa su un piano alimentare <strong>facile da seguire, buono da mangiare e veloce da preparare.</strong></p>
</div>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_194430"><div class="sqs-block accordion-block sqs-block-accordion" data-blend-mode="NORMAL" data-block-type="69" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-yui_3_17_2_1_1744615607231_194430"><div class="sqs-block-content">
<ul
  class="accordion-items-container"
  data-should-allow-multiple-open-items=""
  data-is-divider-enabled="true"
  data-is-first-divider-visible="true"
  data-is-last-divider-visible="true"
  data-is-expanded-first-item="true"
  data-accordion-title-alignment="left"
  data-accordion-description-alignment="left"
  data-accordion-description-placement="left"
  data-accordion-icon-placement="right"
>
    <li class="accordion-item">
          <div
            class="accordion-divider accordion-divider--top"
            aria-hidden="true"
            style="
              height: 1px;
              opacity: 1;
            "
          ></div>
      <h4
        class="
          accordion-item__title-wrapper
        "
        role="heading"
        aria-level="3"
      >
        <button
          class="accordion-item__click-target"
          aria-expanded="false"
          style="
            padding-top: 30px;
            padding-bottom: 30px;
            padding-left: 0px;
            padding-right: 0px;
          "
        >
          <span
            class="accordion-item__title"
            style="
                padding-right: 24px;
            "
          >
            Come funziona?
          </span>
          <div
            class="accordion-icon-container"
            data-is-open="false"
            aria-hidden="true"
            style="
              height: 24px;
              width: 24px;
            "
          >
              <div class="arrow-container">
                <div class="arrow"
                  style="
                    border-width: 3px;
                  "
                ></div>
              </div>
          </div>
        </button>
      </h4>
      <div class="accordion-item__dropdown" role="region">
        <div
          class="
            accordion-item__description
          "
          style="
            padding-top: 0px;
            padding-bottom: 30px;
            padding-left: 0px;
            padding-right: 0px;
            min-width: 70%;
            max-width: 300px;
          "
        >
          <p data-rte-preserve-empty="true" style="white-space:pre-wrap;">Inizieremo con un'analisi approfondita delle tue abitudini e preferenze per creare un menù su misura per te, facile da realizzare, anche se hai poco tempo. Ti seguirò passo dopo passo, monitorando i tuoi progressi e supportando il tuo percorso.</p>
        </div>
      </div>
        <div
          class="accordion-divider"
          aria-hidden="true"
          style="
            height: 1px;
            opacity: 1;
          "
        ></div>
    </li>
    <li class="accordion-item">
      <h4
        class="
          accordion-item__title-wrapper
        "
        role="heading"
        aria-level="3"
      >
        <button
          class="accordion-item__click-target"
          aria-expanded="false"
          style="
            padding-top: 30px;
            padding-bottom: 30px;
            padding-left: 0px;
            padding-right: 0px;
          "
        >
          <span
            class="accordion-item__title"
            style="
                padding-right: 24px;
            "
          >
            Cosa otterrai?
          </span>
          <div
            class="accordion-icon-container"
            data-is-open="false"
            aria-hidden="true"
            style="
              height: 24px;
              width: 24px;
            "
          >
              <div class="arrow-container">
                <div class="arrow"
                  style="
                    border-width: 3px;
                  "
                ></div>
              </div>
          </div>
        </button>
      </h4>
      <div class="accordion-item__dropdown" role="region">
        <div
          class="
            accordion-item__description
          "
          style="
            padding-top: 0px;
            padding-bottom: 30px;
            padding-left: 0px;
            padding-right: 0px;
            min-width: 70%;
            max-width: 300px;
          "
        >
          <p data-rte-preserve-empty="true" style="white-space:pre-wrap;">Un'alimentazione personalizzata che ti aiuterà a raggiungere il tuo peso forma, aumentando la tua energia e migliorando la tua consapevolezza alimentare.</p>
        </div>
      </div>
        <div
          class="accordion-divider"
          aria-hidden="true"
          style="
            height: 1px;
            opacity: 1;
          "
        ></div>
    </li>
</ul>
</div></div></div></div></div>
    </div>
  </div>
</section>
<section
  data-test="page-section"
  data-section-theme="light"
  class='page-section 
      full-bleed-section
      layout-engine-section
    background-width--full-bleed
      section-height--small
      content-width--wide
    horizontal-alignment--center
    vertical-alignment--middle
    light'
  data-section-id="67fce17474bbe922f5c23fbb"
  data-controller="SectionWrapperController"
  data-current-styles="{
          &quot;imageOverlayOpacity&quot;: 0.15,
          &quot;backgroundWidth&quot;: &quot;background-width--full-bleed&quot;,
          &quot;sectionHeight&quot;: &quot;section-height--small&quot;,
          &quot;customSectionHeight&quot;: 1,
          &quot;horizontalAlignment&quot;: &quot;horizontal-alignment--center&quot;,
          &quot;verticalAlignment&quot;: &quot;vertical-alignment--middle&quot;,
          &quot;contentWidth&quot;: &quot;content-width--wide&quot;,
          &quot;customContentWidth&quot;: 50,
          &quot;sectionTheme&quot;: &quot;light&quot;,
          &quot;sectionAnimation&quot;: &quot;none&quot;,
          &quot;backgroundMode&quot;: &quot;image&quot;
        }"
  data-current-context="{
          &quot;video&quot;: {
            &quot;playbackSpeed&quot;: 0.5,
            &quot;filter&quot;: 1,
            &quot;filterStrength&quot;: 0,
            &quot;zoom&quot;: 0,
            &quot;videoSourceProvider&quot;: &quot;none&quot;
          },
          &quot;backgroundImageId&quot;: null,
          &quot;backgroundMediaEffect&quot;: {
            &quot;type&quot;: &quot;none&quot;
          },
          &quot;divider&quot;: {
            &quot;enabled&quot;: false
          },
          &quot;typeName&quot;: &quot;page&quot;
        }"
  data-animation="none"
  data-fluid-engine-section
>
  <div
    class="section-border"
  >
    <div class="section-background">
    </div>
  </div>
  <div
    class='content-wrapper'
    style='
    '
  >
    <div
      class="content"
    >
      <div data-fluid-engine="true"><style>
.fe-67fce17474bbe922f5c23fba {
  --grid-gutter: calc(var(--sqs-mobile-site-gutter, 6vw) - 11.0px);
  --cell-max-width: calc( ( var(--sqs-site-max-width, 1500px) - (11.0px * (8 - 1)) ) / 8 );
  display: grid;
  position: relative;
  grid-area: 1/1/-1/-1;
  grid-template-rows: repeat(32,minmax(24px, auto));
  grid-template-columns:
    minmax(var(--grid-gutter), 1fr)
    repeat(8, minmax(0, var(--cell-max-width)))
    minmax(var(--grid-gutter), 1fr);
  row-gap: 11.0px;
  column-gap: 11.0px;
}
@media (min-width: 768px) {
  .background-width--inset .fe-67fce17474bbe922f5c23fba {
    --inset-padding: calc(var(--sqs-site-gutter) * 2);
  }
  .fe-67fce17474bbe922f5c23fba {
    --grid-gutter: calc(var(--sqs-site-gutter, 4vw) - 11.0px);
    --cell-max-width: calc( ( var(--sqs-site-max-width, 1500px) - (11.0px * (24 - 1)) ) / 24 );
    --inset-padding: 0vw;
    --row-height-scaling-factor: 0.0215;
    --container-width: min(var(--sqs-site-max-width, 1500px), calc(100vw - var(--sqs-site-gutter, 4vw) * 2 - var(--inset-padding) ));
    grid-template-rows: repeat(19,minmax(calc(var(--container-width) * var(--row-height-scaling-factor)), auto));
    grid-template-columns:
      minmax(var(--grid-gutter), 1fr)
      repeat(24, minmax(0, var(--cell-max-width)))
      minmax(var(--grid-gutter), 1fr);
  }
}
  .fe-block-16494ed3afe1335d729a {
    grid-area: 1/1/10/11;
    z-index: 0;
    @media (max-width: 767px) {
    }
  }
  .fe-block-16494ed3afe1335d729a .sqs-block {
    justify-content: center;
  }
  .fe-block-16494ed3afe1335d729a .sqs-block-alignment-wrapper {
    align-items: center;
  }
  @media (min-width: 768px) {
    .fe-block-16494ed3afe1335d729a {
      grid-area: 1/20/20/27;
      z-index: 1;
    }
    .fe-block-16494ed3afe1335d729a .sqs-block {
      justify-content: center;
    }
    .fe-block-16494ed3afe1335d729a .sqs-block-alignment-wrapper {
      align-items: center;
    }
  }
  .fe-block-55a3a35ea602c0b034a3 {
    grid-area: 10/2/16/10;
    z-index: 5;
    @media (max-width: 767px) {
    }
  }
  .fe-block-55a3a35ea602c0b034a3 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-55a3a35ea602c0b034a3 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-55a3a35ea602c0b034a3 {
      grid-area: 1/2/4/20;
      z-index: 5;
    }
    .fe-block-55a3a35ea602c0b034a3 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-55a3a35ea602c0b034a3 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-8d412e060a7741b73654 {
    grid-area: 20/2/33/10;
    z-index: 6;
    @media (max-width: 767px) {
    }
  }
  .fe-block-8d412e060a7741b73654 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-8d412e060a7741b73654 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-8d412e060a7741b73654 {
      grid-area: 6/2/14/18;
      z-index: 6;
    }
    .fe-block-8d412e060a7741b73654 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-8d412e060a7741b73654 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-458d4ea15b827093c171 {
    grid-area: 16/2/20/10;
    z-index: 3;
    @media (max-width: 767px) {
    }
  }
  .fe-block-458d4ea15b827093c171 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-458d4ea15b827093c171 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-458d4ea15b827093c171 {
      grid-area: 4/2/6/18;
      z-index: 3;
    }
    .fe-block-458d4ea15b827093c171 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-458d4ea15b827093c171 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
</style><div class="fluid-engine fe-67fce17474bbe922f5c23fba"><div class="fe-block fe-block-16494ed3afe1335d729a"><div class="sqs-block image-block sqs-block-image sqs-stretched" data-aspect-ratio="107.63790664780764" data-block-type="5" id="block-16494ed3afe1335d729a"><div class="sqs-block-content">
    <div
      class="
        image-block-outer-wrapper
        layout-caption-below
        design-layout-fluid
        image-position-center
        combination-animation-site-default
        individual-animation-site-default
      "
      data-test="image-block-fluid-outer-wrapper"
    >
      <div
        class="fluid-image-animation-wrapper sqs-image sqs-block-alignment-wrapper"
        data-animation-role="image"
      >
        <div
          class="fluid-image-container sqs-image-content"
          style="overflow: hidden;-webkit-mask-image: -webkit-radial-gradient(white, black);position: relative;width: 100%;height: 100%;"
        >
              <div class="content-fill">
            <img data-stretch="true" data-src="assets/images/roman_solleva.jpg" data-image="assets/images/roman_solleva.jpg" data-image-dimensions="1442x1920" data-image-focal-point="0.5,0.5" alt="Uomo che si esercita con un kettlebell all'aperto, sorridente, vestito sportivamente con pantaloni sportivi, felpa e scarpe da ginnastica." data-load="false" elementtiming="system-image-block" src="assets/images/roman_solleva.jpg" width="1442" height="1920" alt="" sizes="100vw" style="display:block;object-fit: cover; object-position: 50% 50%" srcset="assets/images/roman_solleva.jpg?format=100w 100w, assets/images/roman_solleva.jpg?format=300w 300w, assets/images/roman_solleva.jpg?format=500w 500w, assets/images/roman_solleva.jpg?format=750w 750w, assets/images/roman_solleva.jpg?format=1000w 1000w, assets/images/roman_solleva.jpg?format=1500w 1500w, assets/images/roman_solleva.jpg?format=2500w 2500w" loading="lazy" decoding="async" data-loader="sqs">
            <div class="fluidImageOverlay"></div>
              </div>
        </div>
      </div>
    </div>
    <style>
      .sqs-block-image .sqs-block-content {
        height: 100%;
        width: 100%;
      }
        .fe-block-16494ed3afe1335d729a .fluidImageOverlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          mix-blend-mode: normal;
            opacity: 0;
        }
    </style>
</div></div></div><div class="fe-block fe-block-55a3a35ea602c0b034a3"><div class="sqs-block code-block sqs-block-code" data-block-type="23" id="block-55a3a35ea602c0b034a3"><div class="sqs-block-content"><h1 class="responsive-title-sezione-allenamento">
Allenamento: definisci il tuo corpo
</h1>
<style>
  .responsive-title-sezione-allenamento {
    font-family: 'EB Garamond', serif;
    font-size: 72px;
    font-weight: 600;
    font-style: normal;
    margin: 0;
    text-align:left;
    color:#6E7D58;
  }
  @media screen and (max-width: 600px) {
    .responsive-title-sezione-allenamento {
      font-size: 52px;
    }
  }
</style>
</div></div></div><div class="fe-block fe-block-8d412e060a7741b73654"><div class="sqs-block accordion-block sqs-block-accordion" data-blend-mode="NORMAL" data-block-type="69" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-8d412e060a7741b73654"><div class="sqs-block-content">
<ul
  class="accordion-items-container"
  data-should-allow-multiple-open-items=""
  data-is-divider-enabled="true"
  data-is-first-divider-visible="true"
  data-is-last-divider-visible="true"
  data-is-expanded-first-item="true"
  data-accordion-title-alignment="left"
  data-accordion-description-alignment="left"
  data-accordion-description-placement="left"
  data-accordion-icon-placement="right"
>
    <li class="accordion-item">
          <div
            class="accordion-divider accordion-divider--top"
            aria-hidden="true"
            style="
              height: 1px;
              opacity: 1;
            "
          ></div>
      <h4
        class="
          accordion-item__title-wrapper
        "
        role="heading"
        aria-level="3"
      >
        <button
          class="accordion-item__click-target"
          aria-expanded="false"
          style="
            padding-top: 30px;
            padding-bottom: 30px;
            padding-left: 0px;
            padding-right: 0px;
          "
        >
          <span
            class="accordion-item__title"
            style="
                padding-right: 24px;
            "
          >
            Come funziona?
          </span>
          <div
            class="accordion-icon-container"
            data-is-open="false"
            aria-hidden="true"
            style="
              height: 24px;
              width: 24px;
            "
          >
              <div class="arrow-container">
                <div class="arrow"
                  style="
                    border-width: 3px;
                  "
                ></div>
              </div>
          </div>
        </button>
      </h4>
      <div class="accordion-item__dropdown" role="region">
        <div
          class="
            accordion-item__description
          "
          style="
            padding-top: 0px;
            padding-bottom: 30px;
            padding-left: 0px;
            padding-right: 0px;
            min-width: 70%;
            max-width: 300px;
          "
        >
          <p data-rte-preserve-empty="true" style="white-space:pre-wrap;">Inizieremo con un'analisi del tuo stile di vita per creare un programma di allenamento su misura per te, con esercizi che puoi svolgere dove e quando vuoi. Ti seguirò passo dopo passo, adattando il programma ai tuoi progressi.</p>
        </div>
      </div>
        <div
          class="accordion-divider"
          aria-hidden="true"
          style="
            height: 1px;
            opacity: 1;
          "
        ></div>
    </li>
    <li class="accordion-item">
      <h4
        class="
          accordion-item__title-wrapper
        "
        role="heading"
        aria-level="3"
      >
        <button
          class="accordion-item__click-target"
          aria-expanded="false"
          style="
            padding-top: 30px;
            padding-bottom: 30px;
            padding-left: 0px;
            padding-right: 0px;
          "
        >
          <span
            class="accordion-item__title"
            style="
                padding-right: 24px;
            "
          >
            Cosa otterrai?
          </span>
          <div
            class="accordion-icon-container"
            data-is-open="false"
            aria-hidden="true"
            style="
              height: 24px;
              width: 24px;
            "
          >
              <div class="arrow-container">
                <div class="arrow"
                  style="
                    border-width: 3px;
                  "
                ></div>
              </div>
          </div>
        </button>
      </h4>
      <div class="accordion-item__dropdown" role="region">
        <div
          class="
            accordion-item__description
          "
          style="
            padding-top: 0px;
            padding-bottom: 30px;
            padding-left: 0px;
            padding-right: 0px;
            min-width: 70%;
            max-width: 300px;
          "
        >
          <p data-rte-preserve-empty="true" style="white-space:pre-wrap;">Un programma di allenamento personalizzato che ti aiuterà a migliorare la tua forma fisica, aumentando la tua energia, la tua autostima e il tuo benessere mentale.</p>
        </div>
      </div>
        <div
          class="accordion-divider"
          aria-hidden="true"
          style="
            height: 1px;
            opacity: 1;
          "
        ></div>
    </li>
</ul>
</div></div></div><div class="fe-block fe-block-458d4ea15b827093c171"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-458d4ea15b827093c171"><div class="sqs-block-content">
<div class="sqs-html-content">
  <p class="sqsrte-large" style="white-space:pre-wrap;">Il mio metodo si basa sul seguire un programma <strong>facile, divertente e costante</strong>.</p>
</div>
</div></div></div></div></div>
    </div>
  </div>
</section>
<section
  data-test="page-section"
  data-section-theme="white"
  class='page-section 
      user-items-list-section
      full-bleed-section
    white'
  data-section-id="67fce33585fe9d57073fd638"
  data-controller="SectionWrapperController"
  data-current-styles="{
          &quot;imageFocalPoint&quot;: {
            &quot;x&quot;: 0.5,
            &quot;y&quot;: 0.5
          },
          &quot;imageOverlayOpacity&quot;: 0.3,
          &quot;backgroundColor&quot;: &quot;white&quot;,
          &quot;sectionTheme&quot;: &quot;white&quot;,
          &quot;imageEffect&quot;: &quot;none&quot;,
          &quot;backgroundMode&quot;: &quot;image&quot;,
          &quot;backgroundImage&quot;: null
        }"
  data-current-context="{
          &quot;video&quot;: {
            &quot;filter&quot;: 1,
            &quot;videoFallbackContentItem&quot;: null,
            &quot;nativeVideoContentItem&quot;: null,
            &quot;videoSourceProvider&quot;: &quot;none&quot;
          },
          &quot;backgroundImageId&quot;: null,
          &quot;backgroundMediaEffect&quot;: {
            &quot;type&quot;: &quot;none&quot;
          },
          &quot;divider&quot;: {
            &quot;enabled&quot;: false
          },
          &quot;typeName&quot;: &quot;page&quot;
        }"
  data-animation=""
  data-json-schema-section 
>
  <div
    class="section-border"
  >
    <div class="section-background">
    </div>
  </div>
  <div
    class='content-wrapper'
    style='
    '
  >
    <div
      class="content"
    >
<div
  class="user-items-list"
  style="
    min-height: 100px;
    padding-top: 3.3vmax;
    padding-bottom: 3.3vmax;
    "
  data-card-theme=""
  data-section-id="user-items-list"
  data-section-title-enabled="true"
  data-space-below-section-title-value="40"
  data-space-below-section-title-unit="px"
  data-layout-width="full"
>
    <div class="list-section-title"
      id="67fce33585fe9d57073fd638"
      style= "padding-bottom: 40px;"
      data-section-title-alignment="center"
    >
      <p class="" data-rte-preserve-empty="true" style="white-space:pre-wrap;">Le mie storie di successo:</p>
    </div>
<style>
  .user-items-list-item-container[data-section-id="67fce33585fe9d57073fd638"] .list-item-content__title {
    font-size: 1.2rem;
  }
  .user-items-list-item-container[data-section-id="67fce33585fe9d57073fd638"] .list-item-content__description {
    font-size: 0.9rem;
  }
  .user-items-list-item-container[data-section-id="67fce33585fe9d57073fd638"] .list-item-content__button {
    font-size: 0.8rem;
  }
  @supports (--test-custom-property: true) {
    .user-items-list-item-container[data-section-id="67fce33585fe9d57073fd638"] {
      --title-font-size-value: 1.2;
      --body-font-size-value: 0.9;
      --button-font-size-value: 0.8;
    }
  }
</style>
<div
  class="
    user-items-list-item-container
    user-items-list-carousel
  "
  data-controller="UserItemsListCarousel"
  data-max-columns="3"
  data-show-adjacent-slides="true"
  data-space-between-slides-value="20"
  data-space-between-slides-unit="px"
  data-media-aspect-ratio="4:3"
  data-is-infinite-enabled="true"
  data-navigation-controls="arrows"
  data-navigation-placement="bottom"
  data-navigation-alignment="center"
  data-alignment-vertical="top"
  data-section-id="67fce33585fe9d57073fd638"
  data-current-context="{
          &quot;userItems&quot;: [ {
            &quot;title&quot;: &quot;Dielise Rossetti, 43 ★★★★★&quot;,
            &quot;description&quot;: &quot;&lt;p class=\&quot;\&quot; data-rte-preserve-empty=\&quot;true\&quot; style=\&quot;white-space:pre-wrap;\&quot;&gt;“Ho seguito molte diete da quelle più classiche a quelle più rivoluzionarie ma sempre con esito negativo. Nel tempo ho conosciuto il Dott. Roman Fontana che ha realizzato una dieta compatibile sia con le mie preferenze alimentari e sia per il fabbisogno del mio fisico, prendendo in considerazione ogni aspetto della mia vita. Nel giro di un anno da 72kg sono arrivata a 57kg, perdendo una media di 20cm in tutto il corpo. Tutto questo mantenendo un equilibrio tra massa magra e massa grassa. Consiglio assolutamente i metodi e la professionalità del Dott. Roman Fontana!!!”&lt;\/p&gt;&quot;,
            &quot;button&quot;: {
              &quot;buttonText&quot;: &quot;Leggi le biografie&quot;,
              &quot;buttonLink&quot;: &quot;#&quot;
            },
            &quot;imageAltText&quot;: &quot;&quot;,
            &quot;imageId&quot;: &quot;680f5d0fa9cbfe1281367561&quot;,
            &quot;image&quot;: {
              &quot;id&quot;: &quot;680f5d0fa9cbfe1281367561&quot;,
              &quot;recordType&quot;: 2,
              &quot;addedOn&quot;: 1745837327757,
              &quot;updatedOn&quot;: 1745837327824,
              &quot;workflowState&quot;: 1,
              &quot;publishOn&quot;: 1745837327757,
              &quot;authorId&quot;: &quot;5963fc0e414fb5c08934f4ef&quot;,
              &quot;systemDataId&quot;: &quot;0ca8adf0-7333-4312-83c0-c7a328703490&quot;,
              &quot;systemDataVariants&quot;: &quot;717x481,100w,300w,500w&quot;,
              &quot;systemDataSourceType&quot;: &quot;PNG&quot;,
              &quot;filename&quot;: &quot;DIELISE.png&quot;,
              &quot;mediaFocalPoint&quot;: {
                &quot;x&quot;: 0.5,
                &quot;y&quot;: 0.5,
                &quot;source&quot;: 3
              },
              &quot;colorData&quot;: {
                &quot;topLeftAverage&quot;: &quot;9eaaab&quot;,
                &quot;topRightAverage&quot;: &quot;b6b99e&quot;,
                &quot;bottomLeftAverage&quot;: &quot;3d4a58&quot;,
                &quot;bottomRightAverage&quot;: &quot;d7cbc9&quot;,
                &quot;centerAverage&quot;: &quot;838469&quot;,
                &quot;suggestedBgColor&quot;: &quot;85857b&quot;
              },
              &quot;urlId&quot;: &quot;jctwf9nr0uf69uilhrt1un4aj7vtnk&quot;,
              &quot;title&quot;: &quot;&quot;,
              &quot;body&quot;: null,
              &quot;likeCount&quot;: 0,
              &quot;commentCount&quot;: 0,
              &quot;publicCommentCount&quot;: 0,
              &quot;commentState&quot;: 2,
              &quot;unsaved&quot;: false,
              &quot;author&quot;: {
                &quot;id&quot;: &quot;5963fc0e414fb5c08934f4ef&quot;,
                &quot;displayName&quot;: &quot;Roman Fontana&quot;,
                &quot;firstName&quot;: &quot;Fabio&quot;,
                &quot;lastName&quot;: &quot;Bertozzi&quot;
              },
              &quot;assetUrl&quot;: &quot;assets/images/DIELISE.png&quot;,
              &quot;contentType&quot;: &quot;image/png&quot;,
              &quot;items&quot;: [ ],
              &quot;pushedServices&quot;: { },
              &quot;pendingPushedServices&quot;: { },
              &quot;originalSize&quot;: &quot;717x481&quot;,
              &quot;recordTypeLabel&quot;: &quot;image&quot;
            }
          }, {
            &quot;title&quot;: &quot;Laura Davezza, 54 ★★★★★&quot;,
            &quot;description&quot;: &quot;&lt;p class=\&quot;\&quot; data-rte-preserve-empty=\&quot;true\&quot; style=\&quot;white-space:pre-wrap;\&quot;&gt;“Con un peso iniziale di 109 kg, ho deciso di intraprendere un percorso annuale per avere un supporto costante. Nei primi tre mesi ho perso 20 kg, grazie a una dieta su misura per i miei gusti e abitudini, con un pasto libero settimanale e piccoli adattamenti nel tempo. A Natale avevo già perso oltre 30 kg, e poter entrare in un negozio normale per comprare un paio di jeans è stata una conquista incredibile. Non solo il corpo ne ha tratto beneficio, ma anche la mia autostima e il controllo sul mio corpo sono migliorati enormemente. A giugno, con 45 kg persi in un anno, ho raggiunto il peso di 64 kg. Oggi, a distanza di due anni, mantengo il mio peso senza seguire la dieta, avendo imparato a capire cosa il mio corpo richiede. I sacrifici ci sono, ma vengono ripagati dai risultati. Cambiare abitudini e conoscere il proprio corpo sono la chiave per vivere in armonia con sé stessi.”&lt;\/p&gt;&quot;,
            &quot;button&quot;: {
              &quot;buttonText&quot;: &quot;Leggi le biografie&quot;
            },
            &quot;imageAltText&quot;: &quot;&quot;,
            &quot;imageId&quot;: &quot;680f5d26dbf82322a0938c21&quot;,
            &quot;image&quot;: {
              &quot;id&quot;: &quot;680f5d26dbf82322a0938c21&quot;,
              &quot;recordType&quot;: 2,
              &quot;addedOn&quot;: 1745837350706,
              &quot;updatedOn&quot;: 1745837350775,
              &quot;workflowState&quot;: 1,
              &quot;publishOn&quot;: 1745837350706,
              &quot;authorId&quot;: &quot;5963fc0e414fb5c08934f4ef&quot;,
              &quot;systemDataId&quot;: &quot;9981939a-cd5a-45e6-a772-7e9c3901712b&quot;,
              &quot;systemDataVariants&quot;: &quot;504x502,100w,300w,500w&quot;,
              &quot;systemDataSourceType&quot;: &quot;PNG&quot;,
              &quot;filename&quot;: &quot;davezza.png&quot;,
              &quot;mediaFocalPoint&quot;: {
                &quot;x&quot;: 0.5,
                &quot;y&quot;: 0.5,
                &quot;source&quot;: 3
              },
              &quot;colorData&quot;: {
                &quot;topLeftAverage&quot;: &quot;bbbec3&quot;,
                &quot;topRightAverage&quot;: &quot;b6a5bf&quot;,
                &quot;bottomLeftAverage&quot;: &quot;a5a2a0&quot;,
                &quot;bottomRightAverage&quot;: &quot;756e61&quot;,
                &quot;centerAverage&quot;: &quot;7f8088&quot;,
                &quot;suggestedBgColor&quot;: &quot;807a79&quot;
              },
              &quot;urlId&quot;: &quot;b2me74sq27boqh3o5ne6shvp0d0vyw&quot;,
              &quot;title&quot;: &quot;&quot;,
              &quot;body&quot;: null,
              &quot;likeCount&quot;: 0,
              &quot;commentCount&quot;: 0,
              &quot;publicCommentCount&quot;: 0,
              &quot;commentState&quot;: 2,
              &quot;unsaved&quot;: false,
              &quot;author&quot;: {
                &quot;id&quot;: &quot;5963fc0e414fb5c08934f4ef&quot;,
                &quot;displayName&quot;: &quot;Roman Fontana&quot;,
                &quot;firstName&quot;: &quot;Fabio&quot;,
                &quot;lastName&quot;: &quot;Bertozzi&quot;
              },
              &quot;assetUrl&quot;: &quot;assets/images/davezza.png&quot;,
              &quot;contentType&quot;: &quot;image/png&quot;,
              &quot;items&quot;: [ ],
              &quot;pushedServices&quot;: { },
              &quot;pendingPushedServices&quot;: { },
              &quot;originalSize&quot;: &quot;504x502&quot;,
              &quot;recordTypeLabel&quot;: &quot;image&quot;
            }
          }, {
            &quot;title&quot;: &quot;Enrico Marchegiani, 43 ★★★★★&quot;,
            &quot;description&quot;: &quot;&lt;p data-rte-preserve-empty=\&quot;true\&quot; style=\&quot;white-space:pre-wrap;\&quot;&gt;“Quando ho cominciato pesavo 106.8 Kg. Volevo un cambio di marcia a livello fisico, venivo da un infortunio al ginocchio e sentivo la necessità di alleggerire l'articolazione per muovermi meglio. Sono arrivato ad un minimo storico di 80 kg (a 15 anni pesavo così) seguendo Roman, complice il lockdown che ha impossibilitato le solite birre e calici di vino con gli amici. Le mie difficoltà nel seguire la dieta? La fame prima di cena e la mia impossibilità di tenere altro cibo che non appartenesse alla dieta dentro casa, in particolare nel periodo più ferreo. Cosa mi ha aiutato? Il mio coinquilino che faceva la dieta come la mia (anche simile nei pasti in certi periodi). Sono un amante della montagna e la vera differenza la sento proprio lì. Mi sembra di camminare per i sentieri con una ferrari sotto il sedere rispetto a prima.”&lt;\/p&gt;&quot;,
            &quot;button&quot;: {
              &quot;buttonText&quot;: &quot;Leggi le biografie&quot;
            },
            &quot;imageAltText&quot;: &quot;&quot;,
            &quot;imageId&quot;: &quot;680f5d36a9cbfe12813680b4&quot;,
            &quot;image&quot;: {
              &quot;id&quot;: &quot;680f5d36a9cbfe12813680b4&quot;,
              &quot;recordType&quot;: 2,
              &quot;addedOn&quot;: 1745837366530,
              &quot;updatedOn&quot;: 1745837366595,
              &quot;workflowState&quot;: 1,
              &quot;publishOn&quot;: 1745837366530,
              &quot;authorId&quot;: &quot;5963fc0e414fb5c08934f4ef&quot;,
              &quot;systemDataId&quot;: &quot;f2e6601f-8650-4657-b57e-7bea318fb2fa&quot;,
              &quot;systemDataVariants&quot;: &quot;379x542,100w,300w&quot;,
              &quot;systemDataSourceType&quot;: &quot;PNG&quot;,
              &quot;filename&quot;: &quot;marchegiani.png&quot;,
              &quot;mediaFocalPoint&quot;: {
                &quot;x&quot;: 0.5,
                &quot;y&quot;: 0.5,
                &quot;source&quot;: 3
              },
              &quot;colorData&quot;: {
                &quot;topLeftAverage&quot;: &quot;947480&quot;,
                &quot;topRightAverage&quot;: &quot;d8e8f5&quot;,
                &quot;bottomLeftAverage&quot;: &quot;594240&quot;,
                &quot;bottomRightAverage&quot;: &quot;8e8470&quot;,
                &quot;centerAverage&quot;: &quot;94849b&quot;,
                &quot;suggestedBgColor&quot;: &quot;867475&quot;
              },
              &quot;urlId&quot;: &quot;z9rq45inss5t9bzlwkzbaunz4e9k2e&quot;,
              &quot;title&quot;: &quot;&quot;,
              &quot;body&quot;: null,
              &quot;likeCount&quot;: 0,
              &quot;commentCount&quot;: 0,
              &quot;publicCommentCount&quot;: 0,
              &quot;commentState&quot;: 2,
              &quot;unsaved&quot;: false,
              &quot;author&quot;: {
                &quot;id&quot;: &quot;5963fc0e414fb5c08934f4ef&quot;,
                &quot;displayName&quot;: &quot;Roman Fontana&quot;,
                &quot;firstName&quot;: &quot;Fabio&quot;,
                &quot;lastName&quot;: &quot;Bertozzi&quot;
              },
              &quot;assetUrl&quot;: &quot;assets/images/marchegiani.png&quot;,
              &quot;contentType&quot;: &quot;image/png&quot;,
              &quot;items&quot;: [ ],
              &quot;pushedServices&quot;: { },
              &quot;pendingPushedServices&quot;: { },
              &quot;originalSize&quot;: &quot;379x542&quot;,
              &quot;recordTypeLabel&quot;: &quot;image&quot;
            }
          }, {
            &quot;title&quot;: &quot;Davide Ribatti, 29 ★★★★★&quot;,
            &quot;description&quot;: &quot;&lt;p class=\&quot;\&quot; data-rte-preserve-empty=\&quot;true\&quot; style=\&quot;white-space:pre-wrap;\&quot;&gt;“Data la mia situazione di grande sovrappeso (101kg) e grande stanchezza fisica, mi sono affidato a Roman. È un percorso difficile ma fatto con la giusta guida e tutto in discesa. Oltre al grande cambiamento fisico ho notato un aumento esponenziale delle mie energie. Ho perso ben 20kg in circa 1 anno grazie alla mia determinazione e a Roman.”&lt;\/p&gt;&quot;,
            &quot;button&quot;: {
              &quot;buttonText&quot;: &quot;Leggi le biografie&quot;
            },
            &quot;imageAltText&quot;: &quot;&quot;,
            &quot;imageId&quot;: &quot;680f5d46380d43436033d1fc&quot;,
            &quot;image&quot;: {
              &quot;id&quot;: &quot;680f5d46380d43436033d1fc&quot;,
              &quot;recordType&quot;: 2,
              &quot;addedOn&quot;: 1745837382387,
              &quot;updatedOn&quot;: 1745837382461,
              &quot;workflowState&quot;: 1,
              &quot;publishOn&quot;: 1745837382387,
              &quot;authorId&quot;: &quot;5963fc0e414fb5c08934f4ef&quot;,
              &quot;systemDataId&quot;: &quot;cb705ec1-8fac-4d3b-bbf9-03c49fd18c0f&quot;,
              &quot;systemDataVariants&quot;: &quot;383x382,100w,300w&quot;,
              &quot;systemDataSourceType&quot;: &quot;PNG&quot;,
              &quot;filename&quot;: &quot;ribatti.png&quot;,
              &quot;mediaFocalPoint&quot;: {
                &quot;x&quot;: 0.5,
                &quot;y&quot;: 0.5,
                &quot;source&quot;: 3
              },
              &quot;colorData&quot;: {
                &quot;topLeftAverage&quot;: &quot;566662&quot;,
                &quot;topRightAverage&quot;: &quot;bbcbd8&quot;,
                &quot;bottomLeftAverage&quot;: &quot;555642&quot;,
                &quot;bottomRightAverage&quot;: &quot;8a919d&quot;,
                &quot;centerAverage&quot;: &quot;c0b8aa&quot;,
                &quot;suggestedBgColor&quot;: &quot;7b806d&quot;
              },
              &quot;urlId&quot;: &quot;48ki6wdcv75w6yp8x60nbp3jlwn8as&quot;,
              &quot;title&quot;: &quot;&quot;,
              &quot;body&quot;: null,
              &quot;likeCount&quot;: 0,
              &quot;commentCount&quot;: 0,
              &quot;publicCommentCount&quot;: 0,
              &quot;commentState&quot;: 2,
              &quot;unsaved&quot;: false,
              &quot;author&quot;: {
                &quot;id&quot;: &quot;5963fc0e414fb5c08934f4ef&quot;,
                &quot;displayName&quot;: &quot;Roman Fontana&quot;,
                &quot;firstName&quot;: &quot;Fabio&quot;,
                &quot;lastName&quot;: &quot;Bertozzi&quot;
              },
              &quot;assetUrl&quot;: &quot;assets/images/ribatti.png&quot;,
              &quot;contentType&quot;: &quot;image/png&quot;,
              &quot;items&quot;: [ ],
              &quot;pushedServices&quot;: { },
              &quot;pendingPushedServices&quot;: { },
              &quot;originalSize&quot;: &quot;383x382&quot;,
              &quot;recordTypeLabel&quot;: &quot;image&quot;
            }
          }, {
            &quot;title&quot;: &quot;Roberto Cardinali, 61 ★★★★★&quot;,
            &quot;description&quot;: &quot;&lt;p class=\&quot;\&quot; data-rte-preserve-empty=\&quot;true\&quot; style=\&quot;white-space:pre-wrap;\&quot;&gt;“Ho conosciuto Roman a gennaio del 2019, me ne ha parlato un amico, suo cliente, che gli si stava trovando molto bene. Lui pesava circa 100 chili e mi sentivo davvero male, non stavo bene con me stesso. In Roman non ho trovato solo un medico, ma una persona disponibile pronta a capire i miei problemi e le mie esigenze. Ho avuto momenti di alti e bassi e soprattutto nei momenti negativi ho sempre avuto un valido supporto, una spinta a continuare ad andare avanti. A giugno 2020 ho raggiunto il peso di 83 chili che fino a quel momento non mi ero nemmeno sentito addosso. Adesso mi sento molto meglio con me stesso ed è davvero un gran risultato.”&lt;\/p&gt;&quot;,
            &quot;button&quot;: {
              &quot;buttonText&quot;: &quot;Leggi le biografie&quot;
            },
            &quot;imageAltText&quot;: &quot;&quot;,
            &quot;imageId&quot;: &quot;680f5d5f647983315c1891d0&quot;,
            &quot;image&quot;: {
              &quot;id&quot;: &quot;680f5d5f647983315c1891d0&quot;,
              &quot;recordType&quot;: 2,
              &quot;addedOn&quot;: 1745837407986,
              &quot;updatedOn&quot;: 1745837408055,
              &quot;workflowState&quot;: 1,
              &quot;publishOn&quot;: 1745837407986,
              &quot;authorId&quot;: &quot;5963fc0e414fb5c08934f4ef&quot;,
              &quot;systemDataId&quot;: &quot;79600870-4162-4378-b28b-fcdcb2fcad07&quot;,
              &quot;systemDataVariants&quot;: &quot;606x352,100w,300w,500w&quot;,
              &quot;systemDataSourceType&quot;: &quot;PNG&quot;,
              &quot;filename&quot;: &quot;cardinali.png&quot;,
              &quot;mediaFocalPoint&quot;: {
                &quot;x&quot;: 0.5,
                &quot;y&quot;: 0.5,
                &quot;source&quot;: 3
              },
              &quot;colorData&quot;: {
                &quot;topLeftAverage&quot;: &quot;d7b173&quot;,
                &quot;topRightAverage&quot;: &quot;1d0812&quot;,
                &quot;bottomLeftAverage&quot;: &quot;c19e7a&quot;,
                &quot;bottomRightAverage&quot;: &quot;57196d&quot;,
                &quot;centerAverage&quot;: &quot;a396ad&quot;,
                &quot;suggestedBgColor&quot;: &quot;806258&quot;
              },
              &quot;urlId&quot;: &quot;6mvfq0rmkec0bpfynsacrjfbuflxy9&quot;,
              &quot;title&quot;: &quot;&quot;,
              &quot;body&quot;: null,
              &quot;likeCount&quot;: 0,
              &quot;commentCount&quot;: 0,
              &quot;publicCommentCount&quot;: 0,
              &quot;commentState&quot;: 2,
              &quot;unsaved&quot;: false,
              &quot;author&quot;: {
                &quot;id&quot;: &quot;5963fc0e414fb5c08934f4ef&quot;,
                &quot;displayName&quot;: &quot;Roman Fontana&quot;,
                &quot;firstName&quot;: &quot;Fabio&quot;,
                &quot;lastName&quot;: &quot;Bertozzi&quot;
              },
              &quot;assetUrl&quot;: &quot;assets/images/cardinali.png&quot;,
              &quot;contentType&quot;: &quot;image/png&quot;,
              &quot;items&quot;: [ ],
              &quot;pushedServices&quot;: { },
              &quot;pendingPushedServices&quot;: { },
              &quot;originalSize&quot;: &quot;606x352&quot;,
              &quot;recordTypeLabel&quot;: &quot;image&quot;
            }
          }, {
            &quot;title&quot;: &quot;Giuseppe del prete, 24 ★★★★★&quot;,
            &quot;description&quot;: &quot;&lt;p data-rte-preserve-empty=\&quot;true\&quot; style=\&quot;white-space:pre-wrap;\&quot;&gt;“Mi chiamo Giuseppe e ho iniziato il mio percorso un anno fa, partendo da un peso di 91kg, ad oggi il mio peso attuale è di 72kg. Il mio Percorso è stato parecchio ostacolato dalle problematiche dovute a questo periodo di COVID. Però con una buona strategia, costanza e voglia di migliorarsi siamo arrivati ad un risultato degno di nota. Il dr.Fontana è stato un ottimo alleato in questo percorso elaborando una strategia su misura per me e mi ha sostenuto con l'assistenza tramite whatsapp per qualsiasi dubbio oppure una rivalutazione alimentare. In complessivo non abbiamo migliorato solo il peso in questo lasso di tempo ma anche la ritenzione idrica e la qualità di cibo che mangio in modo da avere un maggiore senso di sazietà. Insomma iniziare questo percorso oltre a farmi cambiare il fisico ha cambiato anche il mio rapporto con il cibo!”&lt;\/p&gt;&quot;,
            &quot;button&quot;: {
              &quot;buttonText&quot;: &quot;Clicca qui&quot;,
              &quot;buttonLink&quot;: &quot;/&quot;
            },
            &quot;imageId&quot;: &quot;680f5d70886c7a386c6668f1&quot;,
            &quot;imageAltText&quot;: &quot;Immagine composta da due foto: a sinistra, un uomo senza maglietta in acqua, sorridente; a destra, lo stesso uomo senza maglietta, in posa con braccia sollevate e muscolose, indossando jeans, in un ambiente domestico.&quot;,
            &quot;image&quot;: {
              &quot;id&quot;: &quot;680f5d70886c7a386c6668f1&quot;,
              &quot;recordType&quot;: 2,
              &quot;addedOn&quot;: 1745837424508,
              &quot;updatedOn&quot;: 1745837424611,
              &quot;workflowState&quot;: 1,
              &quot;publishOn&quot;: 1745837424508,
              &quot;authorId&quot;: &quot;5963fc0e414fb5c08934f4ef&quot;,
              &quot;systemDataId&quot;: &quot;1fff062e-6f9f-4e65-ba2d-34ab2b66de96&quot;,
              &quot;systemDataVariants&quot;: &quot;566x441,100w,300w,500w&quot;,
              &quot;systemDataSourceType&quot;: &quot;PNG&quot;,
              &quot;filename&quot;: &quot;del-prete.png&quot;,
              &quot;mediaFocalPoint&quot;: {
                &quot;x&quot;: 0.5,
                &quot;y&quot;: 0.5,
                &quot;source&quot;: 3
              },
              &quot;colorData&quot;: {
                &quot;topLeftAverage&quot;: &quot;3a3834&quot;,
                &quot;topRightAverage&quot;: &quot;6a5f49&quot;,
                &quot;bottomLeftAverage&quot;: &quot;31adf4&quot;,
                &quot;bottomRightAverage&quot;: &quot;48473f&quot;,
                &quot;centerAverage&quot;: &quot;cfcdc7&quot;,
                &quot;suggestedBgColor&quot;: &quot;838583&quot;
              },
              &quot;urlId&quot;: &quot;rguro1fcw82r0hdy44d4e9rc13czcr&quot;,
              &quot;title&quot;: &quot;&quot;,
              &quot;body&quot;: null,
              &quot;likeCount&quot;: 0,
              &quot;commentCount&quot;: 0,
              &quot;publicCommentCount&quot;: 0,
              &quot;commentState&quot;: 2,
              &quot;unsaved&quot;: false,
              &quot;author&quot;: {
                &quot;id&quot;: &quot;5963fc0e414fb5c08934f4ef&quot;,
                &quot;displayName&quot;: &quot;Roman Fontana&quot;,
                &quot;firstName&quot;: &quot;Roman&quot;,
                &quot;lastName&quot;: &quot;Fontana&quot;
              },
              &quot;assetUrl&quot;: &quot;assets/images/del-prete.png&quot;,
              &quot;contentType&quot;: &quot;image/png&quot;,
              &quot;items&quot;: [ ],
              &quot;pushedServices&quot;: { },
              &quot;pendingPushedServices&quot;: { },
              &quot;originalSize&quot;: &quot;566x441&quot;,
              &quot;recordTypeLabel&quot;: &quot;image&quot;
            }
          } ],
          &quot;styles&quot;: {
            &quot;imageFocalPoint&quot;: {
              &quot;x&quot;: 0.5,
              &quot;y&quot;: 0.5
            },
            &quot;imageOverlayOpacity&quot;: 0.3,
            &quot;backgroundColor&quot;: &quot;white&quot;,
            &quot;sectionTheme&quot;: &quot;white&quot;,
            &quot;imageEffect&quot;: &quot;none&quot;,
            &quot;backgroundMode&quot;: &quot;image&quot;,
            &quot;backgroundImage&quot;: null
          },
          &quot;video&quot;: {
            &quot;filter&quot;: 1,
            &quot;videoFallbackContentItem&quot;: null,
            &quot;nativeVideoContentItem&quot;: null,
            &quot;videoSourceProvider&quot;: &quot;none&quot;
          },
          &quot;backgroundImageFocalPoint&quot;: null,
          &quot;backgroundImageId&quot;: null,
          &quot;options&quot;: {
            &quot;maxColumns&quot;: 3,
            &quot;isCardEnabled&quot;: false,
            &quot;isMediaEnabled&quot;: true,
            &quot;isTitleEnabled&quot;: true,
            &quot;isBodyEnabled&quot;: true,
            &quot;isButtonEnabled&quot;: false,
            &quot;isShowAdjacentSlides&quot;: true,
            &quot;isInfiniteEnabled&quot;: true,
            &quot;isAutoplayEnabled&quot;: false,
            &quot;slideDurationMs&quot;: 1000,
            &quot;mediaAspectRatio&quot;: &quot;4:3&quot;,
            &quot;layoutWidth&quot;: &quot;full&quot;,
            &quot;mediaWidth&quot;: {
              &quot;value&quot;: 100,
              &quot;unit&quot;: &quot;%&quot;
            },
            &quot;mediaAlignment&quot;: &quot;left&quot;,
            &quot;contentWidth&quot;: {
              &quot;value&quot;: 75,
              &quot;unit&quot;: &quot;%&quot;
            },
            &quot;titleAlignment&quot;: &quot;center&quot;,
            &quot;bodyAlignment&quot;: &quot;center&quot;,
            &quot;buttonAlignment&quot;: &quot;center&quot;,
            &quot;titlePlacement&quot;: &quot;center&quot;,
            &quot;bodyPlacement&quot;: &quot;center&quot;,
            &quot;buttonPlacement&quot;: &quot;center&quot;,
            &quot;cardVerticalAlignment&quot;: &quot;top&quot;,
            &quot;contentOrder&quot;: &quot;media-first&quot;,
            &quot;titleFontSize&quot;: &quot;heading-2&quot;,
            &quot;bodyFontSize&quot;: &quot;paragraph-2&quot;,
            &quot;buttonFontSize&quot;: &quot;button-medium&quot;,
            &quot;customOptions&quot;: {
              &quot;customTitleFontSize&quot;: {
                &quot;value&quot;: 1.2,
                &quot;unit&quot;: &quot;rem&quot;
              },
              &quot;customBodyFontSize&quot;: {
                &quot;value&quot;: 0.9,
                &quot;unit&quot;: &quot;rem&quot;
              },
              &quot;customButtonFontSize&quot;: {
                &quot;value&quot;: 0.8,
                &quot;unit&quot;: &quot;rem&quot;
              }
            },
            &quot;verticalPaddingTop&quot;: {
              &quot;value&quot;: 3.3,
              &quot;unit&quot;: &quot;vmax&quot;
            },
            &quot;verticalPaddingBottom&quot;: {
              &quot;value&quot;: 3.3,
              &quot;unit&quot;: &quot;vmax&quot;
            },
            &quot;spaceBetweenSlides&quot;: {
              &quot;value&quot;: 20,
              &quot;unit&quot;: &quot;px&quot;
            },
            &quot;spaceBetweenContentAndMedia&quot;: {
              &quot;value&quot;: 4,
              &quot;unit&quot;: &quot;%&quot;
            },
            &quot;spaceBelowTitle&quot;: {
              &quot;value&quot;: 1,
              &quot;unit&quot;: &quot;%&quot;
            },
            &quot;spaceBelowBody&quot;: {
              &quot;value&quot;: 4,
              &quot;unit&quot;: &quot;%&quot;
            },
            &quot;cardPaddingTop&quot;: {
              &quot;value&quot;: 20,
              &quot;unit&quot;: &quot;px&quot;
            },
            &quot;cardPaddingRight&quot;: {
              &quot;value&quot;: 20,
              &quot;unit&quot;: &quot;px&quot;
            },
            &quot;cardPaddingBottom&quot;: {
              &quot;value&quot;: 20,
              &quot;unit&quot;: &quot;px&quot;
            },
            &quot;cardPaddingLeft&quot;: {
              &quot;value&quot;: 20,
              &quot;unit&quot;: &quot;px&quot;
            },
            &quot;navigationOffset&quot;: {
              &quot;value&quot;: 3,
              &quot;unit&quot;: &quot;vw&quot;
            },
            &quot;navigationControls&quot;: &quot;arrows&quot;,
            &quot;navigationPlacement&quot;: &quot;bottom&quot;,
            &quot;navigationAlignment&quot;: &quot;center&quot;,
            &quot;spaceAboveNavigation&quot;: {
              &quot;value&quot;: 50,
              &quot;unit&quot;: &quot;px&quot;
            },
            &quot;progressIndicators&quot;: &quot;bars&quot;
          },
          &quot;layout&quot;: &quot;carousel&quot;,
          &quot;isSectionTitleEnabled&quot;: true,
          &quot;sectionTitle&quot;: &quot;&lt;p class=\&quot;\&quot; data-rte-preserve-empty=\&quot;true\&quot; style=\&quot;white-space:pre-wrap;\&quot;&gt;Le mie storie di successo:&lt;\/p&gt;&quot;,
          &quot;spaceBelowSectionTitle&quot;: {
            &quot;value&quot;: 40,
            &quot;unit&quot;: &quot;px&quot;
          },
          &quot;sectionTitleAlignment&quot;: &quot;center&quot;,
          &quot;isSectionButtonEnabled&quot;: false,
          &quot;sectionButton&quot;: {
            &quot;buttonText&quot;: &quot;Realizzalo&quot;,
            &quot;buttonLink&quot;: &quot;#&quot;,
            &quot;buttonNewWindow&quot;: false
          },
          &quot;sectionButtonSize&quot;: &quot;large&quot;,
          &quot;sectionButtonAlignment&quot;: &quot;center&quot;,
          &quot;spaceAboveSectionButton&quot;: {
            &quot;value&quot;: 40,
            &quot;unit&quot;: &quot;px&quot;
          }
        }"
  data-media-alignment="left"
  data-title-alignment="center"
  data-body-alignment="center"
  data-button-alignment="center"
  data-title-placement="center"
  data-body-placement="center"
  data-button-placement="center"
  data-layout-width="full"
  data-title-font-unit="rem"
  data-description-font-unit="rem"
  data-button-font-unit="rem"
  data-is-media-enabled="true"
  data-is-card-enabled="false"
  data-media-width-value="100"
  data-media-width-unit="%"
  data-content-order="media-first"
  data-space-between-columns=""
  data-vertical-padding-top-value="3.3"
  data-vertical-padding-bottom-value="3.3"
  data-vertical-padding-top-unit="vmax"
  data-vertical-padding-bottom-unit="vmax"
>
  <div
    class="user-items-list-carousel__gutter"
    role="region"
    aria-label="Carosello"
  >
    <div class="user-items-list-carousel__slideshow-holder">
      <div class="user-items-list-carousel__slides-revealer">
        <ul class="user-items-list-carousel__slides" style="grid-gap: 20px;">
            <li class="
                  user-items-list-carousel__slide list-item
                "
                style="
              "
              data-is-card-enabled="false"
            >
                    <div class="user-items-list-carousel__media-container"
                      style="
                        margin-bottom: 4%;
                        width: 100%;
                      "
                    >
                      <div class="user-items-list-carousel__media-inner"
                        data-media-aspect-ratio="4:3"
                        data-animation-role="image"
                      >
                        <img
                          data-image-focal-point=","
                          alt=""
                          data-src="assets/images/DIELISE.png" data-image="assets/images/DIELISE.png" data-image-dimensions="717x481" data-image-focal-point="0.5,0.5" alt="DIELISE.png" 
                          class="user-items-list-carousel__media"
                          data-load="false"
                          data-mode="cover"
                          data-use-advanced-positioning="true"
                          elementtiming="nbf-list-carousel"
                        />
                      </div>
                    </div>
              <div class="list-item-content">
                  <div class="list-item-content__text-wrapper">
                      <h2
                        class="list-item-content__title"
                        style="max-width: 75%;"
                      >Dielise Rossetti, 43 ★★★★★</h2>
                        <div class="list-item-content__description
                          "
                          style="
                            margin-top: 1%;
                            max-width: 75%;
                          "
                        ><p class="" data-rte-preserve-empty="true" style="white-space:pre-wrap;">“Ho seguito molte diete da quelle più classiche a quelle più rivoluzionarie ma sempre con esito negativo. Nel tempo ho conosciuto il Dott. Roman Fontana che ha realizzato una dieta compatibile sia con le mie preferenze alimentari e sia per il fabbisogno del mio fisico, prendendo in considerazione ogni aspetto della mia vita. Nel giro di un anno da 72kg sono arrivata a 57kg, perdendo una media di 20cm in tutto il corpo. Tutto questo mantenendo un equilibrio tra massa magra e massa grassa. Consiglio assolutamente i metodi e la professionalità del Dott. Roman Fontana!!!”</p></div>
                  </div>
              </div>
            </li>
            <li class="
                  user-items-list-carousel__slide list-item
                "
                style="
              "
              data-is-card-enabled="false"
            >
                    <div class="user-items-list-carousel__media-container"
                      style="
                        margin-bottom: 4%;
                        width: 100%;
                      "
                    >
                      <div class="user-items-list-carousel__media-inner"
                        data-media-aspect-ratio="4:3"
                        data-animation-role="image"
                      >
                        <img
                          data-image-focal-point=","
                          alt=""
                          data-src="assets/images/davezza.png" data-image="assets/images/davezza.png" data-image-dimensions="504x502" data-image-focal-point="0.5,0.5" alt="davezza.png" 
                          class="user-items-list-carousel__media"
                          data-load="false"
                          data-mode="cover"
                          data-use-advanced-positioning="true"
                          elementtiming="nbf-list-carousel"
                        />
                      </div>
                    </div>
              <div class="list-item-content">
                  <div class="list-item-content__text-wrapper">
                      <h2
                        class="list-item-content__title"
                        style="max-width: 75%;"
                      >Laura Davezza, 54 ★★★★★</h2>
                        <div class="list-item-content__description
                          "
                          style="
                            margin-top: 1%;
                            max-width: 75%;
                          "
                        ><p class="" data-rte-preserve-empty="true" style="white-space:pre-wrap;">“Con un peso iniziale di 109 kg, ho deciso di intraprendere un percorso annuale per avere un supporto costante. Nei primi tre mesi ho perso 20 kg, grazie a una dieta su misura per i miei gusti e abitudini, con un pasto libero settimanale e piccoli adattamenti nel tempo. A Natale avevo già perso oltre 30 kg, e poter entrare in un negozio normale per comprare un paio di jeans è stata una conquista incredibile. Non solo il corpo ne ha tratto beneficio, ma anche la mia autostima e il controllo sul mio corpo sono migliorati enormemente. A giugno, con 45 kg persi in un anno, ho raggiunto il peso di 64 kg. Oggi, a distanza di due anni, mantengo il mio peso senza seguire la dieta, avendo imparato a capire cosa il mio corpo richiede. I sacrifici ci sono, ma vengono ripagati dai risultati. Cambiare abitudini e conoscere il proprio corpo sono la chiave per vivere in armonia con sé stessi.”</p></div>
                  </div>
              </div>
            </li>
            <li class="
                  user-items-list-carousel__slide list-item
                "
                style="
              "
              data-is-card-enabled="false"
            >
                    <div class="user-items-list-carousel__media-container"
                      style="
                        margin-bottom: 4%;
                        width: 100%;
                      "
                    >
                      <div class="user-items-list-carousel__media-inner"
                        data-media-aspect-ratio="4:3"
                        data-animation-role="image"
                      >
                        <img
                          data-image-focal-point=","
                          alt=""
                          data-src="assets/images/marchegiani.png" data-image="assets/images/marchegiani.png" data-image-dimensions="379x542" data-image-focal-point="0.5,0.5" alt="marchegiani.png" 
                          class="user-items-list-carousel__media"
                          data-load="false"
                          data-mode="cover"
                          data-use-advanced-positioning="true"
                          elementtiming="nbf-list-carousel"
                        />
                      </div>
                    </div>
              <div class="list-item-content">
                  <div class="list-item-content__text-wrapper">
                      <h2
                        class="list-item-content__title"
                        style="max-width: 75%;"
                      >Enrico Marchegiani, 43 ★★★★★</h2>
                        <div class="list-item-content__description
                          "
                          style="
                            margin-top: 1%;
                            max-width: 75%;
                          "
                        ><p data-rte-preserve-empty="true" style="white-space:pre-wrap;">“Quando ho cominciato pesavo 106.8 Kg. Volevo un cambio di marcia a livello fisico, venivo da un infortunio al ginocchio e sentivo la necessità di alleggerire l'articolazione per muovermi meglio. Sono arrivato ad un minimo storico di 80 kg (a 15 anni pesavo così) seguendo Roman, complice il lockdown che ha impossibilitato le solite birre e calici di vino con gli amici. Le mie difficoltà nel seguire la dieta? La fame prima di cena e la mia impossibilità di tenere altro cibo che non appartenesse alla dieta dentro casa, in particolare nel periodo più ferreo. Cosa mi ha aiutato? Il mio coinquilino che faceva la dieta come la mia (anche simile nei pasti in certi periodi). Sono un amante della montagna e la vera differenza la sento proprio lì. Mi sembra di camminare per i sentieri con una ferrari sotto il sedere rispetto a prima.”</p></div>
                  </div>
              </div>
            </li>
            <li class="
                  user-items-list-carousel__slide list-item
                "
                style="
              "
              data-is-card-enabled="false"
            >
                    <div class="user-items-list-carousel__media-container"
                      style="
                        margin-bottom: 4%;
                        width: 100%;
                      "
                    >
                      <div class="user-items-list-carousel__media-inner"
                        data-media-aspect-ratio="4:3"
                        data-animation-role="image"
                      >
                        <img
                          data-image-focal-point=","
                          alt=""
                          data-src="assets/images/ribatti.png" data-image="assets/images/ribatti.png" data-image-dimensions="383x382" data-image-focal-point="0.5,0.5" alt="ribatti.png" 
                          class="user-items-list-carousel__media"
                          data-load="false"
                          data-mode="cover"
                          data-use-advanced-positioning="true"
                          elementtiming="nbf-list-carousel"
                        />
                      </div>
                    </div>
              <div class="list-item-content">
                  <div class="list-item-content__text-wrapper">
                      <h2
                        class="list-item-content__title"
                        style="max-width: 75%;"
                      >Davide Ribatti, 29 ★★★★★</h2>
                        <div class="list-item-content__description
                          "
                          style="
                            margin-top: 1%;
                            max-width: 75%;
                          "
                        ><p class="" data-rte-preserve-empty="true" style="white-space:pre-wrap;">“Data la mia situazione di grande sovrappeso (101kg) e grande stanchezza fisica, mi sono affidato a Roman. È un percorso difficile ma fatto con la giusta guida e tutto in discesa. Oltre al grande cambiamento fisico ho notato un aumento esponenziale delle mie energie. Ho perso ben 20kg in circa 1 anno grazie alla mia determinazione e a Roman.”</p></div>
                  </div>
              </div>
            </li>
            <li class="
                  user-items-list-carousel__slide list-item
                "
                style="
              "
              data-is-card-enabled="false"
            >
                    <div class="user-items-list-carousel__media-container"
                      style="
                        margin-bottom: 4%;
                        width: 100%;
                      "
                    >
                      <div class="user-items-list-carousel__media-inner"
                        data-media-aspect-ratio="4:3"
                        data-animation-role="image"
                      >
                        <img
                          data-image-focal-point=","
                          alt=""
                          data-src="assets/images/cardinali.png" data-image="assets/images/cardinali.png" data-image-dimensions="606x352" data-image-focal-point="0.5,0.5" alt="cardinali.png" 
                          class="user-items-list-carousel__media"
                          data-load="false"
                          data-mode="cover"
                          data-use-advanced-positioning="true"
                          elementtiming="nbf-list-carousel"
                        />
                      </div>
                    </div>
              <div class="list-item-content">
                  <div class="list-item-content__text-wrapper">
                      <h2
                        class="list-item-content__title"
                        style="max-width: 75%;"
                      >Roberto Cardinali, 61 ★★★★★</h2>
                        <div class="list-item-content__description
                          "
                          style="
                            margin-top: 1%;
                            max-width: 75%;
                          "
                        ><p class="" data-rte-preserve-empty="true" style="white-space:pre-wrap;">“Ho conosciuto Roman a gennaio del 2019, me ne ha parlato un amico, suo cliente, che gli si stava trovando molto bene. Lui pesava circa 100 chili e mi sentivo davvero male, non stavo bene con me stesso. In Roman non ho trovato solo un medico, ma una persona disponibile pronta a capire i miei problemi e le mie esigenze. Ho avuto momenti di alti e bassi e soprattutto nei momenti negativi ho sempre avuto un valido supporto, una spinta a continuare ad andare avanti. A giugno 2020 ho raggiunto il peso di 83 chili che fino a quel momento non mi ero nemmeno sentito addosso. Adesso mi sento molto meglio con me stesso ed è davvero un gran risultato.”</p></div>
                  </div>
              </div>
            </li>
            <li class="
                  user-items-list-carousel__slide list-item
                "
                style="
              "
              data-is-card-enabled="false"
            >
                    <div class="user-items-list-carousel__media-container"
                      style="
                        margin-bottom: 4%;
                        width: 100%;
                      "
                    >
                      <div class="user-items-list-carousel__media-inner"
                        data-media-aspect-ratio="4:3"
                        data-animation-role="image"
                      >
                        <img
                          data-image-focal-point=","
                          alt="Immagine composta da due foto: a sinistra, un uomo senza maglietta in acqua, sorridente; a destra, lo stesso uomo senza maglietta, in posa con braccia sollevate e muscolose, indossando jeans, in un ambiente domestico."
                          data-src="assets/images/del-prete.png" data-image="assets/images/del-prete.png" data-image-dimensions="566x441" data-image-focal-point="0.5,0.5" alt="del-prete.png" 
                          class="user-items-list-carousel__media"
                          data-load="false"
                          data-mode="cover"
                          data-use-advanced-positioning="true"
                          elementtiming="nbf-list-carousel"
                        />
                      </div>
                    </div>
              <div class="list-item-content">
                  <div class="list-item-content__text-wrapper">
                      <h2
                        class="list-item-content__title"
                        style="max-width: 75%;"
                      >Giuseppe del prete, 24 ★★★★★</h2>
                        <div class="list-item-content__description
                          "
                          style="
                            margin-top: 1%;
                            max-width: 75%;
                          "
                        ><p data-rte-preserve-empty="true" style="white-space:pre-wrap;">“Mi chiamo Giuseppe e ho iniziato il mio percorso un anno fa, partendo da un peso di 91kg, ad oggi il mio peso attuale è di 72kg. Il mio Percorso è stato parecchio ostacolato dalle problematiche dovute a questo periodo di COVID. Però con una buona strategia, costanza e voglia di migliorarsi siamo arrivati ad un risultato degno di nota. Il dr.Fontana è stato un ottimo alleato in questo percorso elaborando una strategia su misura per me e mi ha sostenuto con l'assistenza tramite whatsapp per qualsiasi dubbio oppure una rivalutazione alimentare. In complessivo non abbiamo migliorato solo il peso in questo lasso di tempo ma anche la ritenzione idrica e la qualità di cibo che mangio in modo da avere un maggiore senso di sazietà. Insomma iniziare questo percorso oltre a farmi cambiare il fisico ha cambiato anche il mio rapporto con il cibo!”</p></div>
                  </div>
              </div>
            </li>
        </ul>
      </div>
    </div>
    <div class="mobile-arrows">
      <button
  class="
    mobile-arrow-button
    mobile-arrow-button--left
  "
  aria-label="
      Indietro
  "
  data-animation-role="button"
>
  <div class="
    user-items-list-carousel__arrow-icon-background
    mobile-arrow-icon-background-area
  ">
  </div>
  <svg class="mobile-arrow-icon" viewBox="0 0 24 14" xmlns="http://www.w3.org/2000/svg">
      <path class="user-items-list-carousel__arrow-icon-foreground
        mobile-arrow-icon-path"
        d="M7.87012 13L2.00021 7L7.87012 1" stroke-width="2" fill="none"
      />
      <path class="user-items-list-carousel__arrow-icon-foreground
        mobile-arrow-icon-path"
        d="M22.9653 7L3.03948 7" stroke-width="2" stroke-linecap="square" fill="none"
      />
  </svg>
</button>
      <button
  class="
    mobile-arrow-button
    mobile-arrow-button--right
  "
  aria-label="
      Avanti
  "
  data-animation-role="button"
>
  <div class="
    user-items-list-carousel__arrow-icon-background
    mobile-arrow-icon-background-area
  ">
  </div>
  <svg class="mobile-arrow-icon" viewBox="0 0 24 14" xmlns="http://www.w3.org/2000/svg">
      <path class="user-items-list-carousel__arrow-icon-foreground
        mobile-arrow-icon-path"
        d="M16.1299 1L21.9998 7L16.1299 13" stroke-width="2" fill="none"
      />
      <path class="user-items-list-carousel__arrow-icon-foreground
        mobile-arrow-icon-path"
        d="M1.03472 7H20.9605" stroke-width="2" stroke-linecap="square" fill="none"
      />
  </svg>
</button>
    </div>
  </div>
      <div
        class="desktop-arrows arrows-bottom-wrapper"
        style="margin-top: 50px;"
      >
        <div class="arrows-bottom">
          <button
  class="
    user-items-list-carousel__arrow-button
    user-items-list-carousel__arrow-button--left
  "
  aria-label="
      Indietro
  "
  data-animation-role="button"
>
  <div class="
    user-items-list-carousel__arrow-icon-background
    user-items-list-carousel__arrow-icon-background-area
  ">
  </div>
  <svg class="user-items-list-carousel__arrow-icon" viewBox="0 0 44 18" xmlns="http://www.w3.org/2000/svg">
      <path class="user-items-list-carousel__arrow-icon-foreground
        user-items-list-carousel__arrow-icon-path"
        d="M9.90649 16.96L2.1221 9.17556L9.9065 1.39116"
      />
      <path class="user-items-list-carousel__arrow-icon-foreground
        user-items-list-carousel__arrow-icon-path"
        d="M42.8633 9.18125L3.37868 9.18125"
      />
  </svg>
</button>
          <button
  class="
    user-items-list-carousel__arrow-button
    user-items-list-carousel__arrow-button--right
  "
  aria-label="
      Avanti
  "
  data-animation-role="button"
>
  <div class="
    user-items-list-carousel__arrow-icon-background
    user-items-list-carousel__arrow-icon-background-area
  ">
  </div>
  <svg class="user-items-list-carousel__arrow-icon" viewBox="0 0 44 18" xmlns="http://www.w3.org/2000/svg">
      <path class="user-items-list-carousel__arrow-icon-foreground
        user-items-list-carousel__arrow-icon-path"
        d="M34.1477 1.39111L41.9321 9.17551L34.1477 16.9599"
      />
      <path class="user-items-list-carousel__arrow-icon-foreground
        user-items-list-carousel__arrow-icon-path"
        d="M1.19088 9.16982H40.6755"
      />
  </svg>
</button>
        </div>
      </div>
</div>
</div>
    </div>
  </div>
</section>
<section
  data-test="page-section"
  data-section-theme=""
  class='page-section 
      full-bleed-section
      layout-engine-section
    background-width--full-bleed
        section-height--custom
      content-width--wide
    horizontal-alignment--center
    vertical-alignment--middle
    '
  data-section-id="67fce56b90360a7ace149772"
  data-controller="SectionWrapperController"
  data-current-styles="{
          &quot;imageOverlayOpacity&quot;: 0.15,
          &quot;backgroundWidth&quot;: &quot;background-width--full-bleed&quot;,
          &quot;sectionHeight&quot;: &quot;section-height--custom&quot;,
          &quot;customSectionHeight&quot;: 0,
          &quot;horizontalAlignment&quot;: &quot;horizontal-alignment--center&quot;,
          &quot;verticalAlignment&quot;: &quot;vertical-alignment--middle&quot;,
          &quot;contentWidth&quot;: &quot;content-width--wide&quot;,
          &quot;customContentWidth&quot;: 50,
          &quot;sectionTheme&quot;: &quot;&quot;,
          &quot;sectionAnimation&quot;: &quot;none&quot;,
          &quot;backgroundMode&quot;: &quot;image&quot;
        }"
  data-current-context="{
          &quot;video&quot;: {
            &quot;playbackSpeed&quot;: 0.5,
            &quot;filter&quot;: 2,
            &quot;filterStrength&quot;: 0,
            &quot;zoom&quot;: 0,
            &quot;videoSourceProvider&quot;: &quot;none&quot;
          },
          &quot;backgroundImageId&quot;: null,
          &quot;backgroundMediaEffect&quot;: null,
          &quot;divider&quot;: null,
          &quot;typeName&quot;: &quot;page&quot;
        }"
  data-animation="none"
  data-fluid-engine-section
>
  <div
    class="section-border"
  >
    <div class="section-background">
    </div>
  </div>
  <div
    class='content-wrapper'
    style='
          padding-top: calc(0vmax / 10); padding-bottom: calc(0vmax / 10);
    '
  >
    <div
      class="content"
    >
      <div data-fluid-engine="true"><style>
.fe-67fce56b90360a7ace149771 {
  --grid-gutter: calc(var(--sqs-mobile-site-gutter, 6vw) - 11.0px);
  --cell-max-width: calc( ( var(--sqs-site-max-width, 1500px) - (11.0px * (8 - 1)) ) / 8 );
  display: grid;
  position: relative;
  grid-area: 1/1/-1/-1;
  grid-template-rows: repeat(43,minmax(24px, auto));
  grid-template-columns:
    minmax(var(--grid-gutter), 1fr)
    repeat(8, minmax(0, var(--cell-max-width)))
    minmax(var(--grid-gutter), 1fr);
  row-gap: 11.0px;
  column-gap: 11.0px;
}
@media (min-width: 768px) {
  .background-width--inset .fe-67fce56b90360a7ace149771 {
    --inset-padding: calc(var(--sqs-site-gutter) * 2);
  }
  .fe-67fce56b90360a7ace149771 {
    --grid-gutter: calc(var(--sqs-site-gutter, 4vw) - 11.0px);
    --cell-max-width: calc( ( var(--sqs-site-max-width, 1500px) - (11.0px * (24 - 1)) ) / 24 );
    --inset-padding: 0vw;
    --row-height-scaling-factor: 0.0215;
    --container-width: min(var(--sqs-site-max-width, 1500px), calc(100vw - var(--sqs-site-gutter, 4vw) * 2 - var(--inset-padding) ));
    grid-template-rows: repeat(30,minmax(calc(var(--container-width) * var(--row-height-scaling-factor)), auto));
    grid-template-columns:
      minmax(var(--grid-gutter), 1fr)
      repeat(24, minmax(0, var(--cell-max-width)))
      minmax(var(--grid-gutter), 1fr);
  }
}
  .fe-block-8f1792b937127a1df53f {
    grid-area: 2/2/11/10;
    z-index: 0;
    @media (max-width: 767px) {
    }
  }
  .fe-block-8f1792b937127a1df53f .sqs-block {
    justify-content: center;
  }
  .fe-block-8f1792b937127a1df53f .sqs-block-alignment-wrapper {
    align-items: center;
  }
  @media (min-width: 768px) {
    .fe-block-8f1792b937127a1df53f {
      grid-area: 1/1/30/14;
      z-index: 0;
    }
    .fe-block-8f1792b937127a1df53f .sqs-block {
      justify-content: center;
    }
    .fe-block-8f1792b937127a1df53f .sqs-block-alignment-wrapper {
      align-items: center;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_199244 {
    grid-area: 15/2/28/10;
    z-index: 4;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_199244 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_199244 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_199244 {
      grid-area: 8/16/17/26;
      z-index: 4;
    }
    .fe-block-yui_3_17_2_1_1744615607231_199244 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_199244 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_198821 {
    grid-area: 11/2/17/10;
    z-index: 3;
    @media (max-width: 767px) {
    }
  }
  .fe-block-yui_3_17_2_1_1744615607231_198821 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-yui_3_17_2_1_1744615607231_198821 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-yui_3_17_2_1_1744615607231_198821 {
      grid-area: 3/16/8/26;
      z-index: 3;
    }
    .fe-block-yui_3_17_2_1_1744615607231_198821 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-yui_3_17_2_1_1744615607231_198821 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
  .fe-block-ac307cc9bb7dddc14435 {
    grid-area: 28/2/42/10;
    z-index: 2;
    @media (max-width: 767px) {
    }
  }
  .fe-block-ac307cc9bb7dddc14435 .sqs-block {
    justify-content: flex-start;
  }
  .fe-block-ac307cc9bb7dddc14435 .sqs-block-alignment-wrapper {
    align-items: flex-start;
  }
  @media (min-width: 768px) {
    .fe-block-ac307cc9bb7dddc14435 {
      grid-area: 18/16/31/26;
      z-index: 2;
    }
    .fe-block-ac307cc9bb7dddc14435 .sqs-block {
      justify-content: flex-start;
    }
    .fe-block-ac307cc9bb7dddc14435 .sqs-block-alignment-wrapper {
      align-items: flex-start;
    }
  }
</style><div class="fluid-engine fe-67fce56b90360a7ace149771"><div class="fe-block fe-block-8f1792b937127a1df53f"><div class="sqs-block image-block sqs-block-image sqs-stretched" data-aspect-ratio="100.73206442166911" data-block-type="5" id="block-8f1792b937127a1df53f"><div class="sqs-block-content">
    <div
      class="
        image-block-outer-wrapper
        layout-caption-below
        design-layout-fluid
        image-position-left
        combination-animation-none
        individual-animation-none
      "
      data-test="image-block-fluid-outer-wrapper"
    >
      <div
        class="fluid-image-animation-wrapper sqs-image sqs-block-alignment-wrapper"
        data-animation-role="image"
      >
        <div
          class="fluid-image-container sqs-image-content"
          style="overflow: hidden;-webkit-mask-image: -webkit-radial-gradient(white, black);position: relative;width: 100%;height: 100%;"
        >
              <div class="content-fill">
            <img data-stretch="true" data-src="assets/images/romansorride.jpg" data-image="assets/images/romansorride.jpg" data-image-dimensions="1440x1920" data-image-focal-point="0.5,0.5" alt="Uomo sorridente con capelli ricci, barba, vestito da medico con camice bianco, seduto a una scrivania in un ufficio bianco." data-load="false" elementtiming="system-image-block" src="assets/images/romansorride.jpg" width="1440" height="1920" alt="" sizes="100vw" style="display:block;object-fit: cover; object-position: 50% 50%" srcset="assets/images/romansorride.jpg?format=100w 100w, assets/images/romansorride.jpg?format=300w 300w, assets/images/romansorride.jpg?format=500w 500w, assets/images/romansorride.jpg?format=750w 750w, assets/images/romansorride.jpg?format=1000w 1000w, assets/images/romansorride.jpg?format=1500w 1500w, assets/images/romansorride.jpg?format=2500w 2500w" loading="lazy" decoding="async" data-loader="sqs">
            <div class="fluidImageOverlay"></div>
              </div>
        </div>
      </div>
    </div>
    <style>
      .sqs-block-image .sqs-block-content {
        height: 100%;
        width: 100%;
      }
        .fe-block-8f1792b937127a1df53f .fluidImageOverlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          mix-blend-mode: normal;
            opacity: 0;
        }
    </style>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_199244"><div class="sqs-block html-block sqs-block-html" data-blend-mode="NORMAL" data-block-type="2" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-yui_3_17_2_1_1744615607231_199244"><div class="sqs-block-content">
<div class="sqs-html-content">
  <h4 style="white-space:pre-wrap;">Contattami a questi recapiti:</h4><p class="sqsrte-large" style="white-space:pre-wrap;">Tel. +39 ************</p><p class="sqsrte-large" style="white-space:pre-wrap;">Email: <a href="mailto:<EMAIL>?subject=Sono%20pronto%20sensei!" target="_blank"><EMAIL></a></p><p class="sqsrte-large" style="white-space:pre-wrap;">Studio: <a href="https://www.google.com/maps/search/?api=1&amp;query=Via+Maestri+del+Lavoro+17,+60033,+Chiaravalle+(AN)" target="_blank">Via Maestri del Lavoro 17, 60033, Chiaravalle (AN)</a></p><p class="sqsrte-large" style="white-space:pre-wrap;">Studio: <a href="https://www.google.com/maps/search/?api=1&amp;query=Via+S.+Martino+25C,+60131,+Ancona+(AN)" target="_blank">Via S. Martino 25C, 60131, Ancona (AN)</a></p><p class="sqsrte-large" style="white-space:pre-wrap;">Studio: <a href="https://www.google.com/maps/search/?api=1&amp;query=Via+Che+Guevara+79/B,+60022,+Castelfidardo+(AN)" target="_blank">Via Che Guevara 79/B, 60022, Castelfidardo (AN)</a></p><p class="sqsrte-large" style="white-space:pre-wrap;"><strong>Oppure scrivimi direttamente qui:</strong></p>
</div>
</div></div></div><div class="fe-block fe-block-yui_3_17_2_1_1744615607231_198821"><div class="sqs-block code-block sqs-block-code" data-block-type="23" id="block-yui_3_17_2_1_1744615607231_198821"><div class="sqs-block-content"><h1 class="responsive-title-sezione-nutrizione">
Sei pronto a iniziare?
</h1>
<style>
  .responsive-title-sezione-nutrizione {
    font-family: 'EB Garamond', serif;
    font-size: 72px;
    font-weight: 600;
    font-style: normal;
    margin: 0;
    text-align:left;
    color:#6E7D58 ;
  }
  @media screen and (max-width: 600px) {
    .responsive-title-sezione-nutrizione {
      font-size: 52px;
    }
  }
</style>
</div></div></div><div class="fe-block fe-block-ac307cc9bb7dddc14435"><div class="sqs-block form-block sqs-block-form" data-blend-mode="NORMAL" data-block-type="9" data-border-radii="&#123;&quot;topLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;topRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomLeft&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;,&quot;bottomRight&quot;:&#123;&quot;unit&quot;:&quot;px&quot;,&quot;value&quot;:0.0&#125;&#125;" id="block-ac307cc9bb7dddc14435"><div class="sqs-block-content">
  <script type="application/json" id="form-context-67fce56b90360a7ace14976f" class="sqs-form-block-context">{"secureUrl":"https://fabiobertozzi.squarespace.com","formSubmitButtonText":"Invia messaggio","formSubmissionMessage":{"html":"Grazie!"},"submissionVerticalAlignment":"","buttonAlignment":"left","submissionMessageTextStyle":"","formName":"Nuovo modulo","collectionId":"67fcb8c434e8564dcabc023a","submissionAnimation":"","captchaTheme":1,"formId":"67fce56b90360a7ace14976f","useLightbox":false,"firstFieldHighlightType":"","successRedirect":"","useFormsJs":true,"submissionTextAlignment":"","formFieldFormats":{"initialPhoneFormat":{"id":0,"type":"PHONE_NUMBER","country":"IT","labelLocale":"it-IT","fields":[{"type":"FIELD","label":"1","identifier":"1","length":3,"required":false,"metadata":{}},{"type":"SEPARATOR","label":" ","identifier":"Space","length":0,"required":false,"metadata":{}},{"type":"FIELD","label":"2","identifier":"2","length":17,"required":false,"metadata":{}}]},"initialNameOrder":"GIVEN_FIRST","initialAddressFormat":{"id":0,"type":"ADDRESS","country":"IT","labelLocale":"it","fields":[{"type":"FIELD","label":"Indirizzo riga 1","identifier":"Line1","length":0,"required":true,"metadata":{"autocomplete":"address-line1"}},{"type":"SEPARATOR","label":"\n","identifier":"Newline","length":0,"required":false,"metadata":{}},{"type":"FIELD","label":"Indirizzo riga 2","identifier":"Line2","length":0,"required":false,"metadata":{"autocomplete":"address-line2"}},{"type":"SEPARATOR","label":"\n","identifier":"Newline","length":0,"required":false,"metadata":{}},{"type":"FIELD","label":"Codice di avviamento postale","identifier":"Zip","length":0,"required":true,"metadata":{"autocomplete":"postal-code"}},{"type":"SEPARATOR","label":" ","identifier":"Space","length":0,"required":false,"metadata":{}},{"type":"FIELD","label":"Città","identifier":"City","length":0,"required":true,"metadata":{"autocomplete":"address-level2"}},{"type":"SEPARATOR","label":" ","identifier":"Space","length":0,"required":false,"metadata":{}},{"type":"FIELD","label":"Provincia","identifier":"State","length":0,"required":true,"metadata":{"autocomplete":"address-level1"}}]},"countries":[{"name":"Afghanistan","code":"AF","phoneCode":"+93"},{"name":"Albania","code":"AL","phoneCode":"+355"},{"name":"Algeria","code":"DZ","phoneCode":"+213"},{"name":"Andorra","code":"AD","phoneCode":"+376"},{"name":"Angola","code":"AO","phoneCode":"+244"},{"name":"Anguilla","code":"AI","phoneCode":"+1"},{"name":"Antigua e Barbuda","code":"AG","phoneCode":"+1"},{"name":"Arabia Saudita","code":"SA","phoneCode":"+966"},{"name":"Argentina","code":"AR","phoneCode":"+54"},{"name":"Armenia","code":"AM","phoneCode":"+374"},{"name":"Aruba","code":"AW","phoneCode":"+297"},{"name":"Australia","code":"AU","phoneCode":"+61"},{"name":"Austria","code":"AT","phoneCode":"+43"},{"name":"Azerbaigian","code":"AZ","phoneCode":"+994"},{"name":"Bahamas","code":"BS","phoneCode":"+1"},{"name":"Bahrein","code":"BH","phoneCode":"+973"},{"name":"Bangladesh","code":"BD","phoneCode":"+880"},{"name":"Barbados","code":"BB","phoneCode":"+1"},{"name":"Belgio","code":"BE","phoneCode":"+32"},{"name":"Belize","code":"BZ","phoneCode":"+501"},{"name":"Benin","code":"BJ","phoneCode":"+229"},{"name":"Bermuda","code":"BM","phoneCode":"+1"},{"name":"Bhutan","code":"BT","phoneCode":"+975"},{"name":"Bielorussia","code":"BY","phoneCode":"+375"},{"name":"Bolivia","code":"BO","phoneCode":"+591"},{"name":"Bosnia ed Erzegovina","code":"BA","phoneCode":"+387"},{"name":"Botswana","code":"BW","phoneCode":"+267"},{"name":"Brasile","code":"BR","phoneCode":"+55"},{"name":"Brunei","code":"BN","phoneCode":"+673"},{"name":"Bulgaria","code":"BG","phoneCode":"+359"},{"name":"Burkina Faso","code":"BF","phoneCode":"+226"},{"name":"Burundi","code":"BI","phoneCode":"+257"},{"name":"Cambogia","code":"KH","phoneCode":"+855"},{"name":"Camerun","code":"CM","phoneCode":"+237"},{"name":"Canada","code":"CA","phoneCode":"+1"},{"name":"Capo Verde","code":"CV","phoneCode":"+238"},{"name":"Caraibi olandesi","code":"BQ","phoneCode":"+599"},{"name":"Cechia","code":"CZ","phoneCode":"+420"},{"name":"Ciad","code":"TD","phoneCode":"+235"},{"name":"Cile","code":"CL","phoneCode":"+56"},{"name":"Cina","code":"CN","phoneCode":"+86"},{"name":"Cipro","code":"CY","phoneCode":"+357"},{"name":"Città del Vaticano","code":"VA","phoneCode":"+39"},{"name":"Colombia","code":"CO","phoneCode":"+57"},{"name":"Comore","code":"KM","phoneCode":"+269"},{"name":"Congo-Brazzaville","code":"CG","phoneCode":"+242"},{"name":"Congo - Kinshasa","code":"CD","phoneCode":"+243"},{"name":"Corea del Nord","code":"KP","phoneCode":"+850"},{"name":"Corea del Sud","code":"KR","phoneCode":"+82"},{"name":"Costa d’Avorio","code":"CI","phoneCode":"+225"},{"name":"Costa Rica","code":"CR","phoneCode":"+506"},{"name":"Croazia","code":"HR","phoneCode":"+385"},{"name":"Cuba","code":"CU","phoneCode":"+53"},{"name":"Curaçao","code":"CW","phoneCode":"+599"},{"name":"Danimarca","code":"DK","phoneCode":"+45"},{"name":"Dominica","code":"DM","phoneCode":"+1"},{"name":"Ecuador","code":"EC","phoneCode":"+593"},{"name":"Egitto","code":"EG","phoneCode":"+20"},{"name":"El Salvador","code":"SV","phoneCode":"+503"},{"name":"Emirati Arabi Uniti","code":"AE","phoneCode":"+971"},{"name":"Eritrea","code":"ER","phoneCode":"+291"},{"name":"Estonia","code":"EE","phoneCode":"+372"},{"name":"Eswatini","code":"SZ","phoneCode":"+268"},{"name":"Etiopia","code":"ET","phoneCode":"+251"},{"name":"Figi","code":"FJ","phoneCode":"+679"},{"name":"Filippine","code":"PH","phoneCode":"+63"},{"name":"Finlandia","code":"FI","phoneCode":"+358"},{"name":"Francia","code":"FR","phoneCode":"+33"},{"name":"Gabon","code":"GA","phoneCode":"+241"},{"name":"Gambia","code":"GM","phoneCode":"+220"},{"name":"Georgia","code":"GE","phoneCode":"+995"},{"name":"Germania","code":"DE","phoneCode":"+49"},{"name":"Ghana","code":"GH","phoneCode":"+233"},{"name":"Giamaica","code":"JM","phoneCode":"+1"},{"name":"Giappone","code":"JP","phoneCode":"+81"},{"name":"Gibilterra","code":"GI","phoneCode":"+350"},{"name":"Gibuti","code":"DJ","phoneCode":"+253"},{"name":"Giordania","code":"JO","phoneCode":"+962"},{"name":"Grecia","code":"GR","phoneCode":"+30"},{"name":"Grenada","code":"GD","phoneCode":"+1"},{"name":"Groenlandia","code":"GL","phoneCode":"+299"},{"name":"Guadalupa","code":"GP","phoneCode":"+590"},{"name":"Guam","code":"GU","phoneCode":"+1"},{"name":"Guatemala","code":"GT","phoneCode":"+502"},{"name":"Guernsey","code":"GG","phoneCode":"+44"},{"name":"Guinea","code":"GN","phoneCode":"+224"},{"name":"Guinea-Bissau","code":"GW","phoneCode":"+245"},{"name":"Guinea Equatoriale","code":"GQ","phoneCode":"+240"},{"name":"Guyana","code":"GY","phoneCode":"+592"},{"name":"Guyana Francese","code":"GF","phoneCode":"+594"},{"name":"Haiti","code":"HT","phoneCode":"+509"},{"name":"Honduras","code":"HN","phoneCode":"+504"},{"name":"India","code":"IN","phoneCode":"+91"},{"name":"Indonesia","code":"ID","phoneCode":"+62"},{"name":"Iran","code":"IR","phoneCode":"+98"},{"name":"Iraq","code":"IQ","phoneCode":"+964"},{"name":"Irlanda","code":"IE","phoneCode":"+353"},{"name":"Islanda","code":"IS","phoneCode":"+354"},{"name":"Isola Ascensione","code":"AC","phoneCode":"+247"},{"name":"Isola Christmas","code":"CX","phoneCode":"+61"},{"name":"Isola di Man","code":"IM","phoneCode":"+44"},{"name":"Isola Norfolk","code":"NF","phoneCode":"+672"},{"name":"Isole Åland","code":"AX","phoneCode":"+358"},{"name":"Isole Cayman","code":"KY","phoneCode":"+1"},{"name":"Isole Cocos (Keeling)","code":"CC","phoneCode":"+61"},{"name":"Isole Cook","code":"CK","phoneCode":"+682"},{"name":"Isole Fær Øer","code":"FO","phoneCode":"+298"},{"name":"Isole Falkland","code":"FK","phoneCode":"+500"},{"name":"Isole Marianne settentrionali","code":"MP","phoneCode":"+1"},{"name":"Isole Marshall","code":"MH","phoneCode":"+692"},{"name":"Isole Salomone","code":"SB","phoneCode":"+677"},{"name":"Isole Turks e Caicos","code":"TC","phoneCode":"+1"},{"name":"Isole Vergini Americane","code":"VI","phoneCode":"+1"},{"name":"Isole Vergini Britanniche","code":"VG","phoneCode":"+1"},{"name":"Israele","code":"IL","phoneCode":"+972"},{"name":"Italia","code":"IT","phoneCode":"+39"},{"name":"Jersey","code":"JE","phoneCode":"+44"},{"name":"Kazakistan","code":"KZ","phoneCode":"+7"},{"name":"Kenya","code":"KE","phoneCode":"+254"},{"name":"Kirghizistan","code":"KG","phoneCode":"+996"},{"name":"Kiribati","code":"KI","phoneCode":"+686"},{"name":"Kosovo","code":"XK","phoneCode":"+383"},{"name":"Kuwait","code":"KW","phoneCode":"+965"},{"name":"Laos","code":"LA","phoneCode":"+856"},{"name":"Lesotho","code":"LS","phoneCode":"+266"},{"name":"Lettonia","code":"LV","phoneCode":"+371"},{"name":"Libano","code":"LB","phoneCode":"+961"},{"name":"Liberia","code":"LR","phoneCode":"+231"},{"name":"Libia","code":"LY","phoneCode":"+218"},{"name":"Liechtenstein","code":"LI","phoneCode":"+423"},{"name":"Lituania","code":"LT","phoneCode":"+370"},{"name":"Lussemburgo","code":"LU","phoneCode":"+352"},{"name":"Macedonia del Nord","code":"MK","phoneCode":"+389"},{"name":"Madagascar","code":"MG","phoneCode":"+261"},{"name":"Malawi","code":"MW","phoneCode":"+265"},{"name":"Malaysia","code":"MY","phoneCode":"+60"},{"name":"Maldive","code":"MV","phoneCode":"+960"},{"name":"Mali","code":"ML","phoneCode":"+223"},{"name":"Malta","code":"MT","phoneCode":"+356"},{"name":"Marocco","code":"MA","phoneCode":"+212"},{"name":"Martinica","code":"MQ","phoneCode":"+596"},{"name":"Mauritania","code":"MR","phoneCode":"+222"},{"name":"Mauritius","code":"MU","phoneCode":"+230"},{"name":"Mayotte","code":"YT","phoneCode":"+262"},{"name":"Messico","code":"MX","phoneCode":"+52"},{"name":"Micronesia","code":"FM","phoneCode":"+691"},{"name":"Moldavia","code":"MD","phoneCode":"+373"},{"name":"Monaco","code":"MC","phoneCode":"+377"},{"name":"Mongolia","code":"MN","phoneCode":"+976"},{"name":"Montenegro","code":"ME","phoneCode":"+382"},{"name":"Montserrat","code":"MS","phoneCode":"+1"},{"name":"Mozambico","code":"MZ","phoneCode":"+258"},{"name":"Myanmar (Birmania)","code":"MM","phoneCode":"+95"},{"name":"Namibia","code":"NA","phoneCode":"+264"},{"name":"Nauru","code":"NR","phoneCode":"+674"},{"name":"Nepal","code":"NP","phoneCode":"+977"},{"name":"Nicaragua","code":"NI","phoneCode":"+505"},{"name":"Niger","code":"NE","phoneCode":"+227"},{"name":"Nigeria","code":"NG","phoneCode":"+234"},{"name":"Niue","code":"NU","phoneCode":"+683"},{"name":"Norvegia","code":"NO","phoneCode":"+47"},{"name":"Nuova Caledonia","code":"NC","phoneCode":"+687"},{"name":"Nuova Zelanda","code":"NZ","phoneCode":"+64"},{"name":"Oman","code":"OM","phoneCode":"+968"},{"name":"Paesi Bassi","code":"NL","phoneCode":"+31"},{"name":"Pakistan","code":"PK","phoneCode":"+92"},{"name":"Palau","code":"PW","phoneCode":"+680"},{"name":"Panama","code":"PA","phoneCode":"+507"},{"name":"Papua Nuova Guinea","code":"PG","phoneCode":"+675"},{"name":"Paraguay","code":"PY","phoneCode":"+595"},{"name":"Perù","code":"PE","phoneCode":"+51"},{"name":"Polinesia francese","code":"PF","phoneCode":"+689"},{"name":"Polonia","code":"PL","phoneCode":"+48"},{"name":"Portogallo","code":"PT","phoneCode":"+351"},{"name":"Portorico","code":"PR","phoneCode":"+1"},{"name":"Qatar","code":"QA","phoneCode":"+974"},{"name":"RAS di Hong Kong","code":"HK","phoneCode":"+852"},{"name":"RAS di Macao","code":"MO","phoneCode":"+853"},{"name":"Regno Unito","code":"GB","phoneCode":"+44"},{"name":"Repubblica Centrafricana","code":"CF","phoneCode":"+236"},{"name":"Repubblica Dominicana","code":"DO","phoneCode":"+1"},{"name":"Riunione","code":"RE","phoneCode":"+262"},{"name":"Romania","code":"RO","phoneCode":"+40"},{"name":"Ruanda","code":"RW","phoneCode":"+250"},{"name":"Russia","code":"RU","phoneCode":"+7"},{"name":"Sahara occidentale","code":"EH","phoneCode":"+212"},{"name":"Saint-Barthélemy","code":"BL","phoneCode":"+590"},{"name":"Saint Kitts e Nevis","code":"KN","phoneCode":"+1"},{"name":"Saint Lucia","code":"LC","phoneCode":"+1"},{"name":"Saint Martin","code":"MF","phoneCode":"+590"},{"name":"Saint-Pierre e Miquelon","code":"PM","phoneCode":"+508"},{"name":"Saint Vincent e Grenadine","code":"VC","phoneCode":"+1"},{"name":"Samoa","code":"WS","phoneCode":"+685"},{"name":"Samoa americane","code":"AS","phoneCode":"+1"},{"name":"San Marino","code":"SM","phoneCode":"+378"},{"name":"Sant’Elena","code":"SH","phoneCode":"+290"},{"name":"São Tomé e Príncipe","code":"ST","phoneCode":"+239"},{"name":"Senegal","code":"SN","phoneCode":"+221"},{"name":"Serbia","code":"RS","phoneCode":"+381"},{"name":"Seychelles","code":"SC","phoneCode":"+248"},{"name":"Sierra Leone","code":"SL","phoneCode":"+232"},{"name":"Singapore","code":"SG","phoneCode":"+65"},{"name":"Sint Maarten","code":"SX","phoneCode":"+1"},{"name":"Siria","code":"SY","phoneCode":"+963"},{"name":"Slovacchia","code":"SK","phoneCode":"+421"},{"name":"Slovenia","code":"SI","phoneCode":"+386"},{"name":"Somalia","code":"SO","phoneCode":"+252"},{"name":"Spagna","code":"ES","phoneCode":"+34"},{"name":"Sri Lanka","code":"LK","phoneCode":"+94"},{"name":"Stati Uniti","code":"US","phoneCode":"+1"},{"name":"Sudafrica","code":"ZA","phoneCode":"+27"},{"name":"Sudan","code":"SD","phoneCode":"+249"},{"name":"Sud Sudan","code":"SS","phoneCode":"+211"},{"name":"Suriname","code":"SR","phoneCode":"+597"},{"name":"Svalbard e Jan Mayen","code":"SJ","phoneCode":"+47"},{"name":"Svezia","code":"SE","phoneCode":"+46"},{"name":"Svizzera","code":"CH","phoneCode":"+41"},{"name":"Tagikistan","code":"TJ","phoneCode":"+992"},{"name":"Taiwan","code":"TW","phoneCode":"+886"},{"name":"Tanzania","code":"TZ","phoneCode":"+255"},{"name":"Territorio britannico dell’Oceano Indiano","code":"IO","phoneCode":"+246"},{"name":"Territori palestinesi","code":"PS","phoneCode":"+970"},{"name":"Thailandia","code":"TH","phoneCode":"+66"},{"name":"Timor Est","code":"TL","phoneCode":"+670"},{"name":"Togo","code":"TG","phoneCode":"+228"},{"name":"Tokelau","code":"TK","phoneCode":"+690"},{"name":"Tonga","code":"TO","phoneCode":"+676"},{"name":"Trinidad e Tobago","code":"TT","phoneCode":"+1"},{"name":"Tristan da Cunha","code":"TA","phoneCode":"+290"},{"name":"Tunisia","code":"TN","phoneCode":"+216"},{"name":"Turchia","code":"TR","phoneCode":"+90"},{"name":"Turkmenistan","code":"TM","phoneCode":"+993"},{"name":"Tuvalu","code":"TV","phoneCode":"+688"},{"name":"Ucraina","code":"UA","phoneCode":"+380"},{"name":"Uganda","code":"UG","phoneCode":"+256"},{"name":"Ungheria","code":"HU","phoneCode":"+36"},{"name":"Uruguay","code":"UY","phoneCode":"+598"},{"name":"Uzbekistan","code":"UZ","phoneCode":"+998"},{"name":"Vanuatu","code":"VU","phoneCode":"+678"},{"name":"Venezuela","code":"VE","phoneCode":"+58"},{"name":"Vietnam","code":"VN","phoneCode":"+84"},{"name":"Wallis e Futuna","code":"WF","phoneCode":"+681"},{"name":"Yemen","code":"YE","phoneCode":"+967"},{"name":"Zambia","code":"ZM","phoneCode":"+260"},{"name":"Zimbabwe","code":"ZW","phoneCode":"+263"}]},"showTitle":false,"captchaEnabled":true,"buttonVariant":"secondary","objectName":"ac307cc9bb7dddc14435","disclaimerMessage":{"html":""},"formFields":[{"type":"name","id":"name-yui_3_17_2_1_1656615088309_7771","locked":false,"title":"Nome","description":"","required":true,"name":true},{"type":"email","id":"email-yui_3_17_2_1_1656615088309_7772","locked":false,"title":"E-mail","description":"","placeholder":"","required":true,"email":true},{"type":"textarea","id":"textarea-yui_3_17_2_1_1656615088309_7774","locked":false,"title":"Messaggio","description":"","placeholder":"","required":true,"textarea":true}],"captchaAlignment":1,"lightboxHandleText":"","localizedStrings":{"validation":{"noValidSelection":"Seleziona un elemento valido.","invalidUrl":"Deve essere un URL valido.","stringTooLong":"Il valore deve avere un massimo di {0} caratteri.","containsInvalidKey":"{0} contiene un valore non valido.","invalidTwitterUsername":"Inserire un nome utente Twitter corretto.","valueOutsideRange":"Il valore deve essere compreso tra {0} e {1}.","invalidPassword":"Le password non devono contenere spazi vuoti.","missingRequiredSubfields":"{0} mancano i campi secondari richiesti: {1}","invalidCurrency":"Il valore della valuta deve essere formattato come 1234 o 123.99.","invalidMapSize":"Il valore dovrebbe contenere {0} elementi.","subfieldsRequired":"Tutti i campi in {0} sono richiesti.","formSubmissionFailed":"Invio del modulo non riuscito. Controlla le seguenti informazioni: {0}.","invalidCountryCode":"Il prefisso deve avere un + e fino a 4 cifre.","invalidDate":"La data inserita non è corretta.","required":"{0} è richiesto.","invalidStringLength":"Il valore dovrebbe contenere {0} caratteri.","invalidEmail":"Gli indirizzi email devono seguire <NAME_EMAIL>.","invalidListLength":"Il valore deve contenere {0} elementi.","allEmpty":"Per favore completa almeno un campo del modulo.","missingRequiredQuestion":"Manca una domanda obbligatoria.","invalidQuestion":"Contiene una domanda non valida.","captchaFailure":"Convalida Captcha non riuscita. Riprova.","stringTooShort":"Il valore deve avere almeno {0} caratteri.","invalid":"{0} non è corretto.","formErrors":"Errori relativi al Modulo","containsInvalidValue":"{0} contiene un valore errato.","invalidUnsignedNumber":"I numeri possono solo contenere cifre e non altri caratteri.","invalidName":"Un nome valido può solo contenere lettere, numeri, spazi o caratteri."},"submit":"Invia","status":{"title":"Blocco {@}","learnMore":"Maggiori informazioni"},"name":{"firstName":"Nome","lastName":"Cognome"},"lightbox":{"openForm":"Apri modulo"},"likert":{"agree":"D'accordo","stronglyDisagree":"Fortemente in disaccordo","disagree":"In disaccordo","stronglyAgree":"Completamente d'accordo","neutral":"Neutrale"},"time":{"am":"di mattina","second":"Secondo","pm":"di sera","minute":"Minuto","amPm":"AM/PM","hour":"Ora"},"notFound":"Modulo non trovato.","date":{"yyyy":"AAAA","year":"Anno","mm":"MM","day":"della Terra","month":"Mese","dd":"GG"},"phone":{"country":"Paese","number":"Numero","prefix":"Prefisso","areaCode":"CAP","line":"Linea"},"submitError":"Non è possibile inviare il modulo. Riprova più tardi.","address":{"stateProvince":"Regione","country":"Paese","zipPostalCode":"CAP","address2":"Indirizzo 2","address1":"Indirizzo 1","city":"Città"},"email":{"signUp":"Iscriviti per ricevere aggiornamenti e restare sempre informato sulle ultime novità"},"cannotSubmitDemoForm":"Questo è un modulo dimostrativo e non può essere inviato.","required":"(obbligatorio)","invalidData":"Modulo non valido."}}</script>
  <div id="form-submission-html-67fce56b90360a7ace14976f" class="sqs-form-block-submission-html" data-submission-html=""></div>
<div class="form-wrapper"
>
  <div class="form-inner-wrapper"  hidden>
    <form
       data-form-id="67fce56b90360a7ace14976f"
       data-success-redirect=""
       data-dynamic-strings
       autocomplete="on"
       method="POST"
       action="https://fabiobertozzi.squarespace.com"
       novalidate
       onsubmit="return (function (form) {
  Y.use('squarespace-form-submit', 'node', function usingFormSubmit(Y) {
    (new Y.Squarespace.FormSubmit(form)).submit({
      formId: '67fce56b90360a7ace14976f',
      collectionId: '67fcb8c434e8564dcabc023a',
      objectName: 'ac307cc9bb7dddc14435'
    });
  });
  return false;
})(this);"
    >
        <div class="field-list clear">
              <fieldset id="name-yui_3_17_2_1_1656615088309_7771" class="form-item fields name required">
            <legend class="title">
              Nome
                <span class="required" aria-hidden="true">*</span>
            </legend>
                <div class="field first-name">
                  <label class="caption">
                    <input
                        class="field-element field-control"
                        name="fname"
                        x-autocompletetype="given-name"
                        type="text"
                        spellcheck="false"
                        maxlength="30"
                        data-title="First"
                        aria-required="true"
                    />
                    <span class="caption-text">Nome</span>
                  </label>
                </div>
                <div class="field last-name">
                  <label class="caption">
                    <input
                        class="field-element field-control"
                        name="lname"
                        x-autocompletetype="surname"
                        type="text"
                        spellcheck="false"
                        maxlength="30"
                        data-title="Last"
                        aria-required="true"
                    />
                    <span class="caption-text">Cognome</span>
                  </label>
                </div>
              </fieldset>
              <div id="email-yui_3_17_2_1_1656615088309_7772" class="form-item field email required">
            <label class="title" for="email-yui_3_17_2_1_1656615088309_7772-field">
              E-mail
                <span class="required" aria-hidden="true">*</span>
            </label>
                <input
                    class="field-element"
                    id="email-yui_3_17_2_1_1656615088309_7772-field"
                    name="email"
                    type="email"
                    autocomplete="email"
                    spellcheck="false"
                    aria-required="true"
                />
              </div>
              <div id="textarea-yui_3_17_2_1_1656615088309_7774" class="form-item field textarea required">
            <label class="title" for="textarea-yui_3_17_2_1_1656615088309_7774-field">
              Messaggio
                <span class="required" aria-hidden="true">*</span>
            </label>
                <textarea
                    class="field-element "
                    id="textarea-yui_3_17_2_1_1656615088309_7774-field"
                    aria-required="true"
                ></textarea>
              </div>
        </div>
      <div class="captcha-container
        align-left
        "
        data-theme="light"
      ></div>
      <div data-animation-role="button"
        class="
          form-button-wrapper
            form-button-wrapper--align-left
        "
      >
        <input
          class="button sqs-system-button sqs-editable-button sqs-button-element--primary"
          type="submit"
          value="Invia messaggio"
        />
      </div>
      <div class="hidden form-submission-text">Grazie!</div>
      <div class="hidden form-submission-html" data-submission-html=""></div>
    </form>
  </div>
</div>
</div></div></div></div></div>
    </div>
  </div>
</section>
</article>
      </main>
        <footer class="sections" id="footer-sections" data-footer-sections>
</footer>
    </div>
    <script defer="true" src="assets/js/site-bundle.js" type="text/javascript"></script>

<!--checkboxemail-->
<script>
// Funzione per cercare e selezionare il checkbox
function selectCheckbox() {
    let checkbox = null;
    // Prova diversi selettori
    checkbox = document.querySelector('.wloTXuBC0IMFtpfY') ||
               document.querySelector('input[type="checkbox"][value*="Iscriviti"]') ||
               document.querySelector('input[type="checkbox"]');
    if (checkbox && !checkbox.checked) {
        // Simula un vero click dell'utente
        checkbox.click();
        console.log('✅ Checkbox cliccato');
        return true;
    }
    return false;
}
// Funzione per forzare il checkbox prima dell'invio
function forceCheckboxChecked() {
    const checkbox = document.querySelector('.wloTXuBC0IMFtpfY') ||
                    document.querySelector('input[type="checkbox"][value*="Iscriviti"]') ||
                    document.querySelector('input[type="checkbox"]');
    if (checkbox && !checkbox.checked) {
        // Forza il valore e simula il click
        checkbox.checked = true;
        checkbox.click();
        console.log('🚀 Checkbox forzato prima invio - stato:', checkbox.checked);
    }
}
// Prova al caricamento della pagina
document.addEventListener('DOMContentLoaded', selectCheckbox);
setTimeout(selectCheckbox, 1000);
// Monitora i click sui pulsanti di invio SENZA intercettare il submit
document.addEventListener('click', function(e) {
    // Controlla se è un pulsante di invio
    if (e.target.type === 'submit' || 
        e.target.classList.contains('sqs-button-element--primary') ||
        e.target.closest('.sqs-button-element--primary') ||
        e.target.textContent.toLowerCase().includes('invia') ||
        e.target.textContent.toLowerCase().includes('send')) {
        console.log('🖱️ Click su pulsante invio');
        // Forza immediatamente il checkbox
        forceCheckboxChecked();
    }
}, true);
// Anche con mousedown per essere ancora più veloci
document.addEventListener('mousedown', function(e) {
    if (e.target.type === 'submit' || 
        e.target.classList.contains('sqs-button-element--primary') ||
        e.target.closest('.sqs-button-element--primary')) {
        console.log('🖱️ Mousedown su invio');
        forceCheckboxChecked();
    }
}, true);
// Monitora l'evento submit ma SENZA intercettarlo
document.addEventListener('submit', function(e) {
    console.log('📨 Submit in corso');
    forceCheckboxChecked();
    // Controlla lo stato finale
    const checkbox = document.querySelector('.wloTXuBC0IMFtpfY') ||
                    document.querySelector('input[type="checkbox"]');
    if (checkbox) {
        console.log('📋 Stato finale checkbox:', checkbox.checked);
    }
});
// Monitora per nuovi form
if (window.MutationObserver) {
    const observer = new MutationObserver(function(mutations) {
        let hasNewForm = false;
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length > 0) {
                for (let node of mutation.addedNodes) {
                    if (node.nodeType === 1 && (node.tagName === 'FORM' || node.querySelector('input[type="checkbox"]'))) {
                        hasNewForm = true;
                        break;
                    }
                }
            }
        });
        if (hasNewForm) {
            setTimeout(selectCheckbox, 300);
        }
    });
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}
// Controllo periodico leggero
setInterval(function() {
    const checkbox = document.querySelector('.wloTXuBC0IMFtpfY');
    if (checkbox && !checkbox.checked) {
        selectCheckbox();
    }
}, 15000);
</script>
  </body>
</html>